﻿<?xml version="1.0" encoding="utf-8"?>

<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage6"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <Grid VerticalOptions="Fill">

        <Image
            VerticalOptions="Fill"
            HorizontalOptions="Fill"
            Aspect="Fill"
            Source="tutorialblur4.png" />

        <ScrollView VerticalOptions="Fill">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="170" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="140" />
                </Grid.RowDefinitions>


                <Grid Grid.Row="0" Padding="10,0">


                </Grid>

                <Grid Grid.Row="1" Padding="10,0">

                    <StackLayout
                        Margin="10,-10,10,0"
                        Spacing="12">


                        <Grid
                            Margin="-3,50,0,0"
                            HorizontalOptions="Fill"
                            VerticalOptions="Start"
                            HeightRequest="217">
                            <Image
                                Aspect="Fill"
                                Source="tutorialneedtoworkcontainer.png" />

                            <Frame
                                HasShadow="False"
                                CornerRadius="15"
                                BackgroundColor="#F0F7FF"
                                Padding="0"
                                Margin="30">
                                <StackLayout
                                    Margin="20,10,20,0">

                                    <Label
                                        TextColor="{x:StaticResource ColorText}"
                                        FontSize="16"
                                        FontAttributes="Bold"
                                        HorizontalOptions="Start"
                                        VerticalOptions="Start"
                                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage6.Header}" />

                                    <StackLayout
                                        Spacing="10"
                                        Orientation="Horizontal"
                                        Margin="0,15,0,0">

                                        <Frame
                                            HasShadow="False"
                                            CornerRadius="20"
                                            BorderColor="{x:StaticResource ColorText}"
                                            BackgroundColor="Transparent"
                                            Padding="10">
                                            <Label
                                                MaxLines="1"
                                                LineBreakMode="NoWrap"
                                                TextColor="{x:StaticResource ColorText}"
                                                FontSize="14"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage6.SelfEsteem}" />
                                        </Frame>

                                        <Frame
                                            HasShadow="False"
                                            CornerRadius="20"
                                            BorderColor="{x:StaticResource ColorText}"
                                            BackgroundColor="Transparent"
                                            Padding="10">
                                            <Label
                                                MaxLines="1"
                                                LineBreakMode="NoWrap"
                                                TextColor="{x:StaticResource ColorText}"
                                                FontSize="14"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage6.Anxienty}" />
                                        </Frame>

                                        <Frame
                                            HasShadow="False"
                                            CornerRadius="20"
                                            BorderColor="{x:StaticResource ColorText}"
                                            BackgroundColor="Transparent"
                                            Padding="10">
                                            <Label
                                                MaxLines="1"
                                                LineBreakMode="NoWrap"
                                                TextColor="{x:StaticResource ColorText}"
                                                FontSize="14"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage6.Stress}" />
                                        </Frame>

                                    </StackLayout>

                                    <StackLayout
                                        Spacing="10"
                                        Orientation="Horizontal"
                                        Margin="0,2,0,0">

                                        <Frame
                                            HasShadow="False"
                                            CornerRadius="20"
                                            BorderColor="{x:StaticResource ColorText}"
                                            BackgroundColor="Transparent"
                                            Padding="10">
                                            <Label
                                                MaxLines="1"
                                                LineBreakMode="NoWrap"
                                                TextColor="{x:StaticResource ColorText}"
                                                FontSize="14"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage6.Emotions}" />
                                        </Frame>

                                        <Frame
                                            HasShadow="False"
                                            CornerRadius="20"
                                            BorderColor="{x:StaticResource ColorText}"
                                            BackgroundColor="Transparent"
                                            Padding="10">
                                            <Label
                                                MaxLines="1"
                                                LineBreakMode="NoWrap"
                                                TextColor="{x:StaticResource ColorText}"
                                                FontSize="14"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage6.SelfConfidence}" />
                                        </Frame>


                                    </StackLayout>

                                </StackLayout>

                            </Frame>

                        </Grid>


                        <Label
                            Margin="0,34,0,0"
                            TextColor="#000000"
                            FontAttributes="Bold"
                            FontSize="19"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="317"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage6.Header}" />
                        <Label
                            Margin="0,0,0,0"
                            TextColor="{x:StaticResource ColorText}"
                            FontSize="16"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="318"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage6.Description}" />


                    </StackLayout>

                </Grid>

                <Grid Grid.Row="2">
                    <Button
                        Command="{Binding Source={x:Reference this},Path=GoNext}"
                        VerticalOptions="Start"
                        HorizontalOptions="Fill"
                        Margin="63,0,63,0"
                        Style="{x:StaticResource yellow_btn}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage6.GoNext}" />

                </Grid>

            </Grid>
        </ScrollView>
    </Grid>
</ContentPage>