﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models;
using Triggero.Models.Practices;



namespace Triggero.Controls.Chat.AdviceAttachments
{

    public partial class TopicAdviceAttachment : ContentView
    {
        private Topic _topic;
        public TopicAdviceAttachment(Topic topic)
        {
            _topic = topic;
            InitializeComponent();

            Load();
        }

        private void Load()
        {
            titleLabel.Text = _topic.GetLocalizedTitle(LanguageHelper.LangCode);
            img.Source = Constants.BuildContentUrl(_topic.IconImgPath);
            minutesLabel.Text = _topic.PassingTimeInMinutes.ToString();
        }


        public event EventHandler<Topic> Clicked;
        private void onClick(object sender, EventArgs e)
        {
            Clicked?.Invoke(this, _topic);
        }
    }
}