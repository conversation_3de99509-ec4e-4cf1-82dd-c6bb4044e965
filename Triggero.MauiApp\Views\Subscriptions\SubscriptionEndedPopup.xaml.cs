﻿
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Views.Pages.Subscriptions;


namespace Triggero.MauiMobileApp.Views.Popups
{

    //NTPPup == төлөх хэрэгтэй попап
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SubscriptionEndedPopup : Popup
    {
        public SubscriptionEndedPopup()
        {
            InitializeComponent();
        }

        private RelayCommand close;
        public RelayCommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await this.CloseAsync();
            });
        }

        private RelayCommand goToSubscriptions;
        public RelayCommand GoToSubscriptions
        {
            get => goToSubscriptions ??= new RelayCommand(async obj =>
            {
                await this.CloseAsync();
                App.OpenNeedToPayNow();
            });
        }
    }
}