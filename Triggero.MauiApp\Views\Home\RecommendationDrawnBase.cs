﻿using AppoMobi.Maui.Gestures;
using DrawnUi.Draw;
using SkiaSharp;
using Triggero.Models;
using Triggero.Models.Practices;

namespace Triggero.MauiMobileApp.Views.Drawn
{
	public class RecommendationDrawnBase : SkiaLayout
	{
		protected RecommendationModel _recommendation;

		protected readonly IRecommendation _item;

		public RecommendationDrawnBase(RecommendationModel recommendation, IRecommendation item)
		{
			_recommendation = recommendation;
			_item = item;
		}

		public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args, GestureEventProcessingInfo apply)
		{
			if (args.Type == TouchActionResult.Tapped && Tapped != null)
			{
				Tapped?.Invoke(this, _recommendation);
				return this;
			}

			return base.ProcessGestures(args, apply);
		}

		public event EventHandler<RecommendationModel> Tapped;
	}
}
