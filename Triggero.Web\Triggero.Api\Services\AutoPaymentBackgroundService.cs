using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Triggero.Database;
using Triggero.Domain.Controllers.Rest;
using Triggero.Domain.Models;
using Triggero.Models.Enums;
using Triggero.Models.General;
using Yandex.Checkout.V3;

namespace Triggero.Api.Services;

public class AutoPaymentBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<AutoPaymentBackgroundService> _logger;
    private readonly TimeSpan _period = TimeSpan.FromHours(1); // Run every hour

    public AutoPaymentBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<AutoPaymentBackgroundService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("AutoPaymentBackgroundService started");

        // Initial delay before starting
        await Task.Delay(TimeSpan.FromSeconds(15), stoppingToken);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessAutoPayments(stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred in AutoPaymentBackgroundService");
            }

            // Wait for the next iteration
            try
            {
                await Task.Delay(_period, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
        }

        _logger.LogInformation("AutoPaymentBackgroundService stopped");
    }

    private async Task ProcessAutoPayments(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var config = scope.ServiceProvider.GetRequiredService<IConfiguration>();
        var db = scope.ServiceProvider.GetRequiredService<DatabaseContext>();

        var client = new Yandex.Checkout.V3.Client(
            shopId: config["Secrets:UKassa:ShopId"],
            secretKey: config["Secrets:UKassa:Key"]);

        var users = await db.Users
            .Include(o => o.UserSubscription)
            .ThenInclude(o => o.UserSubsctiptionOptions)
            .Where(o => !o.IsDeleted)
            .ToListAsync(cancellationToken);

        _logger.LogInformation("Processing auto payments for {UserCount} users", users.Count);

        foreach (var user in users)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            if (user.UserSubscription == null) continue;

            if (!user.UserSubscription.IsSubscriptionEnded
                || !user.UserSubscription.AllowRecurrent
                || string.IsNullOrEmpty(user.UserSubscription.SavedPaymentId))
            {
                continue;
            }

            try
            {
                var planOptionIds = user.UserSubscription.UserSubsctiptionOptions
                    .Select(option => option.PlanOptionId)
                    .ToList();

                var settings = new SubscriptionPaymentSettings
                {
                    Duration = user.UserSubscription.SubscriptionDuration,
                    IsBindingPayment = false,
                    SubType = user.UserSubscription.SubscriptionType,
                    planOptionIds = planOptionIds
                };

                var paymentInfo = await PaymentsController.PaySubscriptionYookassaAuto(db, client, user.Id, settings);
                
                _logger.LogInformation("Auto payment processed for user {UserId}: {PaymentId}", 
                    user.Id, paymentInfo?.PaymentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Auto payment failed for user {UserId}", user.Id);
            }

            // Small delay between users to avoid overwhelming the payment provider
            await Task.Delay(TimeSpan.FromSeconds(2), cancellationToken);
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("AutoPaymentBackgroundService is stopping");
        await base.StopAsync(cancellationToken);
    }
}
