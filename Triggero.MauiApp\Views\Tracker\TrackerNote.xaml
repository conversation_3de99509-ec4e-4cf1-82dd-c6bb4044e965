﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.Pages.MoodTracker.TrackerNote"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             BackgroundColor="#EEF6FD"
             x:Name="this">
    <ContentPage.Content>
        <Grid
            BackgroundColor="#EEF6FD">
            <Grid.RowDefinitions>
                <RowDefinition Height="75"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
 
            <Grid Grid.Row="0">
                
                <StackLayout
                    Margin="20,0,0,0"
                    HorizontalOptions="Start"
                    VerticalOptions="End"
                    Orientation="Horizontal">
                    <StackLayout.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=Close}"/>
                    </StackLayout.GestureRecognizers>
                    <ImageButton 
                        InputTransparent="True"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        Source="arrowbackaqua.png"
                        BackgroundColor="Transparent"
                        CornerRadius="0"
                        WidthRequest="8"
                        HeightRequest="16"/>
                    <Label 
                        TextColor="{x:StaticResource ColorPrimary}"
                        Margin="9,0,0,0"
                        FontSize="17"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerMainPage.Notes}"/>
                </StackLayout>

                <Label 
                    TextColor="{x:StaticResource ColorPrimary}"
                    Margin="0,0,20,0"
                    FontSize="17"
                    HorizontalOptions="End"
                    VerticalOptions="End"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerMainPage.Ready}">
                    <Label.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=SaveNote}"/>
                    </Label.GestureRecognizers>
                </Label>

            </Grid>

            <Grid Grid.Row="1">

                <Editor 
                    BackgroundColor="#EEF6FD"
                    Background="#EEF6FD"
                    PlaceholderColor="#989B9E"
                    TextColor="{x:StaticResource ColorText}"
                    Text="{Binding Source={x:Reference this},Path=Text,Mode=TwoWay}"
                    Placeholder="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerMainPage.EnterNote}"
                    Margin="20,0,20,-40"/>

             
            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>