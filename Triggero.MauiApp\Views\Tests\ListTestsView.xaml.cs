﻿
using System.Linq;
using System.Windows.Input;
using Triggero.MauiMobileApp.Services;
using Triggero.MauiMobileApp.ViewModels;
using Triggero.MauiMobileApp.Views.Pages;
using Triggero.Models.Tests;

namespace Triggero.MauiMobileApp.Views
{

    public partial class ListTestsView : ContentView, IDisposable
    {

        public void Dispose()
        {

            App.Messager.Unsubscribe(this, "FavChanged");
        }

        private void GoBackPls(object? sender, ControlTappedEventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                var mainPage = App.Current.MainPage.Navigation.NavigationStack.FirstOrDefault(o => o is MainPage) as MainPage;
                if (mainPage != null)
                {
                    mainPage.GoBack();
                }
            });
        }

        public ListTestsView(TestCategory testCategory)
        {
            _vm = new ListTestsViewModel(testCategory);

            InitializeComponent();

            pageBackNavigationGrid.Opacity = 1;
            arrowBackBtn.Opacity = 0;
            categoryLabelCollapsed.IsVisible = false;
            categoryLabel.Opacity = 1;
            categoryLabel.IsVisible = true;
            mainGridDef1.Height = new GridLength(110, GridUnitType.Absolute);

            BindingContext = _vm;

            App.Messager.Subscribe<FavChangedObject>(this, "FavChanged", async (sender, arg) =>
            {
                var existing = _vm.Items.FirstOrDefault(x => x.Id == arg.Id);
                if (existing != null)
                {
                    try
                    {
                        //for favorites to be refreshed.. just rebuild cells
                        var save = StackCells.BindingContext;
                        StackCells.BindingContext = null;
                        StackCells.BindingContext = save;
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }
                }
            });
        }

        #region Группировка

        private void filterByNewCheckedChanged(object? sender, bool b)
        {
            if (b)
            {
                _vm.CommandOrderA.Execute(true);
            }
        }
        private void filterByWatchesCheckedChanged(object? sender, bool b)
        {
            if (b)
            {
                _vm.CommandOrderB.Execute(true);
            }
        }

        #endregion

        private ICommand goBack;
        private ListTestsViewModel _vm;

        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj =>
            {
                var mainPage = App.Current.MainPage.Navigation.NavigationStack.FirstOrDefault(o => o is MainPage) as MainPage;
                if (mainPage != null)
                {
                    mainPage.GoBack();
                }
            });
        }


    }
}