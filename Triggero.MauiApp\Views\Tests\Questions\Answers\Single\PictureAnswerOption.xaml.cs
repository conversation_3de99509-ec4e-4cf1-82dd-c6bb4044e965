﻿using Triggero.MauiPort.Models;
using Triggero.Models.Abstractions;
using Triggero.Models.Tests.QuestionOptions;


namespace Triggero.Controls.Cards.Tests.Questions.Answers.Single
{

    public partial class PictureAnswerOption : BaseQuestionOptionView
    {
        public PictureAnswerOption(QuestionOption option) : base(option)
        {
            InitializeComponent();
            Load();
        }
        private async void Load()
        {
            Source = await ResorcesHelper.GetImageSource((Option as PictureQuestionOption).ImgPath);
            rb.IsChecked = IsChecked;

            onChecked(null, null);
        }


        private ImageSource source;
        public ImageSource Source
        {
            get { return source; }
            set { source = value; OnPropertyChanged(nameof(Source)); }
        }
        private void onChecked(object sender, CheckedChangedEventArgs e)
        {
            checkedState.IsVisible = IsChecked;
            uncheckedState.IsVisible = !checkedState.IsVisible;
        }

        private void onTapped(object sender, EventArgs e)
        {
            IsChecked = !IsChecked;
        }
    }
}