﻿ using System.Diagnostics;
 using Microsoft.Maui.Controls.Internals;

namespace Triggero.MauiMobileApp;

/// <summary>
/// Provides in-app messaging functionality allowing components to communicate
/// without direct references.
/// </summary>
[Preserve(AllMembers = true)]
public class InAppMessager : IInAppMessager
{
    private readonly ReaderWriterLockSlim _lock = new ReaderWriterLockSlim();
    private readonly Dictionary<string, List<Subscription>> _subscriptions = new Dictionary<string, List<Subscription>>();

    private static IInAppMessager? me;

    public static IInAppMessager? Instance
    {
        get
        {
            if (me == null)
            {
                me = new InAppMessager();
            }
            return me;
        }
    }

    /// <summary>
    /// Sends a message with arguments to all subscribers of the specified message.
    /// </summary>
    /// <typeparam name="T">Type of the message arguments.</typeparam>
    /// <param name="message">The message identifier.</param>
    /// <param name="args">The arguments to send with the message.</param>
    public void All<T>(string message, T args)
    {
        if (string.IsNullOrEmpty(message))
            throw new ArgumentNullException(nameof(message));

        Debug.WriteLine($"[MESSAGE] sent: {message} {args}");

        List<Subscription> subscriptions = null;

        _lock.EnterReadLock();
        try
        {
            if (_subscriptions.TryGetValue(message, out var subs))
            {
                subscriptions = subs.ToList();
            }
        }
        finally
        {
            _lock.ExitReadLock();
        }

        if (subscriptions != null)
        {
            foreach (var subscription in subscriptions)
            {
                if (subscription.Subscriber != null && subscription.CallbackMethod != null)
                {
                    try
                    {
                        if (args is T typedArgs)
                        {
                            var callback = subscription.CallbackMethod as Action<object, T>;
                            callback?.Invoke(subscription.Subscriber, typedArgs);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Exception in message handler: {ex.Message}");
                    }
                }
            }
        }
    }

    /// <summary>
    /// Subscribes an object to receive notifications for a specific message.
    /// </summary>
    /// <typeparam name="T">Type of the message arguments.</typeparam>
    /// <param name="subscriber">The object subscribing to the message.</param>
    /// <param name="message">The message identifier to subscribe to.</param>
    /// <param name="callback">The callback to invoke when the message is received.</param>
    public void Subscribe<T>(object subscriber, string message, Action<object, T> callback)
    {
        if (subscriber == null)
            throw new ArgumentNullException(nameof(subscriber));

        if (string.IsNullOrEmpty(message))
            throw new ArgumentNullException(nameof(message));

        if (callback == null)
            throw new ArgumentNullException(nameof(callback));

        _lock.EnterWriteLock();
        try
        {
            if (!_subscriptions.TryGetValue(message, out var subscriptions))
            {
                subscriptions = new List<Subscription>();
                _subscriptions[message] = subscriptions;
            }

            var existingSubscription = subscriptions.FirstOrDefault(s =>
                s.Subscriber == subscriber && s.MessageId == message);

            if (existingSubscription != null)
            {
                existingSubscription.CallbackMethod = callback;
            }
            else
            {
                subscriptions.Add(new Subscription
                {
                    Subscriber = subscriber,
                    MessageId = message,
                    CallbackMethod = callback,
                    ArgumentType = typeof(T)
                });
            }
        }
        finally
        {
            _lock.ExitWriteLock();
        }
    }

    /// <summary>
    /// Unsubscribes an object from a specific message.
    /// </summary>
    /// <param name="subscriber">The object to unsubscribe.</param>
    /// <param name="message">The message identifier to unsubscribe from.</param>
    public void Unsubscribe(object subscriber, string message)
    {
        if (subscriber == null)
            throw new ArgumentNullException(nameof(subscriber));

        if (string.IsNullOrEmpty(message))
            throw new ArgumentNullException(nameof(message));

        _lock.EnterWriteLock();
        try
        {
            if (_subscriptions.TryGetValue(message, out var subscriptions))
            {
                subscriptions.RemoveAll(s => s.Subscriber == subscriber && s.MessageId == message);

                if (subscriptions.Count == 0)
                {
                    _subscriptions.Remove(message);
                }
            }
        }
        finally
        {
            _lock.ExitWriteLock();
        }
    }

    /// <summary>
    /// Unsubscribes an object from all messages.
    /// </summary>
    /// <param name="subscriber">The object to unsubscribe.</param>
    public void UnsubscribeAll(object subscriber)
    {
        if (subscriber == null)
            throw new ArgumentNullException(nameof(subscriber));

        _lock.EnterWriteLock();
        try
        {
            var messagesToRemove = new List<string>();

            foreach (var entry in _subscriptions)
            {
                entry.Value.RemoveAll(s => s.Subscriber == subscriber);

                if (entry.Value.Count == 0)
                {
                    messagesToRemove.Add(entry.Key);
                }
            }

            foreach (var message in messagesToRemove)
            {
                _subscriptions.Remove(message);
            }
        }
        finally
        {
            _lock.ExitWriteLock();
        }
    }

    /// <summary>
    /// Internal class representing a message subscription.
    /// </summary>
    private class Subscription
    {
        public object Subscriber { get; set; }
        public string MessageId { get; set; }
        public object CallbackMethod { get; set; }
        public Type ArgumentType { get; set; }
    }
}