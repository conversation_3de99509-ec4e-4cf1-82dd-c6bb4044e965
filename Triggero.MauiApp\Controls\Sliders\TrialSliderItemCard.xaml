﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Cards.Sliders.TrialSliderItemCard"
             x:Name="this">
  <ContentView.Content>

        <Frame
            HasShadow="False"
            Padding="20"
            BackgroundColor="#F2F9FF"
            CornerRadius="16">
            <StackLayout Spacing="0">

                <Image
                    x:Name="img"
                    Source="{Binding Source={x:Reference this},Path=TrialSliderItem.ImgPath}"
                    WidthRequest="110"
                    HeightRequest="110"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"/>

                <StackLayout
                    Spacing="9"
                    Orientation="Horizontal"
                    Margin="0,20,0,0">

                    <Image 
                        Source="completedcircleyellow.png"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        WidthRequest="24"
                        HeightRequest="24"/>

                    <Label 
                        x:Name="textLabel"
                        WidthRequest="226"
                        TextColor="{x:StaticResource ColorText}"
                        FontSize="14"
                        FontAttributes="Bold"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Text="{Binding Source={x:Reference this},Path=TrialSliderItem.Text}"/>

                </StackLayout>
                
           
            </StackLayout>
        </Frame>
      
      
  </ContentView.Content>
</ContentView>