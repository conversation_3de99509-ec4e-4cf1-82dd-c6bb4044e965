﻿using AppoMobi.Specials;
using Plugin.Maui.Audio;
using System.Threading.Tasks;
using System.Windows.Input;
using Syncfusion.Maui.Sliders;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Extensions.Helpers.Modules;
using Triggero.MauiMobileApp.Services;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;

namespace Triggero.MauiMobileApp.Views.Pages.Library
{
    public partial class PracticePage : ContentPage, IDisposable
    {
#if PREVIEWS

        [HotPreview.Preview]
        public static void Preview()
        {
            var model = new Practice()
            {
                Category = new PracticeCategory()
                {
                    Title = "Category"
                },
                Description = "description ...",
                Title = "Preview",
                Localizations = new List<PracticeLocalization>()
            };

            App.OpenPage(new PracticePage(model));
        }
#endif

        public PracticePage(Practice practice)
        {
            try
            {
                Practice = practice;

                _audioPlayer = Super.Services.GetService<IAudioService>();

                var image = App.GetFullImageUrl(Practice.ImgPath, ThumbnailSize.Large, ThumbnailType.Jpg);
                //SkiaImageManager.Instance.Preload(image, default(CancellationTokenSource)).ConfigureAwait(false);

                InitializeComponent();

                img.Source = image;

                titleLabel.Text = Practice.GetLocalizedTitle(LanguageHelper.LangCode);

                Tasks.StartDelayed(TimeSpan.FromMilliseconds(30), () => { Load(); });
            }
            catch (Exception e)
            {
                Super.DisplayException(this, e);
            }
        }

        public void Dispose()
        {
            _audioPlayer?.Dispose();
        }

        private Practice practice;

        public Practice Practice
        {
            get { return practice; }
            set
            {
                practice = value;
                OnPropertyChanged();
            }
        }

        public ICommand CommandOpenReferenceLink
        {
            get
            {
                return new Command((context) =>
                {
                    if (!string.IsNullOrEmpty(Practice.ReferenceLink))
                    {
                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            PlatformUi.Instance.OpenUrl(Practice.ReferenceLink);
                        });
                    }
                });
            }
        }

        private IAudioService _audioPlayer;


        private async void toggleFavorite(object? sender, bool b)
        {
            if (isFav != favoriteRb.IsToggled)
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    isFav = favoriteRb.IsToggled;
                    App.Messager.All("FavChanged", new FavChangedObject(Practice.Id, isFav));
                    try
                    {
                        await ApplicationState.UserFavorites.TogglePractice(Practice.Id);
                    }
                    catch (Exception exception)
                    {
                        Console.WriteLine(exception);
                    }
                });
            }
        }

        private bool isFav;

        private void Load()
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    _audioPlayer.PositionChanged += AudioPositionChanged;

                    var uri = new System.Uri(Constants.BuildContentUrl(Practice.AudioPath));

                    Task.Run(async () =>
                    {
                        await _audioPlayer.PlayAsync(uri.AbsoluteUri);

                        if (hidden)
                        {
                            _audioPlayer.Stop();
                        }
                    });

                    isFav = ApplicationState.UserFavorites.HasFavoritePractice(Practice.Id);
                    favoriteRb.IsToggled = isFav;

                    //img.Source = await ResorcesHelper.GetImageSource(Practice.ImgPath);

                    //_audioPlayer.Notification.Enabled = true;
                    //_audioPlayer.Notification.ShowPlayPauseControls = true;
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }
            });

            StatsHelper.AddPracticePassingResult(Practice.Id);
        }


        bool hidden;

        protected override void OnDisappearing()
        {
            base.OnDisappearing();

            hidden = true;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                Super.KeepScreenOn = false;
                _audioPlayer.Stop();
            });
        }

        bool once;

        protected override void OnAppearing()
        {
            base.OnAppearing();

            PlatformUi.Instance.HideStatusBar();

            MainThread.BeginInvokeOnMainThread(async () => { Super.KeepScreenOn = true; });

            if (!once)
            {
                once = true;
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    descLabel.Text = Practice.GetLocalizedDescription(LanguageHelper.LangCode);

                    var pause = 700; //on iOS this will be triggered BEFORE the page is shown, so we delay a bit
                    if (Device.RuntimePlatform == Device.Android)
                        pause = 10;
                    await Task.Delay(pause);

                    await mainStackLayout.FadeTo(1, 700);
                });
            }
        }

        private void AudioPositionChanged(object? sender, AudioProgressEventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                slider.Minimum = 0;
                slider.Maximum = (double)_audioPlayer.Duration.TotalSeconds;

                currentProgress.Text = _audioPlayer.Position.ToString(@"mm\:ss");
                totalTime.Text = _audioPlayer.Duration.ToString(@"mm\:ss");

                slider.Minimum = 0;
                slider.Maximum = (double)_audioPlayer.Duration.TotalSeconds;

                if (!isEditing)
                {
                    slider.Value = _audioPlayer.Position.TotalSeconds;
                }

                if (_audioPlayer.Position == _audioPlayer.Duration && !_audioPlayer.IsPlaying)
                {
                    playerBtn.IsChecked = false;
                }
            });

            if (hidden)
            {
                MainThread.BeginInvokeOnMainThread(async () => { _audioPlayer.Stop(); });
            }
        }

        private bool isEditing = false;

        private void onSliderValueChanging(object? sender, SliderValueChangingEventArgs e)
        {
            if (isEditing)
            {
                _audioPlayer.SeekTo(TimeSpan.FromSeconds(e.NewValue));
            }
        }


        private void onSliderTouchDown(object? sender, EventArgs eventArgs)
        {
            isEditing = true;
        }

        private void onSliderTouchUp(object? sender, EventArgs eventArgs)
        {
            isEditing = false;
        }


        private RelayCommand? decrement10Seconds;

        public RelayCommand? Decrement10Seconds
        {
            get => decrement10Seconds ??= new RelayCommand(async obj =>
            {
                if (_audioPlayer.Position.TotalSeconds >= 10)
                {
                    _audioPlayer.SeekTo(_audioPlayer.Position.Subtract(TimeSpan.FromSeconds(10)));
                }
                else
                {
                    _audioPlayer.SeekTo(TimeSpan.Zero);
                }
            });
        }


        private RelayCommand? increment10Seconds;

        public RelayCommand Increment10Seconds
        {
            get => increment10Seconds ??= new RelayCommand(async obj =>
            {
                if (_audioPlayer.Position.TotalSeconds + 10 < _audioPlayer.Duration.TotalSeconds)
                {
                    _audioPlayer.SeekTo(_audioPlayer.Position.Add(TimeSpan.FromSeconds(10)));
                }
                else
                {
                    _audioPlayer.Position = TimeSpan.Zero;
                }
            });
        }

        //private bool isPlayerActive = true;
        private async void togglePlayer(object sender, EventArgs e)
        {
            //  isPlayerActive = !isPlayerActive;
            // playerBtn.IsChecked = isPlayerActive;

            playerBtn.IsChecked = !_audioPlayer.IsPlaying;

            if (!_audioPlayer.IsPlaying)
            {
                if (_audioPlayer.Duration.TotalSeconds == 0)
                {
                    var uri = new System.Uri(Constants.BuildContentUrl(Practice.AudioPath));
                    await _audioPlayer.PlayAsync(uri.AbsoluteUri);
                }
                else
                {
                    _audioPlayer.Resume();
                }
            }
            else
            {
                _audioPlayer.Pause();
            }
        }


        private ICommand close;

        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                _audioPlayer.Stop();
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
    }
}