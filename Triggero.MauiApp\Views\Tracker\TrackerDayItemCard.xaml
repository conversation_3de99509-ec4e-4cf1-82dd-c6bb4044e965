﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
             
             x:Class="Triggero.Controls.Templates.TrackerDayItemCard">
  <ContentView.Content>
        <Border
               Padding="0"
               BackgroundColor="White"
               StrokeShape="RoundRectangle 0,16,16,0">
            <Border.Shadow>
                <Shadow Brush="#27527A"
                        Offset="2,2"
                        Radius="12"
                        Opacity="0.06" />
            </Border.Shadow>
            <Border.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped"/>
            </Border.GestureRecognizers>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="75"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0">
                        <Image
                            x:Name="moodImg"
                            HeightRequest="40"
                            WidthRequest="40"
                            HorizontalOptions="Center"
                            VerticalOptions="Center"/>
                    </Grid>

                    <Grid Grid.Column="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="4*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <StackLayout
                            Grid.Column="0"
                            Spacing="1"
                            Margin="0,0,0,0"
                            VerticalOptions="Center"
                            HorizontalOptions="Start">

                            <Label 
                                x:Name="moodTitleLabel"
                                TextColor="{x:StaticResource ColorText}"
                                FontSize="17"
                                FontFamily="FontTextLight"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Text="Отлично"/>
                            <Label 
                                x:Name="dateLabel"
                                TextColor="{x:StaticResource ColorText}"
                                Opacity="0.6"
                                FontSize="12"
                                FontFamily="FontTextLight"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Text="Воскресенье, 9 окт."/>
                            <Label 
                                x:Name="timeLabel"
                                TextColor="{x:StaticResource ColorText}"
                                Opacity="0.6"
                                FontSize="12"
                                FontFamily="FontTextLight"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Text="15:45"/>
                        </StackLayout>

                        <Grid Grid.Column="1">

                            <Image
                                Source="arrowforwardlightblue.png"
                                HorizontalOptions="End"
                                VerticalOptions="Center"
                                Margin="0,0,15,0"
                                WidthRequest="7"
                                HeightRequest="13"/>

                        </Grid>


                    </Grid>



                </Grid>
        </Border>
    </ContentView.Content>
</ContentView>