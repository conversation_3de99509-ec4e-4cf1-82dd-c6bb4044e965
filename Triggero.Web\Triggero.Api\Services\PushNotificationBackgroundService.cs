using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Triggero.Database;
using Triggero.PushNotifications;

namespace Triggero.Api.Services;

public class PushNotificationBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<PushNotificationBackgroundService> _logger;
    private readonly TimeSpan _period = TimeSpan.FromMinutes(1); // Run every minute

    // Static dictionaries to track notifications (shared across service instances)
    private static readonly Dictionary<int, int> NotifiedTrackerUsers = new();
    private static readonly Dictionary<int, int> NotifiedBreathUsers = new();

    public PushNotificationBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<PushNotificationBackgroundService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("PushNotificationBackgroundService started");

        // Initial delay before starting
        await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessPushNotifications(stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred in PushNotificationBackgroundService");
            }

            // Clean up notification tracking
            ClearNotifiedIfNextHour(NotifiedBreathUsers);
            ClearNotifiedIfNextHour(NotifiedTrackerUsers);

            // Wait for the next iteration
            try
            {
                await Task.Delay(_period, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
        }

        _logger.LogInformation("PushNotificationBackgroundService stopped");
    }

    private async Task ProcessPushNotifications(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<DatabaseContext>();

        var users = await db.Users
            .Include(o => o.NotificationSettings)
            .Include(o => o.Devices)
            .Where(o => o.NotificationSettings.ShouldNotifyTrackerFilling
                     || o.NotificationSettings.ShouldNotifyBreathTime)
            .ToListAsync(cancellationToken);

        _logger.LogDebug("Processing push notifications for {UserCount} users", users.Count);

        foreach (var user in users)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            var userLocalTime = DateTime.UtcNow.Add(user.NotificationSettings.BaseUtcOffset);

            // Process tracker filling notifications
            if (user.NotificationSettings.ShouldNotifyTrackerFilling
                && CheckTime(userLocalTime, user.NotificationSettings.NotifyTrackerFillingTime)
                && !NotifiedTrackerUsers.ContainsKey(user.Id))
            {
                await SendNotificationsToUser(user, "Напоминание", "Заполните трекер настроения");
                NotifiedTrackerUsers[user.Id] = DateTime.UtcNow.Hour;
                _logger.LogInformation("Sent tracker filling notification to user {UserId}", user.Id);
            }

            // Process breath time notifications
            if (user.NotificationSettings.ShouldNotifyBreathTime
               && CheckTime(userLocalTime, user.NotificationSettings.NotifyBreathTime)
               && !NotifiedBreathUsers.ContainsKey(user.Id))
            {
                await SendNotificationsToUser(user, "Напоминание", "Выполните дыхательную практику");
                NotifiedBreathUsers[user.Id] = DateTime.UtcNow.Hour;
                _logger.LogInformation("Sent breath time notification to user {UserId}", user.Id);
            }
        }
    }

    private async Task SendNotificationsToUser(dynamic user, string title, string message)
    {
        foreach (var device in user.Devices)
        {
            try
            {
                PushNotificationsHelper.SendNotification(device.FirebaseToken, title, message);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to send notification to device: {ex.Message}");
            }
        }
    }

    private static bool CheckTime(DateTime userLocalTime, TimeSpan time)
    {
        return time.Hours == userLocalTime.Hour && time.Minutes <= userLocalTime.Minute;
    }

    private static void ClearNotifiedIfNextHour(Dictionary<int, int> notifiedUsers)
    {
        var currentHour = DateTime.UtcNow.Hour;
        var keysToRemove = notifiedUsers
            .Where(kvp => kvp.Value != currentHour)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in keysToRemove)
        {
            notifiedUsers.Remove(key);
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("PushNotificationBackgroundService is stopping");
        await base.StopAsync(cancellationToken);
    }
}
