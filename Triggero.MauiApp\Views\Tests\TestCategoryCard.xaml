﻿<?xml version="1.0" encoding="UTF-8"?>

<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"

             x:Class="Triggero.Controls.Templates.TestCategoryCard"
             x:Name="this">
    <ContentView.Content>
        <pancakeview:PancakeView
            Padding="0"
            BackgroundColor="#F5F9FD"
            StrokeShape="RoundRectangle 0,16,16,0">
            <pancakeview:PancakeView.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped" />
            </pancakeview:PancakeView.GestureRecognizers>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="90" />
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">
                    <StackLayout
                        Margin="16,0,0,0"
                        VerticalOptions="Center"
                        HorizontalOptions="Start">
                        <Label
                            x:Name="titleLabel"
                            TextColor="{x:StaticResource ColorText}"
                            FontSize="15"
                            FontFamily="FontTextBold"
                            FontAttributes="Bold"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            Text="Все упражнения" />
                        <Label
                            x:Name="descLabel"
                            TextColor="{x:StaticResource ColorText}"
                            Opacity="0.7"
                            FontSize="12"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            Text="Все упражнения" />
                    </StackLayout>
                </Grid>

                <Grid Grid.Column="1">
                    <Image
                        x:Name="img"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        WidthRequest="70"
                        HeightRequest="70" />
                </Grid>

            </Grid>
        </pancakeview:PancakeView>
    </ContentView.Content>
</ContentView>