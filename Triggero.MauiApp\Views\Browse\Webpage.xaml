﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:BasePage
    x:Class="Triggero.MauiMobileApp.Views.Browse.Webpage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pages="clr-namespace:Triggero.MauiMobileApp.Views.Pages;assembly=Triggero.MauiMobileApp"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    x:Name="ThisPage"
    NavigationPage.HasNavigationBar="False"
    Shell.NavBarIsVisible="False"
    Shell.PresentationMode="Animated">

    <ContentPage.Content>

        <Grid
            BackgroundColor="White"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand">

            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <StackLayout
                Grid.Row="1"
                HorizontalOptions="FillAndExpand"
                Spacing="0"
                VerticalOptions="FillAndExpand">

                <WebView
                    x:Name="ControlBrowser"
                    Margin="0,0"
                    HorizontalOptions="FillAndExpand"
                    VerticalOptions="FillAndExpand" />

            </StackLayout>

            <Grid
                Margin="0,20,5,0"
                HeightRequest="55"
                HorizontalOptions="End"
                Row="1"
                VerticalOptions="Start"
                WidthRequest="55">
                <Grid.GestureRecognizers>
                    <TapGestureRecognizer Command="{Binding Source={x:Reference ThisPage}, Path=Close}" />
                </Grid.GestureRecognizers>

                <ImageButton
                    BackgroundColor="Transparent"
                    CornerRadius="0"
                    HeightRequest="17"
                    HorizontalOptions="Center"
                    InputTransparent="True"
                    Source="close.png"
                    VerticalOptions="Center"
                    WidthRequest="17" />
            </Grid>

        </Grid>


    </ContentPage.Content>
</pages:BasePage>