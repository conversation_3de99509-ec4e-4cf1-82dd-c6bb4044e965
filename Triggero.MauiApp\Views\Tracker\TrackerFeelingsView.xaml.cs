﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;

using Triggero.Models;
using Triggero.Models.MoodTracker.User;



namespace Triggero.Controls.MoodTracker
{
	
	public partial class TrackerFeelingsView : ContentView
    {
        public static readonly BindableProperty TrackerProperty = BindableProperty.Create(nameof(Tracker), typeof(MoodtrackerItem), typeof(TrackerFeelingsView));
        public MoodtrackerItem Tracker
        {
            get { return (MoodtrackerItem)GetValue(TrackerProperty); }
            set { SetValue(TrackerProperty, value); }
        }
        public TrackerFeelingsView()
		{
			InitializeComponent();

            // xxx.ThumbImageSource = ImageSource.FromFile("thumb20.png");
            Refresh();

        }
        

        public void Refresh()
        {
            SetProgressFrameWidth(happinessSlider, happinessProgress,happinessGridScale, false);
            SetProgressFrameWidth(InsprirationSlider, insprirationProgress,inspirationGridScale, false);
            SetProgressFrameWidth(confidenceSlider, confidenceProgress,confidenceGridScale, false);
            SetProgressFrameWidth(stressSlider, stressProgress,stressGridScale, true);
            SetProgressFrameWidth(anxientySlider, anxientyProgress,annoyanceGridScale,true);
            SetProgressFrameWidth(annoyanceSlider, annoyanceProgress,annoyanceGridScale, true);
        }



        private void SetProgressFrameWidth(Slider slider,Frame progressFrame,Grid scaleGrid,bool isNegative)
        {

            double part = 0;
            if(Device.RuntimePlatform == Device.Android)
            {
                part = (slider.Width - (slider.Width / 100 * 11)) / 5;
                progressFrame.WidthRequest = part * slider.Value + 30;
            }
            else if (Device.RuntimePlatform == Device.iOS)
            {
                part = (slider.Width - (slider.Width / 100d * 8.52d)) / 5d;
                progressFrame.WidthRequest = part * slider.Value + 24;



                if (slider.Value > 3.5)
                {
                    progressFrame.WidthRequest += 2;
                }
                else if (slider.Value > 2.5)
                {
                    progressFrame.WidthRequest += 1;
                }
            }


            SetColor(slider, progressFrame, isNegative);


             var val = (int)Math.Round(slider.Value, 0);
            for (int i = 0; i < scaleGrid.Children.Count; i++)
            {
                if (val == i)
                {
                    (scaleGrid.Children[i] as Label).TextColor = Colors.Black;
                    (scaleGrid.Children[i] as Label).Opacity = 1;
                }
                else
                {
                    (scaleGrid.Children[i] as Label).TextColor = Color.FromHex("#363B40");
                    (scaleGrid.Children[i] as Label).Opacity = 0.5;
                }
            }


        }


        private void SetColor(Slider slider,Frame progressFrame,bool isNegative)
        {

            if (!isNegative)
            {
                if (slider.Value < 1)
                {
                    progressFrame.Background = new LinearGradientBrush
                    {
                        GradientStops = new GradientStopCollection
                        {
                            new GradientStop(Color.FromHex("#E8ECF1"),0),
                            new GradientStop(Color.FromHex("#C8CED3"),1),
                        }
                    };
                }
                else if (slider.Value < 2)
                {
                    //   progressFrame.WidthRequest += 10;
                    progressFrame.Background = new LinearGradientBrush
                    {
                        GradientStops = new GradientStopCollection
                        {
                            new GradientStop(Color.FromHex("#E8ECF1"),0),
                            new GradientStop(Color.FromHex("#C8CED3"),1),
                        }
                    };
                }
                else if (slider.Value < 2)
                {
                    //   progressFrame.WidthRequest += 5;
                    progressFrame.Background = new LinearGradientBrush
                    {
                        GradientStops = new GradientStopCollection
                        {
                            new GradientStop(Color.FromHex("#EAF3FA"),0),
                            new GradientStop(Color.FromHex("#D1E5F5"),1),
                        }
                    };
                }
                else if (slider.Value < 3)
                {
                    progressFrame.Background = new LinearGradientBrush
                    {
                        GradientStops = new GradientStopCollection
                        {
                            new GradientStop(Color.FromHex("#EAF3FA"),0),
                            new GradientStop(Color.FromHex("#D1E5F5"),1),
                        }
                    };
                }
                else if (slider.Value < 4)
                {
                    // progressFrame.WidthRequest -= 2;
                    progressFrame.Background = new LinearGradientBrush
                    {
                        GradientStops = new GradientStopCollection
                        {
                            new GradientStop(Color.FromHex("#F8E9C9"),0),
                            new GradientStop(Color.FromHex("#FDD079"),1),
                        }
                    };
                }
                else if (slider.Value < 6)
                {
                    progressFrame.Background = new LinearGradientBrush
                    {
                        GradientStops = new GradientStopCollection
                        {
                            new GradientStop(Color.FromHex("#F8E9C9"),0),
                            new GradientStop(Color.FromHex("#FDD079"),1),
                        }
                    };
                }
            }
            else
            {
                if (slider.Value < 1)
                {
                    progressFrame.Background = new LinearGradientBrush
                    {
                        GradientStops = new GradientStopCollection
                        {
                            new GradientStop(Color.FromHex("#F8E9C9"),0),
                            new GradientStop(Color.FromHex("#FDD079"),1),
                        }
                    };          
                }
                else if (slider.Value < 2)
                {
                    // progressFrame.WidthRequest -= 2;
                    progressFrame.Background = new LinearGradientBrush
                    {
                        GradientStops = new GradientStopCollection
                        {
                            new GradientStop(Color.FromHex("#F8E9C9"),0),
                            new GradientStop(Color.FromHex("#FDD079"),1),
                        }
                    };
                }
                else if (slider.Value < 2)
                {
                    //   progressFrame.WidthRequest += 5;
                    progressFrame.Background = new LinearGradientBrush
                    {
                        GradientStops = new GradientStopCollection
                        {
                            new GradientStop(Color.FromHex("#EAF3FA"),0),
                            new GradientStop(Color.FromHex("#D1E5F5"),1),
                        }
                    };
                }
                else if (slider.Value < 3)
                {
                    //   progressFrame.WidthRequest += 10;
                    progressFrame.Background = new LinearGradientBrush
                    {
                        GradientStops = new GradientStopCollection
                        {
                            new GradientStop(Color.FromHex("#E8ECF1"),0),
                            new GradientStop(Color.FromHex("#C8CED3"),1),
                        }
                    };
                }
                else if (slider.Value < 4)
                {
                    progressFrame.Background = new LinearGradientBrush
                    {
                        GradientStops = new GradientStopCollection
                        {
                            new GradientStop(Color.FromHex("#EAF3FA"),0),
                            new GradientStop(Color.FromHex("#D1E5F5"),1),
                        }
                    };
                }
                else if (slider.Value < 6)
                {
                    progressFrame.Background = new LinearGradientBrush
                    {
                        GradientStops = new GradientStopCollection
                        {
                            new GradientStop(Color.FromHex("#E8ECF1"),0),
                            new GradientStop(Color.FromHex("#C8CED3"),1),
                        }
                    };
                }
            }

           
        }







        private void confidenceValueChanged(object sender, ValueChangedEventArgs e)
        {
            SetProgressFrameWidth(confidenceSlider, confidenceProgress,confidenceGridScale,false);
        }

        private void InsprirationValueChanged(object sender, ValueChangedEventArgs e)
        {
            SetProgressFrameWidth(InsprirationSlider, insprirationProgress,inspirationGridScale, false);
        }

        private void happinessValueChanged(object sender, ValueChangedEventArgs e)
        {
            SetProgressFrameWidth(happinessSlider, happinessProgress,happinessGridScale, false);
        }

        private void stressValueChanged(object sender, ValueChangedEventArgs e)
        {
            SetProgressFrameWidth(stressSlider, stressProgress,stressGridScale, true);
        }

        private void anxientyValueChanged(object sender, ValueChangedEventArgs e)
        {
            SetProgressFrameWidth(anxientySlider, anxientyProgress,anxientyGridScale, true);
        }

        private void annoyanceValueChanged(object sender, ValueChangedEventArgs e)
        {
            SetProgressFrameWidth(annoyanceSlider, annoyanceProgress,annoyanceGridScale, true);
        }
    }
}