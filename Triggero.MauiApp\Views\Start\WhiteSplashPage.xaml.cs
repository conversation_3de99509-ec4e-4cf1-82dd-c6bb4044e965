﻿using AppoMobi.Specials;
using Triggero;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Extensions.Helpers.Modules;

using App = Triggero.MauiMobileApp.App;

namespace Triggero.MauiMobileApp
{

    public partial class WhiteSplashPage : ContentPage
    {
        public WhiteSplashPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }
        protected override void OnAppearing()
        {
            base.OnAppearing();

            ApplicationState.ConfigData = ApplicationState.GetFromFile<ConfigData>(ConfigData.FilePath);

            if (ApplicationState.ConfigData.IsFirstUse)
            {
                App.OpenPage(new SplashPage());
            }
            else
            {
                App.OpenPage(new SplashPageWithDog());
            }

            Tasks.StartDelayed(TimeSpan.FromSeconds(1), () =>
            {
                App.ClosePage(this);
            });


        }

    }
}