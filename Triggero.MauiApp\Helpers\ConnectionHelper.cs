﻿

namespace Triggero.MauiMobileApp.Extensions.Helpers
{
    public static class ConnectionHelper
    {
        public static readonly HttpClient _client = new HttpClient();

        /// <summary>
        /// Checks if the device has an active internet connection
        /// </summary>
        /// <returns>True if connected to internet, false otherwise</returns>
        public static bool HasInternet()
        {
            return Connectivity.Current.NetworkAccess == NetworkAccess.Internet;
        }
    }

}
