﻿using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using Triggero.Controls.Cards.Tests.Questions;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiPort.Models;
using Triggero.Models.Enums;
using Triggero.Models.Tests;
using Triggero.Models.Tests.Questions;


namespace Triggero.MauiMobileApp.Views.Pages.Tests
{

    public partial class TestQuestionPage : ContentPage
    {
        public ICommand CommandTest => new Command((ctx) =>
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {


            });
        });

        private double _scores;
        private Dictionary<TestScale, int> _scoresGroup;


        private Test _test;
        private int _questionNumber;
        private double _questionScores = 0;
        private Dictionary<TestScale, int> _questionScoresGroup = new Dictionary<TestScale, int>();

        private double _progress = 0;

        public TestQuestionPage(Test test, int questionNumber, double scores)
        {
            _scores = scores;
            _test = test;
            _questionNumber = questionNumber;

            if (test.Questions.Count > 0)
            {
                _progress = 100 / test.Questions.Count * (questionNumber - 1);
            }


            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            goNextBtn.Text = $"Далее {_questionNumber}/{_test.Questions.Count}";



            Render();
        }
        public TestQuestionPage(Test test, int questionNumber, Dictionary<TestScale, int> scoresGroup)
        {
            _scoresGroup = scoresGroup;
            _test = test;
            _questionNumber = questionNumber;

            if (test.Questions.Count > 0)
            {
                _progress = 100 / test.Questions.Count * (questionNumber - 1);
            }


            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            goNextBtn.Text = $"Далее {_questionNumber}/{_test.Questions.Count}";

            Render();
        }



        private BaseQuestionView questionView = null;

        private void ProgressStackLayout_OnSizeChanged(object? sender, EventArgs e)
        {
            if (progressStackLayout.Width > 0)
            {
                percentLabel.Text = $"{(int)_progress}%";
                //    percentLabel.Margin = new Thickness(progressStackLayout.Width / 100 * _progress-6, 0, 0, 0);

                //      progressThumb.Margin = new Thickness(progressStackLayout.Width / 100 * _progress - 6, 0, 0, 0);
                progressFrame.WidthRequest = progressStackLayout.Width / 100 * _progress - 6;

                thumbStackLayout.Margin = new Thickness(progressStackLayout.Width / 100 * _progress - 6 - 15, 0, 0, 0);
                thumbStackLayout.IsVisible = true;
            }
        }

        private void Render()
        {
            var question = _test.Questions[_questionNumber - 1];

            testQuestionViewLayout.Children.Clear();
            if (question is SimpleQuestion)
            {
                questionView = new SimpleQuestionView(question);
            }
            else if (question is PictureQuestion)
            {
                questionView = new PicturesQuestionView(question);
            }
            else if (question is SimplePicturedQuestion)
            {
                questionView = new QuestionWithPictureView(question);
            }
            testQuestionViewLayout.Children.Add(questionView);
        }

        private RelayCommand goBack;
        public RelayCommand GoBack
        {
            get => goBack ??= new RelayCommand(obj =>
            {
                App.Current.MainPage.Navigation.PopAsync();
            });
        }


        private RelayCommand goNext;
        public RelayCommand GoNext
        {
            get => goNext ??= new RelayCommand(obj =>
            {
                if (!ValidateQuestion()) return;

                if (_test.Type == TestType.Scaled || _test.Type == TestType.ScaledWithConditions)
                {
                    var countedScores = new Dictionary<TestScale, int>();
                    foreach (var key in _scoresGroup)
                    {
                        var keyFromSelected = _questionScoresGroup.FirstOrDefault(o => o.Key.Id == key.Key.Id);
                        countedScores.Add(key.Key, key.Value + keyFromSelected.Value);
                    }

                    if (_questionNumber + 1 <= _test.Questions.Count)
                    {
                        App.OpenPage(new TestQuestionPage(_test, _questionNumber + 1, countedScores));
                    }
                    else
                    {
                        App.OpenPage(new TestResultPage(_test, countedScores));
                    }
                }
                else
                {
                    if (_questionNumber + 1 <= _test.Questions.Count)
                    {
                        App.OpenPage(new TestQuestionPage(_test, _questionNumber + 1, _scores + _questionScores));
                    }
                    else
                    {
                        App.OpenPage(new TestResultPage(_test, _scores + _questionScores));
                    }
                }

            });
        }

        private bool ValidateQuestion()
        {
            var selectedOptions = questionView.GetSelectedOptions();
            _questionScoresGroup.Clear();

            if (_test.Type == TestType.Scaled || _test.Type == TestType.ScaledWithConditions)
            {
                foreach (var scale in _test.Scales)
                {
                    int scores = 0;
                    foreach (var option in selectedOptions)
                    {
                        scores += option.ScaleScoreInfos.FirstOrDefault(o => o.ScaleId == scale.Id).Score;
                    }
                    _questionScoresGroup.Add(scale, scores);
                }
            }
            else
            {
                _questionScores = selectedOptions.Sum(o => o.Score);
            }


            return selectedOptions.Any();
        }


    }
}