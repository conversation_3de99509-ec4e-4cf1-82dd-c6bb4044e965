﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Subscriptions.BuyPlanPage">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="180"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Image
                Grid.RowSpan="4"
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="trialstartbg.png"/>

            <Grid
                Grid.RowSpan="2"
                Grid.Row="0">

                <ImageButton 
                    Command="{Binding Source={x:Reference this},Path=Close}"
                    Source="close.png"
                    Opacity="0.5"
                    VerticalOptions="Start"
                    HorizontalOptions="End"
                    Margin="0,50,25,0"
                    WidthRequest="14"
                    HeightRequest="14"
                    CornerRadius="0"
                    BackgroundColor="Transparent"/>

                <StackLayout 
                    Spacing="12"
                    VerticalOptions="End"
                    Margin="0,0,0,0"
                    HorizontalOptions="Center">
                    <Label 
                        TextColor="{x:StaticResource ColorText}"
                        FontSize="17"
                        FontAttributes="Bold"
                        VerticalOptions="Center"
                        WidthRequest="226"
                        HorizontalTextAlignment="Center"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.SubscriptionMain.TextFull}"/>
                    <Image 
                        Source="logotitleaqua.png"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        WidthRequest="142"
                        HeightRequest="20"/>


                    <Image
                        Margin="0,20,0,0"
                        HeightRequest="200"
                        WidthRequest="200"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        Source="knowyourself.png"/>

                </StackLayout>
                
            </Grid>

            <Grid Grid.Row="2"
                  ColumnSpacing="12"
                  Margin="20,0,20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <Grid 
                    x:Name="monthPlanSlot"
                    Grid.Column="0">

                </Grid>

                <Grid 
                   x:Name="threeMonthsPlanSlot"
                   Grid.Column="1">

                </Grid>


                <Grid
                    x:Name="yearPlanSlot"
                    Grid.Column="2">

                </Grid>

            </Grid>

            <Grid Grid.Row="3">

                <StackLayout 
                    Spacing="0"
                    Margin="20,0,20,0">

                    <Button 
                        Margin="0,0,0,0"
                        Command="{Binding Source={x:Reference this},Path=Pay}"
                        VerticalOptions="Start"
                        FontAttributes="Bold"
                        Style="{x:StaticResource yellow_btn}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.SubscriptionMain.Pay}"/>


                    <Grid
                        Margin="0,50,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <Grid Grid.Column="0">

                            <Label 
                                Opacity="0.5"
                                TextColor="{x:StaticResource ColorText}"
                                FontSize="10"
                                WidthRequest="80"
                                HorizontalTextAlignment="Center"
                                HorizontalOptions="Center"
                                TextDecorations="Underline"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.SubscriptionMain.Terms}">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToTerms}"/>
                                </Label.GestureRecognizers>
                            </Label>

                        </Grid>

                        <Grid Grid.Column="1">

                            <Label 
                                Opacity="0.5"
                                TextColor="{x:StaticResource ColorText}"
                                FontSize="10"
                                WidthRequest="80"
                                HorizontalTextAlignment="Center"
                                HorizontalOptions="Center"
                                TextDecorations="Underline"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.SubscriptionMain.EULA}">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToEULA}"/>
                                </Label.GestureRecognizers>
                            </Label>

                        </Grid>

                        <Grid Grid.Column="2">
                            <Label 
                                Opacity="0.5"
                                TextColor="{x:StaticResource ColorText}"
                                FontSize="10"
                                WidthRequest="80"
                                HorizontalTextAlignment="Center"
                                HorizontalOptions="Center"
                                TextDecorations="Underline"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.SubscriptionMain.Privacy}">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToPrivacy}"/>
                                </Label.GestureRecognizers>
                            </Label>
                        </Grid>

                    </Grid>


                </StackLayout>
                
            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>