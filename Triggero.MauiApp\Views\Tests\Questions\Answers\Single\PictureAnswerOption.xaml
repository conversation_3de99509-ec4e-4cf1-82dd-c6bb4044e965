﻿<?xml version="1.0" encoding="UTF-8"?>
<models:BaseQuestionOptionView
             
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:models="clr-namespace:Triggero.MauiPort.Models"
             x:Class="Triggero.Controls.Cards.Tests.Questions.Answers.Single.PictureAnswerOption"
             x:Name="this">

        <Grid>
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped"/>
            </Grid.GestureRecognizers>
            
            <RadioButton 
                GroupName="pxxx"
                CheckedChanged="onChecked"
                IsChecked="{Binding Source={x:Reference this},Path=IsChecked,Mode=TwoWay}"
                IsVisible="False"
                x:Name="rb"/>
            
            
            
            <Frame 
                HorizontalOptions="Fill"

                x:Name="checkedState"
                InputTransparent="True"
                BackgroundColor="Transparent"
                BorderColor="{x:StaticResource ColorPrimaryLight}"
                HasShadow="False"
                IsClippedToBounds="True"
                Padding="0"
                CornerRadius="12">
                <Image
                    Aspect="Fill"
                    Source="{Binding Source={x:Reference this},Path=Source}"/>
            </Frame>

            <Frame 
                x:Name="uncheckedState"
                InputTransparent="True"
                BackgroundColor="Transparent"
                BorderColor="Transparent"
                HasShadow="False"
                IsClippedToBounds="True"
                Padding="0"
                CornerRadius="12">
                <Image
                    Aspect="Fill"
                    Source="{Binding Source={x:Reference this},Path=Source}"/>
            </Frame>

        </Grid>
        
</models:BaseQuestionOptionView>