﻿<?xml version="1.0" encoding="utf-8"?>

<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Subscriptions.SelectPaymentPlanPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"

    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
    x:Name="this">
    <ContentPage.Content>
        <Grid
            RowSpacing="0"
            VerticalOptions="FillAndExpand">

            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Image
                Grid.RowSpan="2"
                Aspect="Fill"
                HorizontalOptions="Fill"
                Rotation="180"
                Source="trialstartbg.png"
                VerticalOptions="Fill" />


            <ScrollView
                Grid.Row="1"
                Padding="0">
                <Grid Padding="0">

                    <StackLayout
                        Margin="0,0,0,100"
                        Spacing="0">


                        <!--  button CLOSE  -->
                        <ImageButton
                            Margin="0,50,25,0"
                            BackgroundColor="Transparent"
                            Command="{Binding Source={x:Reference this}, Path=Close}"
                            CornerRadius="0"
                            HeightRequest="14"
                            HorizontalOptions="End"
                            Opacity="0.5"
                            Source="close.png"
                            VerticalOptions="Start"
                            WidthRequest="14" />

                        <!--CARD CURRENT PLAN-->
                        <Frame
                            x:Name="subscriptionDataFrame"
                            Margin="20,28,20,0"
                            Padding="20"
                            BackgroundColor="#FFFFFF"
                            CornerRadius="20"
                            HasShadow="False">
                            <Frame.Shadow>
                                <Shadow Brush="#27527A"
                                        Offset="2,2"
                                        Radius="12"
                                        Opacity="0.06" />
                            </Frame.Shadow>
                            <StackLayout Spacing="0">

                                <Label
                                    Margin="0,8,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text="Тарифный план"
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Start" />

                                <Label
                                    x:Name="tarifNameLabel"
                                    Margin="0,8,0,0"
                                    FontAttributes="Bold"
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text=""
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Start" />

                                <Label
                                    x:Name="activeBeforeLabel"
                                    Margin="0,8,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text="Обновление.."
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Start" />

                                <Label
                                    x:Name="cardNumberLabel"
                                    Margin="0,8,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text=""
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Start">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="Привязанная карта : " />
                                                <Span
                                                    x:Name="cardNumberSpan"
                                                    Text="" />
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>

                                <Label
                                    x:Name="unbindCardLabel"
                                    Margin="0,8,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text="Отвязать карту"
                                    TextColor="{x:StaticResource ColorText}"
                                    TextDecorations="Underline"
                                    VerticalOptions="Start">
                                    <Label.GestureRecognizers>
                                        <TapGestureRecognizer
                                            Command="{Binding Source={x:Reference this}, Path=UnbindCard}" />
                                    </Label.GestureRecognizers>
                                </Label>

                            </StackLayout>
                        </Frame>

                        <!--  ChooseYourPlan  -->

                        <Label
                            x:Name="LabelChooseYourPlan"
                            Margin="0,33,0,0"
                            FontAttributes="Bold"
                            FontSize="22"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            IsVisible="False"
                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.SelectYourSubscription}"
                            TextColor="{x:StaticResource ColorText}"
                            VerticalOptions="Center" />


                        <!--  FULL  -->
                        <Frame
                            x:Name="fullPlanContainer"
                            Margin="20,28,20,0"
                            IsVisible="False"
                            Padding="20"
                            BackgroundColor="#FFFFFF"
                            CornerRadius="20"
                            HasShadow="False">
                            <Frame.Shadow>
                                <Shadow Brush="#27527A"
                                        Offset="2,2"
                                        Radius="12"
                                        Opacity="0.06" />
                            </Frame.Shadow>
                            <StackLayout Spacing="0">
                                <Image
                                    HeightRequest="20"
                                    HorizontalOptions="Center"
                                    Source="logotitleaqua.png"
                                    VerticalOptions="Center"
                                    WidthRequest="142" />
                                <Label
                                    Margin="0,8,0,0"
                                    FontAttributes="Bold"
                                    FontSize="22"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.PremiumLowercase}"
                                    TextColor="{x:StaticResource ColorPrimary}"
                                    VerticalOptions="Center" />
                                <Label
                                    Margin="0,8,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Opacity="0.5"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.FullAccess}"
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Center" />

                                <Image
                                    Margin="0,22,0,0"
                                    HeightRequest="104"
                                    HorizontalOptions="Center"
                                    Source="dogonphonw.png"
                                    VerticalOptions="Start"
                                    WidthRequest="212" />


                                <StackLayout
                                    Margin="0,45,0,0"
                                    Spacing="10">

                                    <Frame
                                        Padding="0"
                                        BackgroundColor="Transparent"
                                        BorderColor="{x:StaticResource ColorPrimaryLight}"
                                        CornerRadius="20"
                                        HasShadow="False"
                                        HeightRequest="40">
                                        <Label
                                            FontSize="14"
                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.Exercises}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Center" />
                                    </Frame>

                                    <Frame
                                        Padding="0"
                                        BackgroundColor="Transparent"
                                        BorderColor="{x:StaticResource ColorPrimaryLight}"
                                        CornerRadius="20"
                                        HasShadow="False"
                                        HeightRequest="40">
                                        <Label
                                            FontSize="14"
                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.Practices}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Center" />
                                    </Frame>

                                    <Frame
                                        Padding="0"
                                        BackgroundColor="Transparent"
                                        BorderColor="{x:StaticResource ColorPrimaryLight}"
                                        CornerRadius="20"
                                        HasShadow="False"
                                        HeightRequest="40">
                                        <Label
                                            FontSize="14"
                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.BreathControl}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Center" />
                                    </Frame>

                                    <Frame
                                        Padding="0"
                                        BackgroundColor="Transparent"
                                        BorderColor="{x:StaticResource ColorPrimaryLight}"
                                        CornerRadius="20"
                                        HasShadow="False"
                                        HeightRequest="40">
                                        <Label
                                            FontSize="14"
                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.Topics}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Center" />
                                    </Frame>

                                    <Frame
                                        Padding="0"
                                        BackgroundColor="Transparent"
                                        BorderColor="{x:StaticResource ColorPrimaryLight}"
                                        CornerRadius="20"
                                        HasShadow="False"
                                        HeightRequest="40">
                                        <Label
                                            FontSize="14"
                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.Testing}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Center" />
                                    </Frame>

                                    <Frame
                                        Padding="0"
                                        BackgroundColor="Transparent"
                                        BorderColor="{x:StaticResource ColorPrimaryLight}"
                                        CornerRadius="20"
                                        HasShadow="False"
                                        HeightRequest="40">
                                        <Label
                                            FontSize="14"

                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.ChatBotHelper}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Center" />
                                    </Frame>

                                    <Frame
                                        Padding="0"
                                        BackgroundColor="Transparent"
                                        BorderColor="{x:StaticResource ColorPrimaryLight}"
                                        CornerRadius="20"
                                        HasShadow="False"
                                        HeightRequest="40">
                                        <Label
                                            FontSize="14"

                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.MoodDiary}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Center" />
                                    </Frame>

                                    <Frame
                                        Padding="0"
                                        BackgroundColor="Transparent"
                                        BorderColor="{x:StaticResource ColorPrimaryLight}"
                                        CornerRadius="20"
                                        HasShadow="False"
                                        HeightRequest="40">
                                        <Label
                                            FontSize="14"

                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.MoodStats}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Center" />
                                    </Frame>

                                    <Frame
                                        Padding="0"
                                        BackgroundColor="Transparent"
                                        BorderColor="{x:StaticResource ColorPrimaryLight}"
                                        CornerRadius="20"
                                        HasShadow="False"
                                        HeightRequest="40">
                                        <Label
                                            FontSize="14"

                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.Notes}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Center" />
                                    </Frame>


                                    <Button
                                        x:Name="fullAccessBtn"
                                        Margin="0,40,0,20"
                                        Command="{Binding Source={x:Reference this}, Path=GoToFullPlan}"
                                        FontAttributes="Bold"
                                        Style="{x:StaticResource yellow_btn}"
                                        Text=""
                                        VerticalOptions="Start" />

                                    <draw:Canvas
                                        x:Name="LabelExternalLink"
                                        HorizontalOptions="Fill"
                                        IsVisible="False">
                                        <draw:SkiaLayout HorizontalOptions="Fill">

                                            <draw:SkiaLabel
                                                Padding="16,8,16,8"
                                                draw:AddGestures.CommandTapped="{Binding Source={x:Reference this}, Path=CommandExternalPayment}"
                                                BackgroundColor="White"
                                                FontFamily="FontTextMedium"
                                                FontSize="15"
                                                HorizontalOptions="Center"
                                                HorizontalTextAlignment="Center"
                                                TextColor="#3333FF">
                                                <draw:SkiaLabel.Spans>

                                                    <draw:TextSpan
                                                        Text="{x:Static triggeroV2:Constants.ApplePaymentLink}" />

                                                    <draw:TextSpan Text=" " />

                                                    <draw:SvgSpan
                                                        Width="17"
                                                        Height="17"
                                                        Source="Images/linkout.svg"
                                                        TintColor="#3333FF"
                                                        VerticalAlignement="Center" />

                                                </draw:SkiaLabel.Spans>

                                            </draw:SkiaLabel>

                                        </draw:SkiaLayout>
                                    </draw:Canvas>

                                </StackLayout>

                            </StackLayout>
                        </Frame>

                        <!--  CUSTOM  -->
                        <Frame
                            x:Name="yourChoisePlanContainer"
                            Margin="20,40,20,0"
                            IsVisible="False"
                            Padding="20"
                            BackgroundColor="#FFFFFF"
                            CornerRadius="20"
                            HasShadow="False">
                            
                            <Frame.Shadow>
                                <Shadow Brush="#27527A"
                                        Offset="2,2"
                                        Radius="12"
                                        Opacity="0.06" />
                            </Frame.Shadow>

                            <StackLayout Spacing="0">
                                <Label
                                    Margin="0,8,0,0"
                                    FontAttributes="Bold"
                                    FontSize="22"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.YourChoise}"
                                    TextColor="{x:StaticResource ColorPrimary}"
                                    VerticalOptions="Center" />
                                <Label
                                    Margin="0,8,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Opacity="0.5"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SelectSubscription.YourChoiseDescription}"
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Center" />


                                <StackLayout
                                    x:Name="planOptionsLayout"
                                    Margin="0,45,0,0"
                                    Spacing="20">


                                </StackLayout>


                                <Button
                                    x:Name="partialAccessBtn"
                                    Margin="0,40,0,20"
                                    Command="{Binding Source={x:Reference this}, Path=GoToCustomPlan}"
                                    FontAttributes="Bold"
                                    Style="{x:StaticResource yellow_btn}"
                                    Text=""
                                    VerticalOptions="Start" />

                            </StackLayout>
                        </Frame>

                    </StackLayout>
                </Grid>
            </ScrollView>
        </Grid>
    </ContentPage.Content>
</ContentPage>