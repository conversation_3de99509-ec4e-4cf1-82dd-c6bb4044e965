﻿
using System.Threading.Tasks;
using System.Windows.Input;
using Triggero.MauiMobileApp.Controls;
using Triggero.MauiMobileApp.Views.Pages.MoodTracker;

namespace Triggero.MauiMobileApp.Views.Pages.Auth
{

    public partial class TrackerSavedPage : ContentPage
    {
        public TrackerSavedPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        protected override async void OnAppearing()
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {

                var animatedImage = new GifCachedImage
                {
                    HeightRequest = 173,
                    WidthRequest = 240,
                    Opacity = 0,

                    VerticalOptions = LayoutOptions.Start,
                    HorizontalOptions = LayoutOptions.Center,
                    //Aspect = Aspect.AspectFit,
                    Source = "animationdogwithcompanim.gif"
                };
                animatedLayout.Children.Insert(0, animatedImage);

                animatedImage.Success += async (sender, e) =>
                {
                    await Task.Delay(500);
                    animatedImage.Opacity = 1;
                    notAnimatedImg.Opacity = 0;
                };
            });

        }

        public ICommand GoToMain
        {
            get => new Command(async obj =>
            {
                App.PopToRoot();
            });
        }


        public ICommand GoToTracker
        {
            get => new Command(async obj =>
            {
                App.PopToRoot();
                App.OpenPage(new TrackerMainPage());
            });
        }
    }
}