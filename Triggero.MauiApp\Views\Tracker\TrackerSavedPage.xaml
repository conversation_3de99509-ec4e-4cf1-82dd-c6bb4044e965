﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             
             x:Class="Triggero.MauiMobileApp.Views.Pages.Auth.TrackerSavedPage"
             xmlns:app="clr-namespace:Triggero"
             
             x:Name="this">
    <ContentPage.Content>
        <Grid>

            <Image
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightbluegradientbg.png"/>

            <!--<sh:Shadows 
                Margin="30,0,30,0"
                HeightRequest="477"
                VerticalOptions="Center"
                Shades="{sh:SingleShade Offset='2, 2',
                                        BlurRadius=12,
                                        Opacity=0.06,
                                        Color=#27527A}">-->
                <Frame
                    Margin="30,0,30,0"
                    HeightRequest="477"
                    VerticalOptions="Center"
                    CornerRadius="16"
                    BackgroundColor="White"
                    HorizontalOptions="Fill"
       
          
                    HasShadow="False"
                    Padding="0">
                    <Grid>
                        <StackLayout
                            Spacing="0">


                            <Grid
                                x:Name="animatedLayout"
                                Margin="0,30,0,0"
                                HeightRequest="173"
                                WidthRequest="240"
                                HorizontalOptions="Center"
                                VerticalOptions="Start">
                                
                                <Image 
                                    x:Name="notAnimatedImg"
                                    HeightRequest="173"
                                    WidthRequest="240"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Start"
                                    Source="animationdogwithcomp.png"/>

                            </Grid>
                            
                     
                            <Label 
                                Margin="0,50,0,0"
                                TextColor="{x:StaticResource ColorText}"
                                FontSize="17"
                                WidthRequest="200"
                                HorizontalOptions="Center"
                                HorizontalTextAlignment="Center"
                                FontAttributes="Bold"
                                Text="Сводка твоего настроения сохранена"/>

                            <StackLayout
                                HorizontalOptions="Center"
                                Spacing="12"
                                Margin="0,30,0,0">

                                <Button 
                                    Command="{Binding Source={x:Reference this},Path=GoToTracker}"
                                    VerticalOptions="Start"
                                    HorizontalOptions="Center"
                                    WidthRequest="245"
                                    Style="{x:StaticResource yellow_btn}"
                                    Text="В трекер"/>

                                <Button 
                                    Command="{Binding Source={x:Reference this},Path=GoToMain}"
                                    VerticalOptions="Start"
                                    HorizontalOptions="Center"
                                    WidthRequest="245"
                                    Style="{x:StaticResource grey_cornered_btn}"
                                    Text="На главную"/>


                            </StackLayout>

                          

                        </StackLayout>
                    </Grid>
                </Frame>
            <!--</sh:Shadows>-->
        </Grid>
    </ContentPage.Content>
</ContentPage>