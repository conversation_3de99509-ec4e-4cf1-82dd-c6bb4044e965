# Triggero API Payment Tests

This test project is designed to test the PaySubscription endpoint in the Triggero API, specifically to debug issues with the PaymentsController.PaySubscription method.

## Setup

1. **Configuration**: The tests automatically use the same configuration as the Triggero API project:
   - Reads from `Triggero.Web/Triggero.Api/secrets.json`
   - Reads from `Triggero.Web/Triggero.Api/appsettings.json`
   - Reads from `Triggero.Web/Triggero.Api/appsettings.Development.json`

2. **No Separate Configuration Needed**: The test project shares the API project's configuration, ensuring consistency.

3. **Project Location**: This test project is located in the TriggeroYoumi repository under `Tests/Triggero.Api.PaymentTests/`

## Configuration (Optional)

You can optionally configure test-specific settings by adding a `TestSettings` section to your API project's `secrets.json`:

```json
{
  "TestSettings": {
    "UserId": 1,
    "AuthToken": "your-jwt-token-here",
    "ApiBaseUrl": "https://localhost:7013"
  }
}
```

If not specified, the tests will use sensible defaults.

## Authorization Token

The PaySubscription endpoint doesn't require authorization by default, but you can test with authorization by:

1. **Setting the token in test code**: Modify the `PaySubscription_WithAuthToken_ShouldWork` test and set the `testToken` variable
2. **Using configuration**: Add your JWT token to the `TestSettings:AuthToken` in secrets.json
3. **Generating a token**: Use the Account/Login endpoint to get a valid JWT token

## Running Tests

From the repository root directory:

```bash
# Run all payment tests
dotnet test Tests/Triggero.Api.PaymentTests/

# Run specific test
dotnet test Tests/Triggero.Api.PaymentTests/ --filter "PaySubscription_WithValidData_ShouldReturnCreatedPayment"

# Run with verbose output
dotnet test Tests/Triggero.Api.PaymentTests/ --logger "console;verbosity=detailed"

# Run from the Triggero.Web solution
cd Triggero.Web
dotnet test ../Tests/Triggero.Api.PaymentTests/
```

## Test Cases

1. **PaySubscription_WithValidData_ShouldReturnCreatedPayment**: Tests the basic functionality with valid data
2. **PaySubscription_WithInvalidUserID_ShouldHandleGracefully**: Tests error handling with invalid user ID
3. **PaySubscription_WithDifferentPaymentMethods_ShouldWork**: Tests different payment methods (Yookassa, AppStore, GooglePlay)
4. **PaySubscription_WithDifferentDurations_ShouldWork**: Tests different subscription durations (Month, Year)
5. **PaySubscription_WithAuthToken_ShouldWork**: Tests with JWT authorization token

## Debugging

The tests output detailed information including:
- HTTP response status codes
- Response content
- Payment IDs and confirmation URLs
- Error messages

Check the test output for detailed information about what's happening during the payment process.

## Common Issues

1. **Database Connection**: Make sure your database is accessible and contains valid test data
2. **UKassa Configuration**: Ensure UKassa credentials are properly configured in secrets.json
3. **User Data**: Make sure the test user ID exists in your database with proper subscription data
4. **Plan Options**: Ensure the plan option IDs used in tests exist in your Plans table

## Modifying Tests

To test with your specific data:

1. Update the `userID` in test methods to match a valid user in your database
2. Update `planOptionIds` to match valid plan options in your database
3. Set appropriate JWT tokens for authorization testing
4. Modify payment settings to match your business logic requirements
