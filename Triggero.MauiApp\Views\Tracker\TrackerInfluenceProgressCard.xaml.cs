﻿using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.MoodTracker;


namespace Triggero.Controls.Cards.Tracker
{

    public partial class TrackerInfluenceProgressCard : ContentView
    {
        private Factor _item;
        private double _progress;
        public TrackerInfluenceProgressCard(Factor item, double progress)
        {
            _progress = progress;
            _item = item;
            InitializeComponent();
            Load();
        }
        private async void Load()
        {
            titleLabel.Text = _item.GetLocalizedTitle(LanguageHelper.LangCode);
            img.Source = await ResorcesHelper.GetImageSource(_item.ImgPath);
            progressBar.Progress = _progress;
        }
    }
}