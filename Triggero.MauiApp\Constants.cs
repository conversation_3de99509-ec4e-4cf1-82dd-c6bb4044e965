﻿using MobileAPIWrapper;


namespace Triggero.MauiMobileApp
{
    public static class Constants
    {
        static string UrlContent => TriggeroMobileAPI.GetBaseUrl();

        public static readonly string UrlContentThumbnails = "https://api.triggero.ru/common/GetImage";

        public static readonly string SyncfusionKey =
            "MTc3NzE4NUAzMjMxMmUzMTJlMzQzMUpySjMxUFVxVTYzek5kdjcreWJROTZpTFIyd3FsdmlFODZ5a1JSS3NTeFk9";

        //public static string ApplePaymentLink = "Совершайте покупки на веб-сайте triggero.ru";
        public static string ApplePaymentLink = "Перейти на triggero.ru для выбора и оплаты подписки";

        public static string BuildContentUrl(string path)
        {
            if (UrlContent == null || path == null)
            {
                return null;
            }

            return UrlContent.TrimEnd('/') + "/" + path.TrimStart('/');
        }

        /// <summary>
        /// Требовать 1 рубль перед Триалом на Анроиде
        /// </summary>
        public static readonly bool RequirePrepayment = false;

        public static readonly bool EnableTutorial = true;

        public static bool IsFreeVersion
        {
            get
            {

#if WINDOWS && DEBUG
            return false;
#endif

                if (Device.RuntimePlatform == Device.Android)
                {
                    return false; //android is paid
                }

                return true;
            }
        }

        public static bool ShowReferenceLinks
        {
            get
            {
                if (Device.RuntimePlatform == Device.iOS)
                {
                    return true;
                }

                return false;
            }
        }

    }
}
