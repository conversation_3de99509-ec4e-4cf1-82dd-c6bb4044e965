﻿
using MobileAPIWrapper;

using System.Windows.Input;
using Triggero.Domain.Models;
using Triggero.MauiMobileApp.Extensions.Helpers;


namespace Triggero.MauiMobileApp.Views.Pages.Profile.Report
{

    public partial class SendReportPage : ContentPage
    {
        public SendReportPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            RenderValues();
        }

        private MoodTrackerReportSettings settings = new MoodTrackerReportSettings();
        public MoodTrackerReportSettings Settings
        {
            get { return settings; }
            set { settings = value; OnPropertyChanged(nameof(Settings)); }
        }

        #region Init

        private async void RenderValues()
        {
            var user = await AuthHelper.GetUser();
            emailLabel.Text = user.EmailToSendReports;
            periodLabel.Text = $"{Settings.From.ToString("dd.MM.yyyy")} - {Settings.To.ToString("dd.MM.yyyy")}";
        }

        #endregion

        #region Period

        private ICommand setPeriod;
        public ICommand SetPeriod
        {
            get => setPeriod ??= new RelayCommand(async obj =>
            {
                var page = new ReportPeriodPage();
                page.Selected += Page_PeriodSelected;
                App.OpenPage(page);
            });
        }

        private void Page_PeriodSelected(object sender, MoodTrackerReportSettings e)
        {
            Settings.From = e.From;
            Settings.To = e.To;
        }

        #endregion

        #region Email
        private ICommand setEmail;
        public ICommand SetEmail
        {
            get => setEmail ??= new RelayCommand(async obj =>
            {
                var page = new EmailToSendReportPage(emailLabel.Text);
                page.EmailSelected += Page_EmailSelected;
                App.OpenPage(page);
            });
        }

        private async void Page_EmailSelected(object sender, string e)
        {
            emailLabel.Text = e;
            await AuthHelper.SetEmailToSendReports(e);
        }
        #endregion

        private ICommand sendReport;
        public ICommand SendReport
        {
            get => sendReport ??= new RelayCommand(async obj =>
            {
                var user = await AuthHelper.GetUser();

                await TriggeroMobileAPI.GeneralMethods.UserMethods.UserMoodTrackerMethods.SendStatsToEmail(AuthHelper.UserId, user.EmailToSendReports, Settings);
                await DisplayAlert("Уведомление", "Отчет отправлен на почту", "Ок");
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }

    }
}