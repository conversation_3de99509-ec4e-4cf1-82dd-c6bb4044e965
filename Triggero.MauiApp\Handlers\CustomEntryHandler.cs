using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;

#if ANDROID
using Android.Graphics.Drawables;
using AndroidX.AppCompat.Widget;
using Microsoft.Maui.Platform;
#elif IOS
using UIKit;
using Foundation;
#elif WINDOWS
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml;
#endif

namespace Triggero.MauiMobileApp.Handlers
{
    public class CustomEntryHandler : EntryHandler
    {
#if ANDROID
        protected override void ConnectHandler(AppCompatEditText platformView)
        {
            base.ConnectHandler(platformView);

            ApplyCustomStyling(platformView);
        }

        private void ApplyCustomStyling(AppCompatEditText platformView)
        {
            // Create a completely transparent drawable
            var transparentDrawable = new ColorDrawable(Android.Graphics.Color.Transparent);

            // Remove background (underline) immediately and permanently
            platformView.Background = transparentDrawable;
            platformView.SetBackgroundDrawable(transparentDrawable);

            // Remove padding
            platformView.SetPadding(0, 0, 0, 0);

            // Remove any additional styling
            platformView.SetBackgroundColor(Android.Graphics.Color.Transparent);

            // Disable the background tint to prevent any native theming
            platformView.BackgroundTintList = null;
            platformView.BackgroundTintMode = null;

            // Use post to ensure this runs after any native focus handling
            platformView.Post(() =>
            {
                platformView.Background = transparentDrawable;
                platformView.SetBackgroundDrawable(transparentDrawable);
            });

            // Handle focus changes with immediate reapplication
            platformView.FocusChange += (sender, e) =>
            {
                if (sender is AppCompatEditText editText)
                {
                    // Use post to run after native focus handling
                    editText.Post(() =>
                    {
                        editText.Background = transparentDrawable;
                        editText.SetBackgroundDrawable(transparentDrawable);
                        editText.BackgroundTintList = null;
                    });
                }
            };
        }
#endif

#if IOS
        protected override void ConnectHandler(MauiTextField platformView)
        {
            base.ConnectHandler(platformView);
            
            // Remove border
            platformView.BorderStyle = UITextBorderStyle.None;
            
            // Remove padding by setting zero content insets
            platformView.Layer.SublayerTransform = CoreAnimation.CATransform3D.Identity;
            
            // Ensure no background
            platformView.BackgroundColor = UIColor.Clear;
            
            // Remove any additional padding
            platformView.LeftView = new UIView(new CoreGraphics.CGRect(0, 0, 0, 0));
            platformView.LeftViewMode = UITextFieldViewMode.Always;
            platformView.RightView = new UIView(new CoreGraphics.CGRect(0, 0, 0, 0));
            platformView.RightViewMode = UITextFieldViewMode.Always;
        }
#endif

#if WINDOWS
        protected override void ConnectHandler(TextBox platformView)
        {
            base.ConnectHandler(platformView);
            
            // Remove border and background
            platformView.BorderThickness = new Microsoft.UI.Xaml.Thickness(0);
            platformView.Background = null;
            
            // Remove padding
            platformView.Padding = new Microsoft.UI.Xaml.Thickness(0);
            
            // Remove focus visual
            platformView.UseSystemFocusVisuals = false;
            platformView.IsTabStop = true;
            
            // Remove any additional styling
            platformView.Style = null;
        }
#endif
    }
}
