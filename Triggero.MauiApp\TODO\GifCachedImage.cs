﻿using DrawnUi.Controls;
using DrawnUi.Views;

namespace Triggero.MauiMobileApp.Controls;

public class GifCachedImage : Canvas
{
    public SkiaGif Gif { get; protected set; }

    public static readonly BindableProperty SourceProperty = BindableProperty.Create(nameof(Source),
        typeof(string),
        typeof(GifCachedImage),
        string.Empty,
        propertyChanged: ApplySourceProperty);

    private static void ApplySourceProperty(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is GifCachedImage control)
        {
            control.Gif.Source = newValue as string;
        }
    }

    public string Source
    {
        get { return (string)GetValue(SourceProperty); }
        set { SetValue(SourceProperty, value); }
    }

    public GifCachedImage()
    {
        Gif = new SkiaGif();
        Content = Gif;
        Gif.Success += GifOnSuccess;
        Gif.Error += GifOnError;
    }

    public GifCachedImage(string source)
    {
        Gif = new SkiaGif()
        {
            
        };
        Content = Gif;
        Gif.Source = source;
        Gif.Success += GifOnSuccess;
        Gif.Error += GifOnError;
    }

    private void GifOnError(object? sender, Exception e)
    {
        Error?.Invoke(this, e);
    }

    private void GifOnSuccess(object? sender, string e)
    {
        Success?.Invoke(this, e);
    }

    /// <summary>
    /// Happens when loaded fine from `Source`. Will pass source as string.
    /// </summary>
    public event EventHandler<string> Success;

    /// <summary>
    /// Happens when loaded with error from `Source`. Will pass exception.
    /// </summary>
    public event EventHandler<Exception> Error;

}