﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models.Tests;



namespace Triggero.MauiMobileApp.Controls.Tests
{
	
	public partial class TestResultScales : ContentView
	{
		private Dictionary<TestScale, int> _scoresGroup;

        public TestResultScales(Dictionary<TestScale,int> scoresGroup)
		{
            _scoresGroup = scoresGroup;
            InitializeComponent();

			Render();
        }

		private void Render()
		{
            scalesLayout.Children.Clear();
			foreach (var scaleScore in _scoresGroup)
			{
				var scale = new TestResultScale(scaleScore.Key, scaleScore.Value);
				scalesLayout.Children.Add(scale);
            }

        }
	}
}