﻿using MobileAPIWrapper.MethodGroupings;
using MobileAPIWrapper.Methods;

namespace MobileAPIWrapper
{
    public static class TriggeroMobileAPI
    {
        public static AccountMethods Account { get; set; } = new();

        public static GeneralMethods GeneralMethods { get; set; } = new GeneralMethods();
        public static LibraryMethods LibraryMethods { get; set; } = new LibraryMethods();
        public static MessengersMethods MessengersMethods { get; set; } = new MessengersMethods();

        public static PaymentMethods Payment { get; set; } = new PaymentMethods();
        public static CommonMethods Common { get; set; } = new CommonMethods();
        public static TestsMethods TestsMethods { get; set; } = new TestsMethods();

        public static string AddBaseUrl(string path)
        {
            return HOST.TrimEnd('/') + "/" + path.TrimStart('/');
        }

        public static string GetBaseUrl()
        {
            return HOST.TrimEnd('/');
        }

        //private const string HOST = "https://localhost:7013/";
        private const string HOST = "https://api.triggero.ru/";
    }
}