﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#if ANDROID
using Android.Graphics.Drawables;
using Android.Views;
using AndroidX.Core.Content;
using AndroidX.DrawerLayout.Widget;
using AndroidX.Fragment.App;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Maui.Platform;
#endif

namespace Triggero.MobileMaui
{
    public class FixNavigationPage : NavigationPage
    {

        public FixNavigationPage(Page page) : base(page)
        {

        }

        protected override void OnHandlerChanged()
        {
            base.OnHandlerChanged();

#if ANDROID
            if (Handler != null && Handler.PlatformView is FragmentContainerView view)
            {
                try
                {
                    var rootView = view.Context?.GetActivity()?.Window?.DecorView;
                    if (rootView != null)
                    {
                        rootView.SetBackground(view.Background);
                    }

                    view.Background = null;
                    if (rootView != null)
                    {

                        Drawable[] layers = new Drawable[2];
                        layers[0] = new ColorDrawable(Android.Graphics.Color.ParseColor("#EEF0F1")); 
                        
                        LayerDrawable layerDrawable = new LayerDrawable(layers);

                        // Set the gravity for the image to be centered
                        int inset = 0; // You can adjust this value if you want an inset from the center
                        layerDrawable.SetLayerInset(1, inset, inset, inset, inset);
                        layerDrawable.SetLayerGravity(1, GravityFlags.Center);

                        // Set the drawable as the background of the root view
                        rootView.SetBackground(layerDrawable);
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            }
#endif
        }
    }
}