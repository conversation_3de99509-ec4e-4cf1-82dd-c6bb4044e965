﻿<?xml version="1.0" encoding="utf-8"?>

<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Auth.LoginByPhonePage"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
             x:Name="this">
    <ContentPage.Content>
        <Grid>


            <Image
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightbluegradientbg.png" />

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="200" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>


                <Grid Grid.Row="0">

                    <ImageButton
                        Command="{Binding Source={x:Reference this},Path=Close}"
                        Source="buttonbackbordered.png"
                        WidthRequest="56"
                        HeightRequest="56"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        Margin="20,0,0,0"
                        BackgroundColor="Transparent"
                        CornerRadius="0" />

                    <Label
                        TextColor="{x:StaticResource ColorText}"
                        FontSize="22"
                        VerticalOptions="End"
                        HorizontalOptions="Start"
                        FontAttributes="Bold"
                        Margin="20"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Auth.LoginByPhone.LoginByPhone}" />

                </Grid>

                <pancakeview:PancakeView
                    Grid.Row="1"
                    Padding="0"
                    StrokeShape="RoundRectangle 15,15,0,0"
                    BackgroundColor="#FFFFFF">

                    <Grid>
                        <StackLayout
                            Spacing="0"
                            Margin="20,20,20,0">

                            <Label
                                TextColor="{x:StaticResource ColorText}"
                                Opacity="0.5"
                                FontSize="14"
                                VerticalOptions="Start"
                                HorizontalOptions="Start"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Auth.LoginByPhone.Phone}" />

                            <!--grayTextEdit-->
                            <Grid
                                HeightRequest="50"
                                Margin="0,10,0,0">
                                <draw:Canvas HorizontalOptions="Fill" VerticalOptions="Fill">

                                    <draw:SkiaShape
                                        HorizontalOptions="Fill" VerticalOptions="Fill"
                                        UseCache="Operations"
                                        StrokeColor="#DEEAF6"
                                        StrokeWidth="1.5"
                                        CornerRadius="16" />

                                </draw:Canvas>

                                <Entry
                                    Margin="10,0"
                                    Keyboard="Telephone"
                                    Style="{x:StaticResource grayTextEdit}"
                                    VerticalOptions="Fill"
                                    HorizontalOptions="Fill"
                                    Text="{Binding Source={x:Reference this},Path=Phone,Mode=TwoWay}"
                                    PlaceholderColor="#989B9E"
                                    Placeholder="{Binding Source={x:Static mobile:App.This},Path=Interface.Auth.LoginByPhone.EnterPhone}" />
                            </Grid>

                            <Button
                                Command="{Binding Source={x:Reference this},Path=SendCode}"
                                Margin="0,40,0,0"
                                Style="{x:StaticResource yellow_btn}"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Auth.LoginByPhone.GoNext}" />


                        </StackLayout>
                    </Grid>
                </pancakeview:PancakeView>

            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>