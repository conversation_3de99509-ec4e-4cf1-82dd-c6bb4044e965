﻿using Triggero.MauiMobileApp.Extensions;
using Triggero.Models;
using Triggero.MauiMobileApp.Views.Pages.Library;

using Triggero.MauiMobileApp.Extensions.Helpers;

namespace Triggero.Controls.Cards.NextItemCard
{

    public partial class TopicNextItemCard : ContentView
    {
        private Topic _topic;

        public TopicNextItemCard()
        {
            InitializeComponent();
        }
        public TopicNextItemCard(Topic topic)
        {
            InitializeComponent();
            Build(topic);
        }

        public async void Build(Topic topic)
        {
            _topic = topic;

            titleLabel.Text = topic.GetLocalizedTitle(LanguageHelper.LangCode);
            var desc = topic.GetLocalizedDescription(LanguageHelper.LangCode);
            desc = StringExtensions.ExtractText(desc);
            if (desc.Length > 27 * 2)
            {
                desc = desc.Substring(0, 27 * 2) + "...";
            }
            descLabel.Text = desc;

            img.Source = await ResorcesHelper.GetImageSource(_topic.ImgPath);

        }

        private async void tapped(object sender, EventArgs e)
        {
            if (_topic != null)
            {
                App.OpenPage(new TopicPage(_topic));
            }
        }
    }
}