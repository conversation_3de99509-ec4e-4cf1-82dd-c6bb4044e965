﻿using MobileAPIWrapper;
using System.Threading.Tasks;
using System.Windows.Input;
using Triggero.MauiMobileApp.Extensions.Helpers;

namespace Triggero.MauiMobileApp.Views.Pages.Auth
{
    public partial class LoginByEmailPage : ContentPage
    {
        public LoginByEmailPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

#if DEBUG
        
        //unpaid
        //private string email = "<EMAIL>";
        //private string password = "Ali000nA";
        
        //paid
        private string email = "<EMAIL>";
        private string password = "new123";

#else
        private string email = "";
        private string password = "";
#endif

        public string Email
        {
            get { return email; }
            set
            {
                email = value;
                OnPropertyChanged();
            }
        }

        public string Password
        {
            get { return password; }
            set
            {
                password = value;
                OnPropertyChanged();
            }
        }

        private bool _IsBusy;

        public new bool IsBusy
        {
            get { return _IsBusy; }
            set
            {
                if (_IsBusy != value)
                {
                    _IsBusy = value;
                    OnPropertyChanged();
                }
            }
        }


        private RelayCommand tryAuth;

        public RelayCommand CommandLoginByEmail
        {
            get => tryAuth ??= new RelayCommand(async obj =>
            {
                if (IsBusy)
                    return;

                try
                {
                    IsBusy = true;
                    await Task.Delay(10);

                    var login = await TriggeroMobileAPI.Account.LoginWithEmail(Email, Password);

                    if (login != null)
                    {
                        AuthHelper.SetupAuthorization(login.Token);
                        var user = await TriggeroMobileAPI.Account.MyProfileFull();
                        if (user != null)
                        {
                            AuthHelper.Login(user, login.Token);
                            App.SetMainPage(new MainPage());
                        }
                        else
                        {
                            App.ShowToast("Не удалось загрузить профиль пользователя");
                        }
                    }
                    else
                    {
                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            errorLabel.IsVisible = true;
                            errorLabel.Text = App.This.Interface.Auth.LoginByEmail.InvalidLoginOrPassword;
                            App.ShowToast(App.This.Interface.Auth.LoginByEmail.InvalidLoginOrPassword);
                        });
                    }
                }
                catch (Exception e)
                {
                    Super.Log(e);
                    //App.ShowToast($"Ошибка: {e}");
                    App.ShowToast($"Ошибка авторизации");
                }
                finally
                {
                    IsBusy = false;
                }
            });
        }

        private RelayCommand goToRestorePage;

        public RelayCommand GoToRestorePage
        {
            get => goToRestorePage ??= new RelayCommand(async obj => { App.OpenPage(new EmailForgotPasswordPage()); });
        }


        private ICommand close;

        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    try
                    {
                        await App.Current.MainPage.Navigation.PopAsync();
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }
                });
            });
        }
    }
}