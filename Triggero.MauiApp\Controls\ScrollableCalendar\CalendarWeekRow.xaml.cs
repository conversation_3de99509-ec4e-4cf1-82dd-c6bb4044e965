﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices.ComTypes;
using System.Text;
using System.Threading.Tasks;




namespace Triggero.Custom.ScrollableCalendarItems
{
    
    public partial class CalendarWeekRow : ContentView
    {
        public DateTime DateStart { get; protected set; }
        public DateTime DateEnd { get; protected set; }


        private ScrollableCalendarV2 _parent;
        public CalendarWeekRow(DateTime startDate,ScrollableCalendarV2 parent)
        {
            DateStart = startDate;
            _parent = parent;

            InitializeComponent();
            Render();
        }

        public void SetCalendarSelection(DateTime from,DateTime to)
        {
            int startCol = -1;
            int colSpan = 0;

            foreach(var child in datesGrid.Children)
            {
                if(child is ScrollableCalendarV2DayButton btn
                    && btn.Day.Date >= from.Date && btn.Day.Date <= to.Date)
                {
                    if(startCol == -1)
                    {
                        startCol = Grid.GetColumn(btn);
                    }
                    colSpan++;
                } 
            }

            if(startCol == -1)
            {
                ClearCalendarSelection();
            }
            else
            {
                Grid.SetColumn(selectionFrame, startCol);
                Grid.SetColumnSpan(selectionFrame, colSpan);
                selectionFrame.IsVisible = true;
            }
       

        }
        public void ClearCalendarSelection()
        {
            selectionFrame.IsVisible = false;

            foreach (var child in datesGrid.Children)
            {
                if (child is ScrollableCalendarV2DayButton btn)
                {
                    btn.SetButtonSelection(false);
                }
            }
        }

        private void Render()
        {
            int dayOfWeekIndex = (int)DateStart.DayOfWeek;
            var date = DateStart;

            for (int i = dayOfWeekIndex; i < 7; i++)
            {
                if (date.Month == DateStart.Month)
                {
                    var btn = MakeButton(date);
                    Grid.SetColumn(btn, i);
                    datesGrid.Children.Add(btn);
                }

                date = date.AddDays(1);
            }
            DateEnd = date;
        }
        private ScrollableCalendarV2DayButton MakeButton(DateTime day)
        {
            var btn = new ScrollableCalendarV2DayButton(day)
            {
                HeightRequest = 36,
                WidthRequest = 36,
                HorizontalOptions = LayoutOptions.Center,
                VerticalOptions = LayoutOptions.Center
            };
            btn.Tapped += DayBtnTapped;
            return btn;
        }

        private void DayBtnTapped(object sender, DateTime e)
        {
            _parent.AfterButtonSelection(e);

            var btn = sender as ScrollableCalendarV2DayButton;
            btn.SetButtonSelection(true);

     
        }

    }
}