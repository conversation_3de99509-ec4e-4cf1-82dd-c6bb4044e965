﻿<?xml version="1.0" encoding="UTF-8"?>

<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:charts="clr-namespace:Syncfusion.Maui.Charts;assembly=Syncfusion.Maui.Charts"
            xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
             
             x:Name="this"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             HorizontalOptions="Fill"
             
             x:Class="Triggero.Controls.MoodTracker.TrackerMoodChart">
    <ContentView.Content>

        <StackLayout
            HorizontalOptions="Fill">

            <Label
                Margin="0,10,0,0"
                TextColor="{x:StaticResource ColorText}"
                HorizontalOptions="Start"
                FontSize="16"
                FontAttributes="Bold"
                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerMainPage.MoodChart}" />

            <Label
                TextColor="{x:StaticResource ColorTextGray}"

                HorizontalOptions="Fill"
                FontSize="12"
                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerMainPage.MoodChartDescription}" />


            <Grid
                Margin="0,30,0,0"
                HorizontalOptions="FillAndExpand"
                VerticalOptions="FillAndExpand">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="65" />
                </Grid.RowDefinitions>

                <Grid Grid.Row="0"
                      HorizontalOptions="Fill"
                      ColumnSpacing="0">

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="45" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>


                    <draw:Canvas 
                        Grid.ColumnSpan="2"
                        HorizontalOptions="Fill" VerticalOptions="Fill" BackgroundColor="White">
                        <draw:SkiaStack  Spacing="43">

                            <draw:SkiaShape
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"
                                      HorizontalOptions="Fill"
                                    HeightRequest="1" />

                            <draw:SkiaShape
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"
                                    HorizontalOptions="Fill"
                                    HeightRequest="1" />

                            <draw:SkiaShape
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"
                                    HorizontalOptions="Fill"
                                    HeightRequest="1" />
                            <draw:SkiaShape
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"
                                    HorizontalOptions="Fill"
                                    HeightRequest="1" />
                            <draw:SkiaShape
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"
                                    HorizontalOptions="Fill"
                                    HeightRequest="1" />
                            <draw:SkiaShape
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"
                                    HorizontalOptions="Fill"
                                    HeightRequest="1" />

                            <draw:SkiaShape
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"
                                    HorizontalOptions="Fill"
                                    HeightRequest="1" />

                        </draw:SkiaStack>
                    </draw:Canvas>
                    
                    <Grid Grid.Column="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="44" />
                            <RowDefinition Height="44" />
                            <RowDefinition Height="44" />
                            <RowDefinition Height="44" />
                            <RowDefinition Height="44" />
                            <RowDefinition Height="44" />
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0" HorizontalOptions="Fill">


                            <Image
                                Source="daymood1.png"
                                VerticalOptions="Center"
                                HorizontalOptions="Center"
                                HeightRequest="36"
                                WidthRequest="36" />
                        </Grid>

                        <Grid Grid.Row="1">
                            <Image
                                Source="daymood2.png"
                                VerticalOptions="Center"
                                HorizontalOptions="Center"
                                HeightRequest="36"
                                WidthRequest="36" />
             
                        </Grid>

                        <Grid Grid.Row="2">
                            <Image
                                Source="daymood3.png"
                                VerticalOptions="Center"
                                HorizontalOptions="Center"
                                HeightRequest="36"
                                WidthRequest="36" />
       
                        </Grid>

                        <Grid Grid.Row="3">
                            <Image
                                Source="daymood4.png"
                                VerticalOptions="Center"
                                HorizontalOptions="Center"
                                HeightRequest="36"
                                WidthRequest="36" />
           
                        </Grid>

                        <Grid Grid.Row="4">
                            <Image
                                Source="daymood5.png"
                                VerticalOptions="Center"
                                HorizontalOptions="Center"
                                HeightRequest="36"
                                WidthRequest="36" />
            
                        </Grid>

                        <Grid Grid.Row="5">
                            <Image
                                Source="daymood6.png"
                                VerticalOptions="Center"
                                HorizontalOptions="Center"
                                HeightRequest="36"
                                WidthRequest="36" />
       
                        </Grid>

                    </Grid>

                    
                    <Grid Grid.Column="1" HorizontalOptions="Fill" VerticalOptions="Fill">

                            <charts:SfCartesianChart
                            x:Name="chart"
                            Margin="10,0,0,0"
                            BackgroundColor="Transparent"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill">

                            <charts:SfCartesianChart.XAxes>
                                <charts:DateTimeAxis
                                    x:Name="dateAxis"
                                    Interval="1"
                                    ShowMajorGridLines="False"
                                    IntervalType="Days"
                                    IsVisible="True">
                                    <charts:DateTimeAxis.LabelStyle>
                                        <charts:ChartAxisLabelStyle
                                            FontSize="10"
                                            LabelFormat="dd"
                                            TextColor="#363B4099" />
                                    </charts:DateTimeAxis.LabelStyle>
                                    <charts:DateTimeAxis.AxisLineStyle>
                                        <charts:ChartLineStyle
                                            StrokeWidth="0"
                                            Stroke="Transparent" />
                                    </charts:DateTimeAxis.AxisLineStyle>
                                </charts:DateTimeAxis>
                            </charts:SfCartesianChart.XAxes>

                            <charts:SfCartesianChart.YAxes>
                                <charts:NumericalAxis
                                    Minimum="-0.5"
                                    Maximum="5.30"
                                    ShowMajorGridLines="False"
                                    IsVisible="False"
                                    Interval="1" />
                            </charts:SfCartesianChart.YAxes>

                            <charts:SplineSeries
                                Fill="#4D4D4D50"
                                Type="Monotonic"
                                ItemsSource="{Binding Source={x:Reference this},Path=Data,Mode=TwoWay}"
                                XBindingPath="Date"
                                YBindingPath="Mood" />

                            <!-- Mood level markers from 0 to 5 -->
                            <charts:LineSeries
                                Fill="Transparent"
                                ItemsSource="{Binding Source={x:Reference this},Path=Data5,Mode=TwoWay}"
                                XBindingPath="Date"
                                YBindingPath="Mood"
                                ShowDataLabels="False">
                                <charts:LineSeries.DataLabelSettings>
                                    <charts:CartesianDataLabelSettings>
                                        <charts:CartesianDataLabelSettings.LabelStyle>
                                            <charts:ChartDataLabelStyle
                                                Background="#FFCD62" />
                                        </charts:CartesianDataLabelSettings.LabelStyle>
                                    </charts:CartesianDataLabelSettings>
                                </charts:LineSeries.DataLabelSettings>
                            </charts:LineSeries>

                            <charts:LineSeries
                                Fill="Transparent"
                                ItemsSource="{Binding Source={x:Reference this},Path=Data4,Mode=TwoWay}"
                                XBindingPath="Date"
                                YBindingPath="Mood"
                                ShowDataLabels="False">
                                <charts:LineSeries.DataLabelSettings>
                                    <charts:CartesianDataLabelSettings>
                                        <charts:CartesianDataLabelSettings.LabelStyle>
                                            <charts:ChartDataLabelStyle
                                                Background="#F3D7A8" />
                                        </charts:CartesianDataLabelSettings.LabelStyle>
                                    </charts:CartesianDataLabelSettings>
                                </charts:LineSeries.DataLabelSettings>
                            </charts:LineSeries>

                            <charts:LineSeries
                                Fill="Transparent"
                                ItemsSource="{Binding Source={x:Reference this},Path=Data3,Mode=TwoWay}"
                                XBindingPath="Date"
                                YBindingPath="Mood"
                                ShowDataLabels="False">
                                <charts:LineSeries.DataLabelSettings>
                                    <charts:CartesianDataLabelSettings>
                                        <charts:CartesianDataLabelSettings.LabelStyle>
                                            <charts:ChartDataLabelStyle
                                                Background="#CFE4F7" />
                                        </charts:CartesianDataLabelSettings.LabelStyle>
                                    </charts:CartesianDataLabelSettings>
                                </charts:LineSeries.DataLabelSettings>
                            </charts:LineSeries>

                            <charts:LineSeries
                                Fill="Transparent"
                                ItemsSource="{Binding Source={x:Reference this},Path=Data2,Mode=TwoWay}"
                                XBindingPath="Date"
                                YBindingPath="Mood"
                                ShowDataLabels="False">
                                <charts:LineSeries.DataLabelSettings>
                                    <charts:CartesianDataLabelSettings>
                                        <charts:CartesianDataLabelSettings.LabelStyle>
                                            <charts:ChartDataLabelStyle
                                                Background="#AEDCEE" />
                                        </charts:CartesianDataLabelSettings.LabelStyle>
                                    </charts:CartesianDataLabelSettings>
                                </charts:LineSeries.DataLabelSettings>
                            </charts:LineSeries>

                            <charts:LineSeries
                                Fill="Transparent"
                                ItemsSource="{Binding Source={x:Reference this},Path=Data1,Mode=TwoWay}"
                                XBindingPath="Date"
                                YBindingPath="Mood"
                                ShowDataLabels="False">
                                <charts:LineSeries.DataLabelSettings>
                                    <charts:CartesianDataLabelSettings>
                                        <charts:CartesianDataLabelSettings.LabelStyle>
                                            <charts:ChartDataLabelStyle
                                                Background="#7AD1E2" />
                                        </charts:CartesianDataLabelSettings.LabelStyle>
                                    </charts:CartesianDataLabelSettings>
                                </charts:LineSeries.DataLabelSettings>
                            </charts:LineSeries>

                            <charts:LineSeries
                                Fill="Transparent"
                                ItemsSource="{Binding Source={x:Reference this},Path=Data0,Mode=TwoWay}"
                                XBindingPath="Date"
                                YBindingPath="Mood"
                                ShowDataLabels="False">
                                <charts:LineSeries.DataLabelSettings>
                                    <charts:CartesianDataLabelSettings>
                                        <charts:CartesianDataLabelSettings.LabelStyle>
                                            <charts:ChartDataLabelStyle
                                                Background="#02C0CD" />
                                        </charts:CartesianDataLabelSettings.LabelStyle>
                                    </charts:CartesianDataLabelSettings>
                                </charts:LineSeries.DataLabelSettings>
                            </charts:LineSeries>

                        </charts:SfCartesianChart>

                    </Grid>

                </Grid>

                <Grid Grid.Row="1"
                      HorizontalOptions="Fill"
                      Margin="7,0,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="1*" />
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0">
                        <StackLayout
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            Spacing="10">

                            <StackLayout
                                Spacing="8"
                                Orientation="Horizontal">
                                <Frame
                                    BackgroundColor="#FFCD62"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    HeightRequest="12"
                                    WidthRequest="12"
                                    CornerRadius="6"
                                    HasShadow="False"
                                    Padding="0" />
                                <Label
                                    Margin="0,-1,0,0"
                                    Opacity="0.6"
                                    VerticalOptions="Center"
                                    TextColor="#363B40"
                                    FontSize="10"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood5}" />
                            </StackLayout>

                            <StackLayout
                                Spacing="8"
                                Orientation="Horizontal">
                                <Frame
                                    BackgroundColor="#F3D7A8"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    HeightRequest="12"
                                    WidthRequest="12"
                                    CornerRadius="6"
                                    HasShadow="False"
                                    Padding="0" />
                                <Label
                                    Margin="0,-1,0,0"
                                    Opacity="0.6"
                                    VerticalOptions="Center"
                                    TextColor="#363B40"
                                    FontSize="10"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood4}" />
                            </StackLayout>

                            <StackLayout
                                Spacing="8"
                                Orientation="Horizontal">
                                <Frame
                                    BackgroundColor="#CFE4F7"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    HeightRequest="12"
                                    WidthRequest="12"
                                    CornerRadius="6"
                                    HasShadow="False"
                                    Padding="0" />
                                <Label
                                    Margin="0,-1,0,0"
                                    Opacity="0.6"
                                    VerticalOptions="Center"
                                    TextColor="#363B40"
                                    FontSize="10"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood3}" />
                            </StackLayout>

                        </StackLayout>
                    </Grid>

                    <Grid Grid.Column="1">
                        <StackLayout
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            Spacing="10">

                            <StackLayout
                                Spacing="8"
                                Orientation="Horizontal">

                                <Frame
                                    BackgroundColor="#AEDCEE"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    HeightRequest="12"
                                    WidthRequest="12"
                                    CornerRadius="6"
                                    HasShadow="False"
                                    Padding="0" />

                                <Label
                                    Margin="0,-1,0,0"
                                    Opacity="0.6"
                                    VerticalOptions="Center"
                                    TextColor="#363B40"
                                    FontSize="10"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood2}" />
                            </StackLayout>

                            <StackLayout
                                Spacing="8"
                                Orientation="Horizontal">
                                <Frame
                                    BackgroundColor="#7AD1E2"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    HeightRequest="12"
                                    WidthRequest="12"
                                    CornerRadius="6"
                                    HasShadow="False"
                                    Padding="0" />
                                <Label
                                    Margin="0,-1,0,0"
                                    Opacity="0.6"
                                    VerticalOptions="Center"
                                    TextColor="#363B40"
                                    FontSize="10"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood1}" />
                            </StackLayout>

                            <StackLayout
                                Spacing="8"
                                Orientation="Horizontal">
                                <Frame
                                    BackgroundColor="#02C0CD"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    HeightRequest="12"
                                    WidthRequest="12"
                                    CornerRadius="6"
                                    HasShadow="False"
                                    Padding="0" />
                                <Label
                                    Margin="0,-1,0,0"
                                    Opacity="0.6"
                                    VerticalOptions="Center"
                                    TextColor="#363B40"
                                    FontSize="10"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood0}" />
                            </StackLayout>

                        </StackLayout>
                    </Grid>

                </Grid>

            </Grid>

        </StackLayout>

    </ContentView.Content>
</ContentView>