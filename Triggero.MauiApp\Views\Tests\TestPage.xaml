﻿<?xml version="1.0" encoding="utf-8"?>

<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Tests.TestPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:custom="clr-namespace:Triggero.MauiMobileApp.Custom"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:views="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="using:Triggero.MauiMobileApp.Views.Drawn"
    x:Name="this"
    BackgroundColor="White"
    NavigationPage.HasNavigationBar="False">
    <ContentPage.Content>

        <Grid RowSpacing="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition Height="70" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid
                Grid.Row="1">

                <Grid>

                    <Grid
                        Margin="0,0,0,0"
                        BackgroundColor="White"
                        HeightRequest="55"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        WidthRequest="55">

                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=Close}" />
                        </Grid.GestureRecognizers>

                        <ImageButton
                            BackgroundColor="Transparent"
                            Command="{Binding Source={x:Reference this}, Path=Close}"
                            CornerRadius="0"
                            HeightRequest="20"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            Source="arrowback.png"
                            VerticalOptions="Center"
                            WidthRequest="12" />

                    </Grid>


                    <Label
                        Margin="0,0,0,0"
                        HorizontalOptions="Center"
                        Style="{x:StaticResource StyleHeaderNavigation}"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Tests.Tests}"
                        VerticalOptions="Center" />

                    <!--BTN FAV-->
                    <views:Canvas
                        Gestures="Enabled"
                        HeightRequest="55"
                        HorizontalOptions="End"
                        VerticalOptions="Center"
                        WidthRequest="55">

                        <drawn:FavCheck
                            UseCache="Operations"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill"
                            x:Name="favoriteRb"
                            Toggled="toggleFavorite" />

                    </views:Canvas>

                    <!--<Grid
                        Margin="0,0,0,0"
                        HeightRequest="55"
                        HorizontalOptions="End"
                        VerticalOptions="Center"
                        WidthRequest="55">
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Tapped="toggleFavorite" />
                        </Grid.GestureRecognizers>
                        <RadioButton
                            x:Name="favoriteRb"
                            CornerRadius="0"
                            HeightRequest="24"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            Style="{x:StaticResource favorite_hearted_rb}"
                            VerticalOptions="Center"
                            WidthRequest="24" />
                    </Grid>-->

                    <BoxView
                        BackgroundColor="{x:StaticResource ColorText}"
                        HeightRequest="0.5"
                        HorizontalOptions="Fill"
                        VerticalOptions="End" />

                </Grid>

            </Grid>

            <Grid Grid.Row="2">

                <ScrollView
                    Margin="0,0,0,0"
                    VerticalScrollBarVisibility="Never">

                    <StackLayout
                        x:Name="ContentStack"
                        HorizontalOptions="Fill"
                        Opacity="0.001">

                        <Label
                            x:Name="titleLabel"
                            Margin="20,30,20,0"
                            FontAttributes="Bold"
                            FontSize="22"
                            HorizontalOptions="Start"
                            Text=""
                            TextColor="{x:StaticResource ColorText}" />

                        <Grid
                            Margin="20,20,20,0"
                            HeightRequest="55">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*" />
                                <ColumnDefinition Width="1*" />
                            </Grid.ColumnDefinitions>

                            <StackLayout
                                Grid.Column="0"
                                Orientation="Horizontal"
                                Spacing="8">

                                <Image
                                    HeightRequest="16"
                                    HorizontalOptions="Start"
                                    Source="helpcircleaqua.png"
                                    VerticalOptions="Center"
                                    WidthRequest="16" />

                                <StackLayout
                                    HorizontalOptions="Start"
                                    Spacing="5"
                                    VerticalOptions="Center">
                                    <Label
                                        Margin="0,0,0,0"
                                        FontSize="12"
                                        HorizontalOptions="Start"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Tests.TestVolume}"
                                        TextColor="{x:StaticResource ColorText}"
                                        VerticalOptions="Start" />
                                    <Label
                                        Margin="0,0,0,0"
                                        FontSize="12"

                                        HorizontalOptions="Start"
                                        TextColor="{x:StaticResource ColorPrimary}"
                                        VerticalOptions="Start">
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <FormattedString.Spans>
                                                    <Span
                                                        Text="{Binding Source={x:Reference this}, Path=Test.Questions.Count, Mode=OneWay}" />
                                                    <Span Text=" " />
                                                    <Span
                                                        x:Name="testQuestionTextLabel"
                                                        Text="" />
                                                </FormattedString.Spans>
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>
                                </StackLayout>

                            </StackLayout>


                            <StackLayout
                                Grid.Column="1"
                                Orientation="Horizontal"
                                Spacing="8">
                                <Image
                                    HeightRequest="16"
                                    HorizontalOptions="Start"
                                    Source="timecircleaqua.png"
                                    VerticalOptions="Center"
                                    WidthRequest="16" />

                                <StackLayout
                                    HorizontalOptions="Start"
                                    Spacing="5"
                                    VerticalOptions="Center">
                                    <Label
                                        Margin="0,0,0,0"
                                        FontSize="12"

                                        HorizontalOptions="Start"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Tests.PassingTime}"
                                        TextColor="{x:StaticResource ColorText}"
                                        VerticalOptions="Start" />
                                    <Label
                                        Margin="0,0,0,0"
                                        FontSize="12"

                                        HorizontalOptions="Start"
                                        TextColor="{x:StaticResource ColorPrimary}"
                                        VerticalOptions="Start">
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <FormattedString.Spans>
                                                    <Span
                                                        Text="{Binding Source={x:Reference this}, Path=Test.PassingTimeInMinutes, Mode=OneWay}" />
                                                    <Span Text=" " />
                                                    <Span
                                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Tests.MinutesAbbrevated}" />
                                                </FormattedString.Spans>
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>
                                </StackLayout>

                            </StackLayout>


                        </Grid>

                        <BoxView
                            Margin="0,20,0,0"
                            BackgroundColor="Black"
                            HeightRequest="1"
                            HorizontalOptions="Fill"
                            Opacity="0.2"
                            VerticalOptions="Start" />

                        <StackLayout
                            x:Name="mainStackLayout"
                            Margin="20,40,20,0">

                            <Frame
                                Margin="0,0,0,0"
                                Padding="0"
                                BackgroundColor="Transparent"
                                CornerRadius="16"
                                HasShadow="False"
                                HeightRequest="250"
                                HorizontalOptions="Fill"
                                IsClippedToBounds="True"
                                VerticalOptions="Start">

                                <Image
                                    x:Name="img"
                                    Aspect="AspectFill"
                                    HorizontalOptions="FillAndExpand"
                                    VerticalOptions="FillAndExpand" />

                            </Frame>


                            <custom:AlfaHtmlLabel
                                x:Name="descLabel"
                                Margin="0,25,0,0" />

                            <Button
                                Margin="0,25,0,40"
                                Command="{Binding Source={x:Reference this}, Path=StartTest}"
                                Style="{x:StaticResource yellow_btn}"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Tests.StartTest}"
                                VerticalOptions="Start" />


                        </StackLayout>


                    </StackLayout>
                </ScrollView>
            </Grid>


        </Grid>
    </ContentPage.Content>
</ContentPage>