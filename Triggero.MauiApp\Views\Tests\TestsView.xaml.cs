﻿using AppoMobi.Maui.Gestures;

using System.Linq;
using System.Windows.Input;
using Triggero.MauiMobileApp.Views.Pages;


namespace Triggero.MauiMobileApp.Views
{

    public partial class TestsView : ContentView
    {
        public TestsView()
        {
            InitializeComponent();
        }

        public bool IsRendered { get; private set; }
        public void Render()
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                //var items = await ApplicationState.Data.GetTestCategories();
                //items.Insert(0, new TestCategory
                //{
                //    Title = App.This.Interface.Tests.AllTests,
                //    Description = App.This.Interface.Tests.AllTestsDescription,
                //    ImgPath = "/built_in/images/allTests.png"
                //});

                //collection.ItemsSource = items;

                IsRendered = true;
            });

        }

        private ICommand goToFavorites;
        public ICommand GoToFavorites
        {
            get => goToFavorites ??= new RelayCommand(obj =>
            {
                if (TouchEffect.CheckLockAndSet())
                    return;


                MainThread.BeginInvokeOnMainThread(() =>
                {
                    var page = App.Current.MainPage.Navigation.NavigationStack.FirstOrDefault(o => o is MainPage) as MainPage;
                    page.SetView(new FavoritesView());

                });

            });
        }
        private ICommand goToSearch;
        public ICommand GoToSearch
        {
            get => goToSearch ??= new RelayCommand(obj =>
            {
                if (TouchEffect.CheckLockAndSet())
                    return;

                App.OpenPage(new SearchPage());
            });
        }
    }
}