﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Practices;



namespace Triggero.Controls.Chat.AdviceAttachments
{

    public partial class PracticeAdviceAttachment : ContentView
    {
        private Practice _practice;
        public PracticeAdviceAttachment(Practice practice)
        {
            _practice = practice;
            InitializeComponent();

            Load();
        }

        private void Load()
        {
            titleLabel.Text = _practice.GetLocalizedTitle(LanguageHelper.LangCode);
            img.Source = Constants.BuildContentUrl(_practice.IconImgPath);
            minutesLabel.Text = _practice.PassingTimeInMinutes.ToString();
        }


        public event EventHandler<Practice> Clicked;
        private void onClick(object sender, EventArgs e)
        {
            Clicked?.Invoke(this, _practice);
        }
    }
}