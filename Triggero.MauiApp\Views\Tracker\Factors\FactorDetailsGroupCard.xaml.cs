﻿using System.Collections.Generic;
using System.Linq;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.MoodTracker;
using Triggero.Models.MoodTracker.User;


namespace Triggero.Controls.Cards.Tracker.Factors
{

    public partial class FactorDetailsGroupCard : ContentView
    {
        public FactorDetailsGroupCard(MoodTrackerInfluence factor)
        {
            Factor = factor;
            InitializeComponent();
            Load();
        }

        private MoodTrackerInfluence factor;
        public MoodTrackerInfluence Factor
        {
            get { return factor; }
            set { factor = value; OnPropertyChanged(nameof(Factor)); }
        }

        #region Init
        private async void Load()
        {
            titleLabel.Text = Factor.Factor.GetLocalizedTitle(LanguageHelper.LangCode);

            var detals = await ApplicationState.Data.GetFactorDetails();
            var factorDetails = new List<FactorDetail>(detals.Where(o => o.FactorId == Factor.FactorId));

            int col = 0;
            int row = 0;
            gridLayout.Children.Clear();

            foreach (var detail in factorDetails)
            {
                if (col > 2)
                {
                    col = 0;
                    row++;
                    gridLayout.RowDefinitions.Add(new RowDefinition() { Height = 104 });
                }

                var view = new FactorDetailCard(detail)
                {
                    WidthRequest = 104,
                    HeightRequest = 104,
                    HorizontalOptions = LayoutOptions.Center,
                    VerticalOptions = LayoutOptions.Center
                };
                view.Tapped += View_Tapped;
                Grid.SetColumn(view, col);
                Grid.SetRow(view, row);
                gridLayout.Children.Add(view);

                col++;
            }

            this.HeightRequest = 20 + 20; //frame top and bottom padding
            this.HeightRequest += (row + 1) * 104; //grid rows
            this.HeightRequest += row * 14; //row spacings
            this.HeightRequest += 25; // header label
            this.HeightRequest += 12; // stack layout spacing
        }
        #endregion

        #region Selection
        private List<FactorDetail> selectedDetails = new List<FactorDetail>();
        private void View_Tapped(object sender, FactorDetail e)
        {
            var card = sender as FactorDetailCard;
            if (selectedDetails.Count >= 3 && !card.IsSelected) return;

            card.IsSelected = !card.IsSelected;
            if (!card.IsSelected)
            {
                selectedDetails.Remove(e);
            }
            else
            {
                selectedDetails.Add(e);
            }
        }

        public bool ValidateFactor()
        {
            return selectedDetails.Any();
        }
        public void BuildFactor()
        {
            Factor.FactorDetails.Clear();
            foreach (var detail in selectedDetails)
            {
                Factor.FactorDetails.Add(detail);
            }
        }
        #endregion

    }
}