﻿using Triggero.MauiPort.Models;
using Triggero.Models.Practices;

namespace Triggero.Controls.Cards.TasksForToday
{

    public partial class PracticeRecommendation : BaseRecommendationView
    {
        private Practice _practice;
        public PracticeRecommendation(RecommendationModel recommendation) : base(recommendation)
        {
            _practice = recommendation.Practice;
            InitializeComponent();
            Load();
        }
        private void Load()
        {
            titleLabel.Text = _practice.GetLocalizedTitle(LanguageHelper.LangCode);
            minutesSpan.Text = $"{_practice.PassingTimeInMinutes}";
            img.Source = Constants.BuildContentUrl(_practice.IconImgPath);
        }
    }
}