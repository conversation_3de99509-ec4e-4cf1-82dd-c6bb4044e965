﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Subscriptions.BuyCustomPlanPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this">
    <ContentPage.Content>


        <Grid
            RowSpacing="0"
            VerticalOptions="FillAndExpand">

            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Image
                Grid.RowSpan="2"
                Aspect="Fill"
                HorizontalOptions="Fill"
                Source="trialstartbg.png"
                VerticalOptions="Fill" />

            <ScrollView Grid.Row="1">
                <Grid>

                    <StackLayout>

                        <Grid
                            HeightRequest="530"
                            VerticalOptions="Start">

                            <ImageButton
                                Margin="0,50,25,0"
                                BackgroundColor="Transparent"
                                Command="{Binding Source={x:Reference this}, Path=Close}"
                                CornerRadius="0"
                                HeightRequest="14"
                                HorizontalOptions="End"
                                Opacity="0.5"
                                Source="close.png"
                                VerticalOptions="Start"
                                WidthRequest="14" />

                            <StackLayout
                                Margin="0,0,0,0"
                                HorizontalOptions="Fill"
                                Spacing="12"
                                VerticalOptions="End">

                                <Label
                                    FontAttributes="Bold"
                                    FontSize="17"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SubscriptionMain.TextCustomPlan}"
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Center"
                                    WidthRequest="226" />

                                <Image
                                    HeightRequest="20"
                                    HorizontalOptions="Center"
                                    Source="logotitleaqua.png"
                                    VerticalOptions="Center"
                                    WidthRequest="142" />


                                <StackLayout
                                    x:Name="planOptionsLayout"
                                    Margin="32,32,32,0"
                                    HeightRequest="-1"
                                    HorizontalOptions="Fill"
                                    Spacing="15"
                                    VerticalOptions="Center">

                                </StackLayout>

                            </StackLayout>

                        </Grid>

                        <Grid
                            Margin="20,0,20,0"
                            ColumnSpacing="12"
                            HeightRequest="200"
                            VerticalOptions="Start">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*" />
                                <ColumnDefinition Width="1*" />
                                <ColumnDefinition Width="1*" />
                            </Grid.ColumnDefinitions>


                            <Grid
                                x:Name="monthPlanSlot"
                                Grid.Column="0">

                            </Grid>

                            <Grid
                                x:Name="threeMonthsPlanSlot"
                                Grid.Column="1">

                            </Grid>

                            <Grid
                                x:Name="yearPlanSlot"
                                Grid.Column="2">

                            </Grid>



                        </Grid>

                        <Grid
                            HeightRequest="180"
                            VerticalOptions="Start">

                            <StackLayout
                                Margin="20,0,20,0"
                                Spacing="0">

                                <Button
                                    Margin="0,0,0,0"
                                    Command="{Binding Source={x:Reference this}, Path=Pay}"
                                    FontAttributes="Bold"
                                    Style="{x:StaticResource yellow_btn}"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SubscriptionMain.Pay}"
                                    VerticalOptions="Start" />


                                <Grid Margin="0,50,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="1*" />
                                        <ColumnDefinition Width="1*" />
                                        <ColumnDefinition Width="1*" />
                                    </Grid.ColumnDefinitions>

                                    <Grid Grid.Column="0">

                                        <Label
                                            FontSize="10"
                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Opacity="0.5"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SubscriptionMain.Terms}"
                                            TextColor="{x:StaticResource ColorText}"
                                            TextDecorations="Underline"
                                            WidthRequest="80">
                                            <Label.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=GoToTerms}" />
                                            </Label.GestureRecognizers>
                                        </Label>

                                    </Grid>

                                    <Grid Grid.Column="1">

                                        <Label
                                            FontSize="10"
                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Opacity="0.5"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SubscriptionMain.EULA}"
                                            TextColor="{x:StaticResource ColorText}"
                                            TextDecorations="Underline"
                                            WidthRequest="80">
                                            <Label.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=GoToEULA}" />
                                            </Label.GestureRecognizers>
                                        </Label>

                                    </Grid>

                                    <Grid Grid.Column="2">
                                        <Label
                                            FontSize="10"
                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Opacity="0.5"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.SubscriptionMain.Privacy}"
                                            TextColor="{x:StaticResource ColorText}"
                                            TextDecorations="Underline"
                                            WidthRequest="80">
                                            <Label.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=GoToPrivacy}" />
                                            </Label.GestureRecognizers>
                                        </Label>
                                    </Grid>

                                </Grid>


                            </StackLayout>

                        </Grid>



                    </StackLayout>






                </Grid>
            </ScrollView>

        </Grid>




    </ContentPage.Content>
</ContentPage>