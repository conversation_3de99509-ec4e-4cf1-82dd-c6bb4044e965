﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="Triggero.MauiMobileApp.Views.FavoritesView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero;assembly=Triggero.MauiMobileApp"
    xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:viewModels="clr-namespace:Triggero.MauiMobileApp.ViewModels"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="this"
    x:DataType="viewModels:FavoritesViewModel">
    <ContentView.Content>

        <Grid
            Padding="0"
            BackgroundColor="#F5FBFF"
            RowSpacing="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="150" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">

                <StackLayout
                    Margin="20,0,0,30"
                    HorizontalOptions="Start"
                    Orientation="Horizontal"
                    Spacing="20"
                    VerticalOptions="End">

                    <ImageButton
                        Grid.Column="0"
                        BackgroundColor="Transparent"
                        Command="{Binding Source={x:Reference this}, Path=GoBack}"
                        CornerRadius="0"
                        HeightRequest="40"
                        HorizontalOptions="Center"
                        Source="buttonbackbordered.png"
                        VerticalOptions="Center"
                        WidthRequest="40" />


                    <Label
                        Margin="0,-3,0,0"
                        FontAttributes="Bold"
                        HorizontalOptions="Start"
                        Style="{x:StaticResource StyleHeaderText}"
                        Text="{Binding Title}"
                        VerticalOptions="Center" />

                </StackLayout>

            </Grid>

            <pancakeview:PancakeView
                Grid.Row="1"
                Padding="0"
                BackgroundColor="#FFFFFF"
                StrokeShape="RoundRectangle 15,15,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="130" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0">
                        <Grid Margin="20,0,20,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*" />
                                <ColumnDefinition Width="1*" />
                                <ColumnDefinition Width="1*" />
                                <ColumnDefinition Width="1*" />
                            </Grid.ColumnDefinitions>

                            <RadioButton
                                Grid.Column="0"
                                CheckedChanged="onExercisesChecked"
                                HeightRequest="90"
                                HorizontalOptions="Center"
                                IsChecked="True"
                                Style="{x:StaticResource library_exercises_rb}"
                                VerticalOptions="Center"
                                WidthRequest="75" />

                            <RadioButton
                                Grid.Column="1"
                                CheckedChanged="onPracticeChecked"
                                HeightRequest="90"
                                HorizontalOptions="Center"
                                Style="{x:StaticResource practices_exercises_rb}"
                                VerticalOptions="Center"
                                WidthRequest="75" />

                            <RadioButton
                                Grid.Column="2"
                                CheckedChanged="onTopicsChecked"
                                HeightRequest="90"
                                HorizontalOptions="Center"
                                Style="{x:StaticResource practices_topics_rb}"
                                VerticalOptions="Center"
                                WidthRequest="75" />

                            <RadioButton
                                Grid.Column="3"
                                CheckedChanged="onTestsChecked"
                                HeightRequest="90"
                                HorizontalOptions="Center"
                                Style="{x:StaticResource library_tests_rb}"
                                VerticalOptions="Center"
                                WidthRequest="75" />

                        </Grid>
                    </Grid>

                    <!--<Grid
                        Grid.Row="1"
                        Margin="20,0,20,0">

                        <Grid>

                            <Grid.Style>
                                <Style TargetType="Grid">
                                    <Style.Triggers>
                                        <DataTrigger
                                            Binding="{Binding Source={x:Reference this}, Path=FavoriteSectionType}"
                                            TargetType="Grid"
                                            Value="Exercises">
                                            <Setter Property="IsVisible" Value="True" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Grid.Style>

                            <CollectionView >
                                <CollectionView.ItemTemplate>
                                    <DataTemplate>
                                        <templates:ExerciseCard
                                            Padding="0,6,0,0"
                                            HeightRequest="104"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="Start" />

                                    </DataTemplate>
                                </CollectionView.ItemTemplate>
                            </CollectionView>

                        </Grid>

                    </Grid>-->

                    <!--  DRAWN  -->
                    <draw:Canvas
                        Tag="Favs"
                        x:Name="DrawnCanvas"
                        Grid.Row="1"
                        Margin="10,6,10,0"
                        Gestures="Lock"
                        RenderingMode="Accelerated"
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill">

                        <draw:SkiaLayout
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill">

                            <draw:SkiaScroll
                                FrictionScrolled="0.35"
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill">

                                <draw:SkiaLayout
                                    x:Name="StackCells"
                                    Padding="0,0,0,0"
                                    HorizontalOptions="Fill"
                                    ItemTemplate="{Binding ItemTemplate}"
                                    ItemsSource="{Binding Items}"
                                    RecyclingTemplate="Enabled"
                                    Spacing="-8"
                                    Type="Column"
                                    VirtualisationInflated="350" />

                            </draw:SkiaScroll>

                            <!--  FPS  -->
                            <draw:SkiaLabelFps
                                Margin="0,0,4,84"
                                BackgroundColor="DarkRed"
                                ForceRefresh="False"
                                HorizontalOptions="End"
                                IsVisible="{x:Static triggeroV2:Globals.ShowFPS}"
                                Rotation="-45"
                                TextColor="White"
                                VerticalOptions="End" />

                        </draw:SkiaLayout>

                    </draw:Canvas>

                </Grid>
            </pancakeview:PancakeView>


        </Grid>
    </ContentView.Content>
</ContentView>