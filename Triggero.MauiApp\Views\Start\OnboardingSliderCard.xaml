﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Cards.Sliders.OnboardingSliderCard"
             x:Name="this">
  <ContentView.Content>
      <StackLayout Spacing="0">
            <Image
                x:Name="img"
                Source="{Binding Source={x:Reference this},Path=HelloSliderItem.ImgPath}"
                WidthRequest="242"
                HeightRequest="269"
                VerticalOptions="Start"
                HorizontalOptions="Center"/>
            <Label 
                x:Name="textLabel"
                Margin="0,40,0,0"
                TextColor="{x:StaticResource ColorText}"
                FontSize="17"
                VerticalOptions="Start"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                Text="{Binding Source={x:Reference this},Path=HelloSliderItem.Text}"/>
        </StackLayout>
  </ContentView.Content>
</ContentView>