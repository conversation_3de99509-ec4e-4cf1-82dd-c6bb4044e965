using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.PushNotifications;

namespace Triggero.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DebugController : ControllerBase
    {
        private readonly DatabaseContext _db;

        public DebugController(DatabaseContext db)
        {
            _db = db;
        }

        /// <summary>
        /// Send a test push notification to a specific user
        /// </summary>
        [HttpPost("push/{userId}")]
        public async Task<IActionResult> SendPushToUser(int userId, string message = "This is a test push notification")
        {
            string title = "Test Notification";
            try
            {
                var user = await _db.Users
                    .Include(u => u.Devices)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                {
                    return NotFound($"User with ID {userId} not found");
                }

                if (!user.Devices.Any())
                {
                    return BadRequest($"User {userId} has no registered devices");
                }

                var results = new List<object>();

                foreach (var device in user.Devices)
                {
                    try
                    {
                        PushNotificationsHelper.SendNotification(device.FirebaseToken, title, message);
                        results.Add(new
                        {
                            DeviceId = device.Id,
                            Token = device.FirebaseToken?.Substring(0, Math.Min(20, device.FirebaseToken?.Length ?? 0)) + "...",
                            Status = "Sent"
                        });
                    }
                    catch (Exception ex)
                    {
                        results.Add(new
                        {
                            DeviceId = device.Id,
                            Token = device.FirebaseToken?.Substring(0, Math.Min(20, device.FirebaseToken?.Length ?? 0)) + "...",
                            Status = "Failed",
                            Error = ex.Message
                        });
                    }
                }

                return Ok(new
                {
                    UserId = userId,
                    Title = title,
                    Message = message,
                    DeviceCount = user.Devices.Count,
                    Results = results
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Error = ex.Message });
            }
        }

/*
        /// <summary>
        /// Send a test push notification to all users (be careful!)
        /// </summary>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <returns>Result of the push notification send</returns>
        [HttpPost("send-push-to-all")]
        public async Task<IActionResult> SendPushToAll(string title = "Test Notification", string message = "This is a test push notification to all users")
        {
            try
            {
                var users = await _db.Users
                    .Include(u => u.Devices)
                    .Include(u => u.NotificationSettings)
                    .Where(u => u.Devices.Any())
                    .ToListAsync();

                var totalDevices = 0;
                var successCount = 0;
                var failCount = 0;

                foreach (var user in users)
                {
                    foreach (var device in user.Devices)
                    {
                        totalDevices++;
                        try
                        {
                            PushNotificationsHelper.SendNotification(device.FirebaseToken, title, message);
                            successCount++;
                        }
                        catch (Exception)
                        {
                            failCount++;
                        }
                    }
                }

                return Ok(new
                {
                    Title = title,
                    Message = message,
                    TotalUsers = users.Count,
                    TotalDevices = totalDevices,
                    SuccessCount = successCount,
                    FailCount = failCount
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Error = ex.Message });
            }
        }
*/
        /// <summary>
        /// Get user devices and their Firebase tokens for debugging
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>User device information</returns>
        [HttpGet("user-devices/{userId}")]
        public async Task<IActionResult> GetUserDevices(int userId)
        {
            try
            {
                var user = await _db.Users
                    .Include(u => u.Devices)
                    .Include(u => u.NotificationSettings)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                {
                    return NotFound($"User with ID {userId} not found");
                }

                return Ok(new
                {
                    UserId = userId,
                    DeviceCount = user.Devices.Count,
                    NotificationSettings = new
                    {
                        user.NotificationSettings?.ShouldNotifyNewTests,
                        user.NotificationSettings?.ShouldNotifyNewExercises,
                        user.NotificationSettings?.ShouldNotifyNewPractices,
                        user.NotificationSettings?.ShouldNotifyNewPosts,
                        user.NotificationSettings?.ShouldNotifyBreathTime,
                        user.NotificationSettings?.ShouldNotifyTrackerFilling
                    },
                    Devices = user.Devices.Select(d => new
                    {
                        d.Id,
                        Token = d.FirebaseToken?.Substring(0, Math.Min(20, d.FirebaseToken?.Length ?? 0)) + "...",
                        FullToken = d.FirebaseToken, // Only for debugging - remove in production
                        d.CreatedAt
                    })
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Error = ex.Message });
            }
        }
    }
}
