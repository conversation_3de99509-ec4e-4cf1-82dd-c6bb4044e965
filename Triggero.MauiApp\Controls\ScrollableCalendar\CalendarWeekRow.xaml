﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Custom.ScrollableCalendarItems.CalendarWeekRow">
  <ContentView.Content>
        <Grid x:Name="datesGrid"
              ColumnSpacing="13">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="36"/>
                <ColumnDefinition Width="36"/>
                <ColumnDefinition Width="36"/>
                <ColumnDefinition Width="36"/>
                <ColumnDefinition Width="36"/>
                <ColumnDefinition Width="36"/>
                <ColumnDefinition Width="36"/>
            </Grid.ColumnDefinitions>

            <Frame 
                x:Name="selectionFrame"
                Opacity="0.35"
                Padding="0"
                IsVisible="False"
                BackgroundColor="{x:StaticResource ColorPrimaryLight}"
                CornerRadius="18"
                VerticalOptions="Center"
                HeightRequest="36"
                HorizontalOptions="Fill"
                IsClippedToBounds="True"
                HasShadow="False">

            </Frame>
        </Grid>
    </ContentView.Content>
</ContentView>