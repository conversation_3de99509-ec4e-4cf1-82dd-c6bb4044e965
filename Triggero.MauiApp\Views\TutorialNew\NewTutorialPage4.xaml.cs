﻿using System.Linq;
using Triggero.MauiMobileApp.Extensions.Helpers;


namespace Triggero.MauiMobileApp.Views
{

    public partial class NewTutorialPage4 : ContentPage
    {
        public NewTutorialPage4()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        private RelayCommand goNext;
        public RelayCommand GoNext
        {
            get => goNext ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new NewTutorialPage5());
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    if (App.Current.MainPage.Navigation.NavigationStack.Contains(this))
                    {
                        try
                        {
                            App.ClosePage(this);
                        }
                        catch { }
                    }

                });
            });
        }
    }
}