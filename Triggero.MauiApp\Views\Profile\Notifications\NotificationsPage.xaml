﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             
             x:Name="ThisPage"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             xmlns:notifications="clr-namespace:Triggero.MauiMobileApp.Views.Pages.Profile.Notifications;assembly=Triggero.MauiMobileApp"
             xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
             xmlns:notifications1="clr-namespace:Triggero.MauiMobileApp.Views.Pages.Profile.Notifications"
             xmlns:buttons="http://schemas.syncfusion.com/maui"

             x:DataType="notifications1:NotificationsPage"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Profile.Notifications.NotificationsPage">

    <ContentPage.Content>
        <Grid>
            <Image
                Aspect="Fill"
                Source="trackerbggradient.png"/>

            <StackLayout
                Margin="20,50,20,0">

                <StackLayout
                    Spacing="21"
                    Orientation="Horizontal">

                    <ImageButton
                        Command="{Binding Source={x:Reference ThisPage},Path=SaveAndClose}"
                        CornerRadius="0"
                        WidthRequest="8"
                        HeightRequest="16"
                        Source="arrowback.png"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        BackgroundColor="Transparent"/>

                    <Label 
                        TextColor="{x:StaticResource ColorText}"
                        FontSize="17"
                        FontAttributes="Bold"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileNotifications.Notifications}">
                        <Label.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference ThisPage},Path=SaveAndClose}"/>
                        </Label.GestureRecognizers>
                    </Label>

                </StackLayout>


                <Frame
                    Margin="0,36,0,0"
                    HeightRequest="588"
                    VerticalOptions="Start"
                    CornerRadius="15"
                    BackgroundColor="#FFFFFF"
                    Padding="0"
                    HasShadow="False">
                    <Frame.Shadow>
                        <Shadow Brush="#27527A" Offset="2,2" Radius="12" Opacity="0.06" />
                    </Frame.Shadow>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0">

                                <Label 
                                    Margin="16,0,0,0"
                                    TextColor="Black"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    FontFamily="FontTextLight"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    WidthRequest="170"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileNotifications.RemindTrackerFilling}"/>

                                <buttons:SfSwitch 
                                    Visual="Custom"
                                    Style="{x:StaticResource sf_switch_yellow}"
                                    StateChanged="onTolggled"
                                    IsOn="{Binding Source={x:Reference ThisPage},Path=ShouldNotifyTrackerFilling,Mode=TwoWay}"
                                    Margin="0,0,20,0"
                                    WidthRequest="51"
                                    HeightRequest="31"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center"/>

                            </Grid>

                            <Grid Grid.Row="1">

                                <Label 
                                    Margin="16,0,0,0"
                                    TextColor="Black"
                                    FontSize="16"
                                    FontFamily="FontTextLight"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    WidthRequest="170"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileNotifications.ToTime}"/>

                                <Label 
                                    x:Name="LabelNotifyTrackerFillingTimeLabel"
                                    Margin="0,0,20,0"
                                    Opacity="0.5"
                                    TextColor="Black"
                                    TextDecorations="Underline"
                                    FontSize="16"
                                    FontFamily="FontTextLight"
                                    WidthRequest="51"
                                    HeightRequest="31"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center">
                                    <Label.GestureRecognizers>
                                        <TapGestureRecognizer Command="{Binding Source={x:Reference ThisPage},Path=SelectDate}" CommandParameter="NotifyTrackerFillingTime"/>
                                    </Label.GestureRecognizers>
                                </Label>

                                <BoxView
                                    Opacity="0.6"
                                    HorizontalOptions="Fill"
                                    VerticalOptions="End"
                                    Margin="16,0,0,0"
                                    HeightRequest="1"
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"/>


                            </Grid>

                            <Grid Grid.Row="2">

                                <Label 
                                    Margin="16,0,0,0"
                                    TextColor="Black"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    FontFamily="FontTextLight"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    WidthRequest="170"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileNotifications.RemindBreathMinute}"/>

                                <buttons:SfSwitch
                                    Style="{x:StaticResource sf_switch_yellow}"
                                    StateChanged="onTolggled"
                                    IsOn="{Binding Source={x:Reference ThisPage},Path=ShouldNotifyBreathTime,Mode=TwoWay}"
                                    Margin="0,0,20,0"
                                    WidthRequest="51"
                                    HeightRequest="31"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center"/>

                            </Grid>

                            <Grid Grid.Row="3">

                                <Label 
                                    Margin="16,0,0,0"
                                    TextColor="Black"
                                    FontSize="16"
                                    FontFamily="FontTextLight"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    WidthRequest="170"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileNotifications.ToTime}"/>

                                <Label 
                                    x:Name="LabelNotifyBreathTimeLabel"
                                    Margin="0,0,20,0"
                                    Opacity="0.5"
                                    TextColor="Black"
                                    TextDecorations="Underline"
                                    FontSize="16"
                                    FontFamily="FontTextLight"
                                    WidthRequest="51"
                                    HeightRequest="31"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center">
                                    <Label.GestureRecognizers>
                                        <TapGestureRecognizer Command="{Binding Source={x:Reference ThisPage},Path=SelectDate}" CommandParameter="NotifyBreathTime"/>
                                    </Label.GestureRecognizers>
                                </Label>

                                <BoxView
                                    Opacity="0.6"
                                    HorizontalOptions="Fill"
                                    VerticalOptions="End"
                                    Margin="16,0,0,0"
                                    HeightRequest="1"
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"/>

                            </Grid>

                            <Grid Grid.Row="4">

                                <Label 
                                    Margin="16,0,0,0"
                                    TextColor="Black"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    FontFamily="FontTextLight"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    WidthRequest="170"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileNotifications.NotifyNewTopics}"/>

                                <buttons:SfSwitch
                                    Style="{x:StaticResource sf_switch_yellow}"
                                    StateChanged="onTolggled"
                                    IsOn="{Binding Source={x:Reference ThisPage},Path=ShouldNotifyNewPosts,Mode=TwoWay}"
                                    Margin="0,0,20,0"
                                    WidthRequest="51"
                                    HeightRequest="31"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center"/>

                                <BoxView
                                    Opacity="0.6"
                                    HorizontalOptions="Fill"
                                    VerticalOptions="End"
                                    Margin="16,0,0,0"
                                    HeightRequest="1"
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"/>

                            </Grid>

                            <Grid Grid.Row="5">

                                <Label 
                                    Margin="16,0,0,0"
                                    TextColor="Black"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    FontFamily="FontTextLight"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    WidthRequest="170"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileNotifications.NotifyNewExercise}"/>

                                <buttons:SfSwitch
                                    Style="{x:StaticResource sf_switch_yellow}"
                                    StateChanged="onTolggled"
                                    IsOn="{Binding Source={x:Reference ThisPage},Path=ShouldNotifyNewExercises,Mode=TwoWay}"
                                    Margin="0,0,20,0"
                                    WidthRequest="51"
                                    HeightRequest="31"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center"/>

                                <BoxView
                                    Opacity="0.6"
                                    HorizontalOptions="Fill"
                                    VerticalOptions="End"
                                    Margin="16,0,0,0"
                                    HeightRequest="1"
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"/>

                            </Grid>

                            <Grid Grid.Row="6">

                                <Label 
                                    Margin="16,0,0,0"
                                    TextColor="Black"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    FontFamily="FontTextLight"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    WidthRequest="170"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileNotifications.NotifyNewPractices}"/>

                                <buttons:SfSwitch
                                    Style="{x:StaticResource sf_switch_yellow}"
                                    StateChanged="onTolggled"
                                    IsOn="{Binding Source={x:Reference ThisPage},Path=ShouldNotifyNewPractices,Mode=TwoWay}"
                                    Margin="0,0,20,0"
                                    WidthRequest="51"
                                    HeightRequest="31"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center"/>

                                <BoxView
                                    Opacity="0.6"
                                    HorizontalOptions="Fill"
                                    VerticalOptions="End"
                                    Margin="16,0,0,0"
                                    HeightRequest="1"
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"/>

                            </Grid>

                            <Grid Grid.Row="7">

                                <Label 
                                    Margin="16,0,0,0"
                                    TextColor="Black"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    FontFamily="FontTextLight"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileNotifications.NotifyNewTests}"/>

                                <buttons:SfSwitch
                                    Style="{x:StaticResource sf_switch_yellow}"
                                    StateChanged="onTolggled"
                                    IsOn="{Binding Source={x:Reference ThisPage},Path=ShouldNotifyNewTests,Mode=TwoWay}"
                                    Margin="0,0,20,0"
                                    WidthRequest="51"
                                    HeightRequest="31"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center"/>



                            </Grid>

                        </Grid>
                    </Frame>

            </StackLayout>
        </Grid>
    </ContentPage.Content>
</ContentPage>