using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace MobileAPIWrapper.Helpers
{
    public enum Method
    {
        Get,
        Post,
        Put,
        Delete,
        Patch
    }

    /// <summary>
    /// Mobile-optimized HTTP client helper for MAUI applications
    /// Uses pure HttpClient with ZERO RestSharp dependencies
    /// Completely secure and lightweight for mobile applications
    /// </summary>
    public static class RequestHelper
    {
        private static readonly HttpClient _httpClient;
        private static string _token;
        private static IEnumerable<KeyValuePair<string, string>> _headers;

        public static EventHandler OnUnauthorized;

        /// <summary>
        /// JSON serialization options that match RestSharp behavior (PascalCase, ignores Newtonsoft attributes)
        /// </summary>
        private static readonly JsonSerializerOptions JsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };

        static RequestHelper()
        {
            var handler = new HttpClientHandler();

            // For development - bypass SSL certificate validation
            // TODO: Remove this in production or make it configurable
            handler.ServerCertificateCustomValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;

            _httpClient = new HttpClient(handler);
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
        }

        public static void SetBearerToken(string token)
        {
            _token = token;
        }

        public static void AddDefaultHeaders(IEnumerable<KeyValuePair<string, string>> headers)
        {
            _headers = headers;
        }

        public static void ClearDefaultHeaders()
        {
            _headers = null;
        }

        /// <summary>
        /// Convert Method enum to HttpMethod
        /// </summary>
        private static HttpMethod ConvertHttpMethod(Method method)
        {
            return method switch
            {
                Method.Get => HttpMethod.Get,
                Method.Post => HttpMethod.Post,
                Method.Put => HttpMethod.Put,
                Method.Delete => HttpMethod.Delete,
                Method.Patch => new HttpMethod("PATCH"),
                _ => HttpMethod.Get
            };
        }

        // No custom enum needed - we use System.Net.Http.HttpMethod directly

        /// <summary>
        /// Response object for mobile HTTP requests
        /// </summary>
        public class MobileResponse
        {
            public string Content { get; set; } = string.Empty;
            public HttpStatusCode StatusCode { get; set; }
            public string ErrorMessage { get; set; } = string.Empty;
            public Exception? ErrorException { get; set; }
            public bool IsSuccessful => (int)StatusCode >= 200 && (int)StatusCode <= 299;
        }

        // No conversion needed - we use HttpMethod directly

        /// <summary>
        /// Execute HTTP request using pure HttpClient (ZERO RestSharp dependencies)
        /// </summary>
        public static async Task<MobileResponse> ExecuteRequestAsync(
            string url,
            Method method,
            object? body = null)
        {
            try
            {
                Debug.WriteLine($"[MobileRequestApiHelper] {method} {url}");

                using var request = new HttpRequestMessage(ConvertHttpMethod(method), url);

                // Add authorization header if token is set
                if (!string.IsNullOrEmpty(_token))
                {
                    Debug.WriteLine($"[MobileRequestApiHelper] Authorization Bearer {_token}");
                    request.Headers.Add("Authorization", "Bearer " + _token);
                }

                // Add default headers
                if (_headers != null)
                {
                    foreach (var header in _headers)
                    {
                        request.Headers.Add(header.Key, header.Value);
                    }
                }

                // Add JSON body if provided
                if (body != null)
                {
                    Debug.WriteLine($"[MobileRequestApiHelper] Sending request {request.Method} with body");
                    var json = JsonSerializer.Serialize(body, JsonOptions);
                    Debug.WriteLine(json);

                    request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                }

                // Debug headers
                Debug.WriteLine($"[MobileRequestApiHelper] Request Headers:");
                foreach (var header in request.Headers)
                {
                    Debug.WriteLine($"  {header.Key}: {string.Join(", ", header.Value)}");
                }
                if (request.Content?.Headers != null)
                {
                    foreach (var header in request.Content.Headers)
                    {
                        Debug.WriteLine($"  {header.Key}: {string.Join(", ", header.Value)}");
                    }
                }

                var response = await _httpClient.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();

                Debug.WriteLine($"[MobileRequestApiHelper] Response: {response.StatusCode} {response.ReasonPhrase}");
                if (!response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"[MobileRequestApiHelper] Error Response Body: {content}");
                }

                // Create MobileResponse
                var mobileResponse = new MobileResponse
                {
                    Content = content,
                    StatusCode = response.StatusCode
                };

                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    OnUnauthorized?.Invoke(null, null);
                }

                return mobileResponse;
            }
            catch (Exception e)
            {
                Debug.WriteLine($"[MobileRequestApiHelper] Error: {e.Message}");
                return new MobileResponse
                {
                    ErrorMessage = e.Message,
                    ErrorException = e,
                    StatusCode = HttpStatusCode.BadRequest
                };
            }
        }

        /// <summary>
        /// Execute request and deserialize response to model using pure .NET JSON
        /// </summary>
        public static async Task<T?> ExecuteRequestReceiveModelAsync<T>(string url, Method method, object? dto = null)
        {
            try
            {
                var response = await ExecuteRequestAsync(url, method, dto);

                if (!response.IsSuccessful)
                {
                    Debug.WriteLine($"[MobileRequestApiHelper] Request failed with status: {response.StatusCode}");
                    return default;
                }

                return JsonSerializer.Deserialize<T>(response.Content, JsonOptions);
            }
            catch (Exception e)
            {
                Debug.WriteLine($"[MobileRequestApiHelper] Deserialization error: {e}");
            }

            return default(T);
        }

        /// <summary>
        /// Dispose resources when application shuts down
        /// </summary>
        public static void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

}
