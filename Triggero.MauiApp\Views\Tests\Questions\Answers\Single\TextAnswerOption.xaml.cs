﻿using Triggero.MauiPort.Models;
using Triggero.Models.Abstractions;
using Triggero.Models.Tests.QuestionOptions;

namespace Triggero.Controls.Cards.Tests.Questions.Answers.Single
{

    public partial class TextAnswerOption : BaseQuestionOptionView
    {
        public TextAnswerOption(QuestionOption option) : base(option)
        {
            InitializeComponent();
            Text = (option as SimpleQuestionOption).GetLocalizedText(LanguageHelper.LangCode);
        }

        private string text;
        public string Text
        {
            get { return text; }
            set { text = value; OnPropertyChanged(nameof(Text)); }
        }

        private void onRbTapped(object sender, EventArgs e)
        {
            IsChecked = !IsChecked;
        }
    }
}