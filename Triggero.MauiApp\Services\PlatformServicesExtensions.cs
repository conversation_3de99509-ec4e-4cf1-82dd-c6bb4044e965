using Triggero.MauiMobileApp.Abstractions;

namespace Triggero.MauiMobileApp.Services
{
    public static class PlatformServicesExtensions
    {
        public static MauiAppBuilder AddPlatformServices(this MauiAppBuilder builder)
        {
            // Register platform-specific services
#if ANDROID
            builder.Services.AddSingleton<IPlatformUi, PlatformUi>();
            builder.Services.AddSingleton<IToastMessage, Platforms.Android.ToastMessage>();
#elif IOS
            builder.Services.AddSingleton<IPlatformUi, PlatformUi>();
            builder.Services.AddSingleton<IToastMessage, Platforms.iOS.ToastMessage>();
#elif WINDOWS
            builder.Services.AddSingleton<IPlatformUi, PlatformUi>();
            builder.Services.AddSingleton<IToastMessage, Platforms.Windows.ToastMessage>();
#endif

            return builder;
        }
    }


}
