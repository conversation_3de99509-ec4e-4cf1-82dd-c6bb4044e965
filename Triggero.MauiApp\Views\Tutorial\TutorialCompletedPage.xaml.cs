﻿

using System.Threading.Tasks;
using Triggero.MauiMobileApp.Controls;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Views.Pages.Start;
using Triggero.MauiMobileApp.Views.Pages.Subscriptions;
using RelayCommand = Triggero.MauiMobileApp.Extensions.Helpers.RelayCommand;


namespace Triggero.MauiMobileApp.Views.Pages.Auth
{

    public partial class TutorialCompletedPage : ContentPage
    {
        public TutorialCompletedPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            ApplicationState.ConfigData.IsFirstUse = false;
            ApplicationState.ConfigData.WasShownStartTutorial = true;
            ApplicationState.SaveChangesToMemory();

            //DependencyService.Get<IStatusBar>().HideStatusBar();
        }

        protected override void OnAppearing()
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                var animatedImage = new GifCachedImage
                {
                    HeightRequest = 240,
                    WidthRequest = 200,
                    Opacity = 0,

                    VerticalOptions = LayoutOptions.Start,
                    HorizontalOptions = LayoutOptions.Center,
                    //Aspect = Aspect.AspectFit,
                    Source = "animationdogwithcompanim.gif"
                };
                animatedLayout.Children.Insert(0, animatedImage);

                animatedImage.Success += async (sender, e) =>
                {
                    await Task.Delay(500);
                    animatedImage.Opacity = 1;
                    notAnimatedImg.Opacity = 0;
                };
            });

        }

        private RelayCommand goToTrial;
        public RelayCommand GoToTrial
        {
            get => goToTrial ??= new RelayCommand(async obj =>
            {
                if (Constants.IsFreeVersion)
                {
                    App.OpenPage(new DisclaimerPage());
                }
                else
                {
                    App.OpenPage(new SelectTrialPage());
                }
            });
        }
    }
}