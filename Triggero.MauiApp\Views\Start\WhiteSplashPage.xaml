﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.WhiteSplashPage">
    <ContentPage.Content>
        <Grid 
            BackgroundColor="White">
            <!--<Image
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightbluegradientbg.png"/>

            <StackLayout
                Spacing="30"
                x:Name="xxxLayout"
                VerticalOptions="Center"
                HorizontalOptions="Center">



                <Image
                    x:Name="notAnimatedImg"
                    Source="logoyellow.png"  
                    Aspect="AspectFit"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    HeightRequest="130"
                    WidthRequest="130"/>

                <Image 
                    Source="logotitleyellow.png"
                    Aspect="Fill"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    HeightRequest="30"
                    WidthRequest="220"/>




            </StackLayout>-->

        </Grid>
    </ContentPage.Content>
</ContentPage>