﻿using System.Linq;


namespace Triggero.MauiMobileApp.Views
{

    public partial class NewTutorialPage12 : ContentPage
    {
        public NewTutorialPage12()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        private Command finishTutorial;
        public Command FinishTutorial
        {
            get => finishTutorial ??= new Command(async obj =>
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    try
                    {
                        if (App.Current.MainPage.Navigation.NavigationStack.Contains(this))
                        {
                            App.Current.MainPage.Navigation.RemovePage(this);
                            App.UpdateState();
                        }
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }
                });
            });
        }
    }
}