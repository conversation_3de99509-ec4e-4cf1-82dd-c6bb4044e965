﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models;
using Triggero.Models.Sliders;



namespace Triggero.Controls.Cards.Sliders
{
    
    public partial class OnboardingSliderCard : ContentView
    {
        public OnboardingSliderCard()
        {
            InitializeComponent();
        }

        public static readonly BindableProperty HelloSliderItemProperty = BindableProperty.Create(nameof(HelloSliderItem), typeof(HelloSliderItem), typeof(OnboardingSliderCard));
        public HelloSliderItem HelloSliderItem
        {
            get { return (HelloSliderItem)GetValue(HelloSliderItemProperty); }
            set { SetValue(HelloSliderItemProperty, value); }
        }


    }
}