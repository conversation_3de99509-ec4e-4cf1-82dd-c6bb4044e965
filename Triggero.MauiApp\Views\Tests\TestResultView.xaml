﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Controls.Tests.TestResultView">
  <ContentView.Content>
      <StackLayout>
            <Label 
                x:Name="titleLabel"
                Margin="10,12,10,0"
                TextColor="{x:StaticResource ColorText}"
                FontSize="18"
                FontAttributes="Bold"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                VerticalOptions="Center"
                Text=""/>
            <Label 
                x:Name="textLabel"
                TextColor="{x:StaticResource ColorText}"
                FontSize="15"
                Margin="0,30,0,0"
                Text=""/>
        </StackLayout>
  </ContentView.Content>
</ContentView>