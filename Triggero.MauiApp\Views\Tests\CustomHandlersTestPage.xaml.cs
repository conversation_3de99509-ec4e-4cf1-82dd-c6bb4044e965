namespace Triggero.MauiPort.Views.Pages;

public partial class CustomHandlersTestPage : ContentPage
{
    private bool _isFocused = false;

    public CustomHandlersTestPage()
    {
        InitializeComponent();
    }

    private void OnTestButtonClicked(object sender, EventArgs e)
    {
        if (!_isFocused)
        {
            TestEntry.Focus();
            StatusLabel.Text = "Entry focused - check for no underline";
            TestButton.Text = "Focus Editor";
            _isFocused = true;
        }
        else
        {
            TestEntry.Unfocus();
            TestEditor.Focus();
            StatusLabel.Text = "Editor focused - check for no underline";
            TestButton.Text = "Unfocus All";
            
            // After a delay, reset the button
            Device.StartTimer(TimeSpan.FromSeconds(2), () =>
            {
                TestEditor.Unfocus();
                StatusLabel.Text = "All unfocused - test complete";
                TestButton.Text = "Test Focus/Unfocus";
                _isFocused = false;
                return false; // Stop the timer
            });
        }
    }
}
