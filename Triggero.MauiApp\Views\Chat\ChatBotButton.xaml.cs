﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models.ChatBot;



namespace Triggero.Controls.ChatBot
{
    
    public partial class ChatBotButton : ContentView
    {
        private ChatOptionButton _chatOptionButton;
        public ChatBotButton(ChatOptionButton btn)
        {
            _chatOptionButton = btn;
            InitializeComponent();

            button.Text = btn.Text;
        }


        public event EventHandler<ChatOptionButton> Clicked;
        private void onTapped(object sender, EventArgs e)
        {
            Clicked?.Invoke(this, _chatOptionButton);
        }
    }
}