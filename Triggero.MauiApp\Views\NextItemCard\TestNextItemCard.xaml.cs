﻿using Triggero.MauiMobileApp.Extensions;
using Triggero.Models.Tests;
using Triggero.MauiMobileApp.Views.Pages.Tests;

using Triggero.MauiMobileApp.Extensions.Helpers;

namespace Triggero.Controls.Cards.NextItemCard
{

    public partial class TestNextItemCard : ContentView
    {
        private Test _test;

        public TestNextItemCard()
        {
            InitializeComponent();
        }
        public TestNextItemCard(Test test)
        {
            InitializeComponent();
            Build(test);
        }
        public async void Build(Test test)
        {
            _test = test;

            titleLabel.Text = _test.GetLocalizedTitle(LanguageHelper.LangCode);
            var desc = _test.GetLocalizedDescription(LanguageHelper.LangCode);
            desc = StringExtensions.ExtractText(desc);
            if (desc.Length > 27 * 2)
            {
                desc = desc.Substring(0, 27 * 2) + "...";
            }
            descLabel.Text = desc;

            img.Source = await ResorcesHelper.GetImageSource(_test.ImgPath);

        }

        private async void tapped(object sender, EventArgs e)
        {
            if (_test != null)
            {
                App.OpenPage(new TestPage(_test));
            }
        }
    }
}