﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Other.NeedToHandleItem">
  <ContentView.Content>
      
      <Frame          
          BackgroundColor="Transparent"
          BorderColor="{x:StaticResource ColorText}"
          CornerRadius="17"
          HeightRequest="35"
          VerticalOptions="Center"
          HasShadow="False"
          Padding="10,7,10,7">
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped"/>
            </Frame.GestureRecognizers>
            
          <Label 
                x:Name="titleLabel"
                TextColor="{x:StaticResource ColorText}"
                FontSize="14"
                HorizontalOptions="Center"
                VerticalOptions="Center"
                Text="Самооценка"/>
        
      </Frame>
  </ContentView.Content>
</ContentView>