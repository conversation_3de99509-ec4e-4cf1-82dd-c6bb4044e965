﻿using MobileAPIWrapper;
using System.Windows.Input;
using Triggero.MauiMobileApp.Abstractions;
using Triggero.MauiMobileApp.Extensions.Helpers;


namespace Triggero.MauiMobileApp.Views.Pages.Auth
{

    public partial class LoginByPhonePage : ContentPage
    {
        public LoginByPhonePage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        private string phone = "";
        public string Phone
        {
            get { return phone; }
            set { phone = value; OnPropertyChanged(nameof(Phone)); }
        }

        private RelayCommand sendCode;
        public RelayCommand SendCode
        {
            get => sendCode ??= new RelayCommand(async obj =>
            {
                if (await TriggeroMobileAPI.Account.RequestSms(Phone))
                {
                    App.OpenPage(new SMSCodePage(Phone));
                }
                else
                {
                    PlatformUi.Instance.ShowAlert("Ошибка");
                }
            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    try
                    {
                        await App.Current.MainPage.Navigation.PopAsync();
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }
                });

            });
        }
    }
}