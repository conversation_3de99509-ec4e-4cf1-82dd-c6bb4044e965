﻿ 
using Triggero.Models.General;
using Triggero.Models.Practices;


namespace Triggero.Models
{
    public record FavChangedObject(int Id, bool Value);

    public class AvatarWrapper : BindableObject
    {

        public AvatarWrapper(UserAvatar avatar)
        {
            Avatar = avatar;
            Load();
        }
        ~AvatarWrapper()
        {
            Image = null;
        }

        private async void Load()
        {
            //Image = await ResorcesHelper.GetImageSource(avatar.AvatarPath);
            Image = App.GetFullImageUrl(avatar.AvatarPath, ThumbnailSize.Small, ThumbnailType.Png);
        }

        private ImageSource image;
        public ImageSource Image
        {
            get { return image; }
            set { image = value; OnPropertyChanged(nameof(Image)); }
        }

        private UserAvatar avatar;
        public UserAvatar Avatar
        {
            get { return avatar; }
            set { avatar = value; OnPropertyChanged(nameof(Avatar)); }
        }
    }
}
