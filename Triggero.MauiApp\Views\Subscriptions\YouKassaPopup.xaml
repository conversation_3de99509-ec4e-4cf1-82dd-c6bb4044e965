﻿<?xml version="1.0" encoding="utf-8"?>

<popups:Popup
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:popups="clr-namespace:AppoMobi.Maui.Popups;assembly=AppoMobi.Maui.Popups"
    x:Name="this"
    HorizontalOptions="Fill"
    VerticalOptions="Fill"
    x:Class="Triggero.MauiMobileApp.Views.Popups.YouKassaPopup">

    <Grid 
        Padding="0,187,0,0"
        VerticalOptions="Fill" HorizontalOptions="Fill">

        <Frame
            BackgroundColor="GhostWhite"
            IsClippedToBounds="True"
            HasShadow="False"
            CornerRadius="15"
            Padding="0"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <Grid x:Name="grid" HorizontalOptions="Fill" VerticalOptions="Fill">

                <!--<WebView x:Name="webView" />-->

                <WebView
                    BackgroundColor="White"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill"
                    x:Name="webView" />

                <ImageButton
                    BackgroundColor="Transparent"
                    Command="{Binding Source={x:Reference this},Path=Close}"
                    CornerRadius="0"
                    Margin="32"
                    HorizontalOptions="End"
                    VerticalOptions="Start"
                    WidthRequest="20"
                    HeightRequest="20"
                    Source="close.png" />

            </Grid>

        </Frame>
    </Grid>

</popups:Popup>