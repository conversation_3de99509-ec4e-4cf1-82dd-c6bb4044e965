﻿using AppoMobi.Maui.Gestures;
using DrawnUi.Draw;
 
 

namespace Triggero.MauiMobileApp.Views.Drawn
{
	public class OverlayLock : ContentLayout
	{
		public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args, GestureEventProcessingInfo apply)
		{
			if (args.Type == TouchActionResult.Tapped)
			{
				App.OpenNeedToPayNow();

				return this;
			}

			return base.ProcessGestures(args, apply);
		}
	}
}
