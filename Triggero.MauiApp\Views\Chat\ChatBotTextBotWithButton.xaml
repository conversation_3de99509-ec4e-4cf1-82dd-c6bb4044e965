﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             xmlns:views="http://schemas.appomobi.com/drawnUi/2023/draw"
             x:Class="Triggero.Controls.ChatBot.ChatBotTextBotWithButton">
  <ContentView.Content>

        <Grid HeightRequest="45">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="45"/>
            </Grid.ColumnDefinitions>

            <!--grayTextEditor-->
            <Grid
                HeightRequest="40">
                <views:Canvas HorizontalOptions="Fill" VerticalOptions="Fill">

                    <views:SkiaShape
                        HorizontalOptions="Fill" VerticalOptions="Fill"
                        UseCache="Operations"
                        StrokeColor="#DEEAF6"
                        StrokeWidth="1.5"
                        CornerRadius="16" />

                </views:Canvas>

                <Editor
                    Margin="10,16,10,0"
                    Placeholder="{Binding Source={x:Static mobile:App.This},Path=Interface.ChatBot.MessagePlaceholder}"                
                    x:Name="textEdit"
                    Style="{x:StaticResource grayTextEditor}"
                    Focused="onFocused"
                    Unfocused="onUnfocused"
                    VerticalOptions="Fill"
                    HorizontalOptions="Fill"/>

            </Grid>

            <ImageButton
                Grid.Column="1"
                x:Name="sendBtn"
                Clicked="onClick"
                Source="chatsendbtn.png"
                BackgroundColor="Transparent"
                CornerRadius="0"
                HeightRequest="39"
                WidthRequest="39"/>

        </Grid>
    </ContentView.Content>
</ContentView>