using System.Collections.Generic;
using FluentAssertions;
using NUnit.Framework;
using Triggero.Domain.Models;
using Triggero.Domain.Models.Enums;
using Triggero.Models.Enums;
using System.Net;
using System.Linq;
using System;
using Microsoft.Extensions.DependencyInjection;
using Triggero.Database;
using System.Threading.Tasks;

namespace Triggero.Api.PaymentTests;

[TestFixture]
public class PaySubscriptionTests : PaymentTestsBase
{
    [SetUp]
    public void SetUp()
    {
        // Set authorization token if needed
        // SetAuthorizationToken("your-jwt-token-here");
    }

    [Test]
    public void Configuration_ShouldBeLoadedCorrectly()
    {
        // Verify that configuration is loaded from the API project
        TestContext.WriteLine($"API Base URL: {TestConfiguration.ApiBaseUrl}");
        TestContext.WriteLine($"JWT Issuer: {TestConfiguration.JwtIssuer}");
        TestContext.WriteLine($"Test User ID: {TestConfiguration.TestUserId}");
        TestContext.WriteLine($"Connection String configured: {!string.IsNullOrEmpty(TestConfiguration.ConnectionString)}");
        TestContext.WriteLine($"UKassa Shop ID configured: {!string.IsNullOrEmpty(TestConfiguration.UKassaShopId)}");
        TestContext.WriteLine($"UKassa Key configured: {!string.IsNullOrEmpty(TestConfiguration.UKassaKey)}");

        // Basic assertions
        TestConfiguration.ApiBaseUrl.Should().NotBeNullOrEmpty();
        TestConfiguration.TestUserId.Should().BeGreaterThan(0);
    }

    [Test]
    public void BackgroundJobs_ShouldBeFixedForDependencyInjection()
    {
        // This test verifies that the background jobs can access the service provider
        // and won't fail with database context creation issues

        TestContext.WriteLine("Testing that background jobs are properly configured for DI...");

        // The jobs should now use IServiceProvider instead of new DatabaseContext()
        // This test just verifies the compilation and basic setup

        var serviceProvider = Factory.Services;
        serviceProvider.Should().NotBeNull();

        // Verify that DatabaseContext can be resolved from DI
        using (var scope = serviceProvider.CreateScope())
        {
            var dbContext = scope.ServiceProvider.GetRequiredService<DatabaseContext>();
            dbContext.Should().NotBeNull();

            TestContext.WriteLine("✓ DatabaseContext can be resolved from DI");
        }

        TestContext.WriteLine("✓ Background jobs should now work with proper DI");
    }

    [Test]
    public async Task PaySubscription_WithValidData_ShouldReturnCreatedPayment()
    {
        // Arrange
        var userID = TestConfiguration.TestUserId; // Use configured test user ID
        var settings = new SubscriptionPaymentSettings
        {
            SubType = SubscriptionType.Full,
            Duration = BuiltInPlanType.Month,
            planOptionIds = new List<int> { 1 }, // Use valid plan option IDs
            IsBindingPayment = false,
            PaymentMethod = PaymentMethodEnum.Yookassa
        };

        TestContext.WriteLine($"Testing with User ID: {userID}");
        TestContext.WriteLine($"Payment Settings: {SerializeObject(settings)}");

        // Act
        var response = await PostJsonAsync($"Payments/PaySubscription?userID={userID}", settings);

        // Assert
        TestContext.WriteLine($"Response Status: {response.StatusCode}");
        var responseContent = await response.Content.ReadAsStringAsync();
        TestContext.WriteLine($"Response Content: {responseContent}");

        if (response.IsSuccessStatusCode)
        {
            var result = Newtonsoft.Json.JsonConvert.DeserializeObject<CreatedPaymentModel>(responseContent);
            result.Should().NotBeNull();

            // Check if we got a payment ID or confirmation URL
            if (!string.IsNullOrEmpty(result.PaymentId))
            {
                TestContext.WriteLine($"✓ Payment ID received: {result.PaymentId}");
            }

            if (!string.IsNullOrEmpty(result.ConfirmationUrl))
            {
                TestContext.WriteLine($"✓ Confirmation URL received: {result.ConfirmationUrl}");

                // Check if it's an error (exceptions are returned in ConfirmationUrl)
                if (result.ConfirmationUrl.Contains("Exception") || result.ConfirmationUrl.Contains("Error"))
                {
                    TestContext.WriteLine($"⚠️ Error detected in response: {result.ConfirmationUrl}");
                }
            }
        }
        else
        {
            TestContext.WriteLine($"❌ Request failed with status: {response.StatusCode}");
            TestContext.WriteLine($"Error content: {responseContent}");
        }
    }

    [Test]
    public async Task PaySubscription_WithInvalidUserID_ShouldHandleGracefully()
    {
        // Arrange
        var userID = -1; // Invalid user ID
        var settings = new SubscriptionPaymentSettings
        {
            SubType = SubscriptionType.Full,
            Duration = BuiltInPlanType.Month,
            planOptionIds = new List<int> { 1 },
            IsBindingPayment = false,
            PaymentMethod = PaymentMethodEnum.Yookassa
        };

        // Act
        var response = await PostJsonAsync($"Payments/PaySubscription?userID={userID}", settings);

        // Assert
        TestContext.WriteLine($"Response Status: {response.StatusCode}");
        var responseContent = await response.Content.ReadAsStringAsync();
        TestContext.WriteLine($"Response Content: {responseContent}");

        // The method should return a CreatedPaymentModel even on error (with error in ConfirmationUrl)
        if (response.IsSuccessStatusCode)
        {
            var result = Newtonsoft.Json.JsonConvert.DeserializeObject<CreatedPaymentModel>(responseContent);
            result.Should().NotBeNull();
            
            // If there's an error, it should be in the ConfirmationUrl field
            if (!string.IsNullOrEmpty(result.ConfirmationUrl) && result.ConfirmationUrl.Contains("Exception"))
            {
                TestContext.WriteLine("Error handled as expected in ConfirmationUrl field");
            }
        }
    }

    [Test]
    public async Task PaySubscription_WithDifferentPaymentMethods_ShouldWork()
    {
        // Arrange
        var userID = 1; // Use a valid user ID
        var paymentMethods = new[] { PaymentMethodEnum.Yookassa, PaymentMethodEnum.Appstore };

        foreach (var paymentMethod in paymentMethods)
        {
            var settings = new SubscriptionPaymentSettings
            {
                SubType = SubscriptionType.Full,
                Duration = BuiltInPlanType.Month,
                planOptionIds = new List<int> { 1 },
                IsBindingPayment = false,
                PaymentMethod = paymentMethod
            };

            // Act
            var response = await PostJsonAsync($"Payments/PaySubscription?userID={userID}", settings);

            // Assert
            TestContext.WriteLine($"Testing payment method: {paymentMethod}");
            TestContext.WriteLine($"Response Status: {response.StatusCode}");
            var responseContent = await response.Content.ReadAsStringAsync();
            TestContext.WriteLine($"Response Content: {responseContent}");
            TestContext.WriteLine("---");
        }
    }

    [Test]
    public async Task PaySubscription_WithDifferentDurations_ShouldWork()
    {
        // Arrange
        var userID = 1; // Use a valid user ID
        var durations = new[] { BuiltInPlanType.Month, BuiltInPlanType.Year };

        foreach (var duration in durations)
        {
            var settings = new SubscriptionPaymentSettings
            {
                SubType = SubscriptionType.Full,
                Duration = duration,
                planOptionIds = new List<int> { 1 },
                IsBindingPayment = false,
                PaymentMethod = PaymentMethodEnum.Yookassa
            };

            // Act
            var response = await PostJsonAsync($"Payments/PaySubscription?userID={userID}", settings);

            // Assert
            TestContext.WriteLine($"Testing duration: {duration}");
            TestContext.WriteLine($"Response Status: {response.StatusCode}");
            var responseContent = await response.Content.ReadAsStringAsync();
            TestContext.WriteLine($"Response Content: {responseContent}");
            TestContext.WriteLine("---");
        }
    }

    [Test]
    public async Task PaySubscription_WithAuthToken_ShouldWork()
    {
        // Arrange - Set your JWT token here for testing with authorization
        var testToken = TestConfiguration.TestAuthToken; // Get from configuration

        if (!string.IsNullOrEmpty(testToken))
        {
            SetAuthorizationToken(testToken);
            TestContext.WriteLine($"Using authorization token: {testToken.Substring(0, Math.Min(20, testToken.Length))}...");
        }
        else
        {
            TestContext.WriteLine("No authorization token configured - testing without auth");
        }

        var userID = TestConfiguration.TestUserId;
        var settings = new SubscriptionPaymentSettings
        {
            SubType = SubscriptionType.Custom,
            Duration = BuiltInPlanType.Month,
            planOptionIds = new List<int> { 3 },
            IsBindingPayment = true,
            PaymentMethod = PaymentMethodEnum.Yookassa
        };

        // Act
        var response = await PostJsonAsync($"Payments/PaySubscription?userID={userID}", settings);

        // Assert
        TestContext.WriteLine($"Response Status: {response.StatusCode}");
        var responseContent = await response.Content.ReadAsStringAsync();
        TestContext.WriteLine($"Response Content: {responseContent}");
    }

    [Test]
    public async Task PaySubscription_DebugFailingScenario_ShouldProvideDetailedInfo()
    {
        // This test is specifically designed to help debug the failing PaySubscription method
        TestContext.WriteLine("=== DEBUGGING PAYSUBSCRIPTION FAILURE ===");

        // Test with minimal valid data first
        var userID = TestConfiguration.TestUserId;
        var settings = new SubscriptionPaymentSettings
        {
            SubType = SubscriptionType.Full,
            Duration = BuiltInPlanType.Month,
            planOptionIds = new List<int>(), // Empty list to test
            IsBindingPayment = false,
            PaymentMethod = PaymentMethodEnum.Yookassa
        };

        TestContext.WriteLine($"Test User ID: {userID}");
        TestContext.WriteLine($"Settings: {SerializeObject(settings)}");
        TestContext.WriteLine($"API Base URL: {Client.BaseAddress}");

        try
        {
            // Act
            var response = await PostJsonAsync($"Payments/PaySubscription?userID={userID}", settings);

            // Detailed response analysis
            TestContext.WriteLine($"HTTP Status: {response.StatusCode} ({(int)response.StatusCode})");
            TestContext.WriteLine($"Response Headers: {string.Join(", ", response.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}"))}");

            var responseContent = await response.Content.ReadAsStringAsync();
            TestContext.WriteLine($"Raw Response Content: {responseContent}");

            if (response.IsSuccessStatusCode && !string.IsNullOrEmpty(responseContent))
            {
                try
                {
                    var result = Newtonsoft.Json.JsonConvert.DeserializeObject<CreatedPaymentModel>(responseContent);
                    TestContext.WriteLine($"Parsed Result - PaymentId: '{result?.PaymentId}', ConfirmationUrl: '{result?.ConfirmationUrl}'");

                    // Check for errors in the response
                    if (!string.IsNullOrEmpty(result?.ConfirmationUrl))
                    {
                        if (result.ConfirmationUrl.Contains("Exception") || result.ConfirmationUrl.Contains("Error"))
                        {
                            TestContext.WriteLine($"🔍 FOUND ERROR IN RESPONSE:");
                            TestContext.WriteLine(result.ConfirmationUrl);

                            // Try to extract the actual exception details
                            var lines = result.ConfirmationUrl.Split('\n');
                            foreach (var line in lines.Take(10)) // Show first 10 lines of error
                            {
                                TestContext.WriteLine($"  {line.Trim()}");
                            }
                        }
                    }
                }
                catch (Exception parseEx)
                {
                    TestContext.WriteLine($"Failed to parse response as JSON: {parseEx.Message}");
                }
            }
            else
            {
                TestContext.WriteLine($"Request failed or empty response");
            }
        }
        catch (Exception ex)
        {
            TestContext.WriteLine($"Exception during test execution: {ex}");
        }
    }
}
