﻿using AppoMobi.Maui.Gestures;
using MobileAPIWrapper;
using System.Windows.Input;

namespace Triggero.MauiMobileApp.Views.Pages.Auth
{

    public partial class EmailForgotPasswordCodePage : ContentPage
    {
        private string _email;
        public EmailForgotPasswordCodePage(string email)
        {
            _email = email;

            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }


        private string code;
        public string Code
        {
            get { return code; }
            set { code = value; OnPropertyChanged(nameof(Code)); }
        }
        void ShowError(string error)
        {

            MainThread.BeginInvokeOnMainThread(() =>
            {
                errorLabel.IsVisible = true;
                errorLabel.Text = error;
            });

        }

        public RelayCommand RestorePassword
        {
            get => new RelayCommand(async obj =>
            {
                if (TouchEffect.CheckLockAndSet())
                    return;


                MainThread.BeginInvokeOnMainThread(() =>
                {
                    errorLabel.IsVisible = false;
                });

                try
                {
                    if (string.IsNullOrEmpty(_email))
                    {
                        ShowError("Не корректный код");
                        return;
                    }

                    if (await TriggeroMobileAPI.GeneralMethods.VerificationMethods.IsCodeRight(_email, Code))
                    {
                        App.OpenPage(new LoginStartPage());
                    }
                    else
                    {
                        ShowError(App.This.Interface.Auth.EmailForgotPasswordCode.MessageInvalidCode);
                    }

                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
#if DEBUG
                    ShowError(e.ToString());
#else
                        ShowError("Ошибка");
#endif
                }

            });
        }
        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
    }
}