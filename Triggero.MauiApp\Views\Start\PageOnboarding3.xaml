﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Start.PageOnboarding3"
             xmlns:app="clr-namespace:Triggero"
             
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        <Grid>

            <Image
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightbluegradientbg.png"/>


            <StackLayout
                Spacing="0"
                VerticalOptions="Center">

                <!--<Image
                    WidthRequest="242"
                    HeightRequest="269"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    Source="letsstart.png"/>-->

         

                <Grid
                    x:Name="animatedLayout"
                    WidthRequest="242"
                    HeightRequest="269"
                    VerticalOptions="Start"
                    HorizontalOptions="Center">

                    <Image
                        x:Name="notAnimatedImg"
                        WidthRequest="242"
                        HeightRequest="269"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        Source="animationscooter.png"/>

                </Grid>

                <Label 
                    Margin="0,40,0,0"
                    TextColor="{x:StaticResource ColorText}"
                    FontSize="22"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Start.LetsStartPage.Header}"/>


                <StackLayout
                    Spacing="1"
                    HorizontalOptions="Center"
                    Margin="0,20,0,0">

                    <Label 
                        TextColor="{x:StaticResource ColorText}"
                        Opacity="0.5"
                        FontSize="14"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Margin="50,0"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Start.LetsStartPage.Text}"/>
                 

                </StackLayout>

                <Grid 
                    HeightRequest="56"
                    ColumnSpacing="16"
                    Margin="20,90,20,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>


                    <Button 
                        Command="{Binding Source={x:Reference this},Path=GoToRegistration}"
                        Grid.Column="0"
                        VerticalOptions="Center"
                        Style="{x:StaticResource yellow_btn}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Start.LetsStartPage.SignUp}"/>

                    <Button 
                        Command="{Binding Source={x:Reference this},Path=GoToLogin}"
                        Grid.Column="1"
                        VerticalOptions="Center"
                        Style="{x:StaticResource grey_cornered_btn}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Start.LetsStartPage.SignIn}"/>

                </Grid>



            </StackLayout>
            
            
            
            

        </Grid>
    </ContentPage.Content>
</ContentPage>