﻿using AppoMobi.Maui.Gestures;
using MobileAPIWrapper;
using System.Windows.Input;
using Triggero.Common.Helpers;
using Triggero.MauiMobileApp.Extensions.Helpers;

namespace Triggero.MauiMobileApp.Views.Pages.Auth
{

    public partial class EmailForgotPasswordPage : ContentPage
    {
        public EmailForgotPasswordPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }


        private string email = "";
        public string Email
        {
            get { return email; }
            set { email = value; OnPropertyChanged(nameof(Email)); }
        }

        void ShowError(string error)
        {

            MainThread.BeginInvokeOnMainThread(() =>
            {
                errorLabel.IsVisible = true;
                errorLabel.Text = error;
            });

        }

        public RelayCommand RestorePassword
        {
            get => new RelayCommand(async obj =>
            {
                if (TouchEffect.CheckLockAndSet())
                    return;

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    errorLabel.IsVisible = false;
                });


                try
                {
                    var email = EmailValidator.NormalizedEmail(Email);
                    if (string.IsNullOrEmpty(email))
                    {
                        ShowError("Не корректный адрес электронной почты");
                        return;
                    }
                    Email = email;

                    if (await TriggeroMobileAPI.GeneralMethods.VerificationMethods.SendEmailCode(Email))
                    {
                        App.OpenPage(new EmailForgotPasswordCodePage(Email));
                    }
                    else
                    {
                        ShowError("Неизвестная ошибка");
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
#if DEBUG
                    ShowError(e.ToString());
#else
                        ShowError("Ошибка");
#endif
                }

            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
    }
}