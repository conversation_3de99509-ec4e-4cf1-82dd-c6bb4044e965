﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Custom.ScrollableCalendarV2DayButton">
  <ContentView.Content>
      <Grid 
          x:Name="template">
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped"/>
            </Grid.GestureRecognizers>

            <Frame
                x:Name="btnFrame"
                BackgroundColor="Transparent"
                VerticalOptions="Center"
                HorizontalOptions="Center"
                Padding="0"
                HasShadow="False"
                IsClippedToBounds="True"
                CornerRadius="0"
                WidthRequest="36"
                HeightRequest="36">
                <Label 
                    VerticalOptions="Center"
                    HorizontalOptions="Center"
                    x:Name="dayLabel"
                    FontSize="17"
                    FontAttributes="Bold"
                    TextColor="#363B40"
                    Text="" />

            </Frame>
          
      </Grid>
  </ContentView.Content>
</ContentView>