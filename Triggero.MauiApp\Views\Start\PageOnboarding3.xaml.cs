﻿
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Controls;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Views.Pages.Auth;
using RelayCommand = Triggero.MauiMobileApp.Extensions.Helpers.RelayCommand;


namespace Triggero.MauiMobileApp.Views.Pages.Start
{

    public partial class PageOnboarding3 : ContentPage
    {
        public PageOnboarding3()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }
        protected override async void OnAppearing()
        {

            MainThread.BeginInvokeOnMainThread(() =>
            {

                var animatedImage = new GifCachedImage
                {
                    HeightRequest = 269,
                    WidthRequest = 242,
                    Opacity = 0,

                    VerticalOptions = LayoutOptions.Start,
                    HorizontalOptions = LayoutOptions.Center,
                    //Aspect = Aspect.AspectFit,
                    Source = "animationscooter.gif"
                };
                animatedLayout.Children.Insert(0, animatedImage);

                animatedImage.Success += async (sender, e) =>
                {
                    await Task.Delay(500);
                    animatedImage.Opacity = 1;
                    notAnimatedImg.Opacity = 0;
                };
            });

        }



        private RelayCommand goToLogin;
        public RelayCommand GoToLogin
        {
            get => goToLogin ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new LoginStartPage());
            });
        }

        private RelayCommand goToRegistration;
        public RelayCommand GoToRegistration
        {
            get => goToRegistration ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new RegistrationPage());
            });
        }
    }
}