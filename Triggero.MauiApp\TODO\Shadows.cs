﻿using System.Collections.Concurrent;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq.Expressions;
using System.Reflection;
using System.Runtime.CompilerServices;

namespace Triggero.MauiMobileApp.Controls;

/// <summary>
/// SHARPNADO
/// </summary>
public class Shadows : ContentView
{
    public static readonly BindableProperty CornerRadiusProperty = BindableProperty.Create(
        nameof(CornerRadius),
        typeof(int),
        typeof(Shadows),
        DefaultCornerRadius);

    public static readonly BindableProperty ShadesProperty = BindableProperty.Create(
        nameof(Shades),
        typeof(IEnumerable<Shade>),
        typeof(Shadows),
        defaultValueCreator: (bo) => new ObservableCollection<Shade> { new Shade { Parent = (Shadows)bo } },
        validateValue: (bo, v) => v is IEnumerable<Shade>,
        propertyChanged: ShadesPropertyChanged,
        coerceValue: CoerceShades);

    private const int DefaultCornerRadius = 0;

    private static int instanceCount = 0;

    private readonly WeakEventSource<NotifyCollectionChangedEventArgs> _weakCollectionChangedSource = new WeakEventSource<NotifyCollectionChangedEventArgs>();

    public Shadows()
    {
        InstanceNumber = ++instanceCount;
    }

    public event EventHandler<NotifyCollectionChangedEventArgs> WeakCollectionChanged
    {
        add => _weakCollectionChangedSource.Subscribe(value);
        remove => _weakCollectionChangedSource.Unsubscribe(value);
    }

    public int InstanceNumber { get; }

    public int CornerRadius
    {
        get => (int)GetValue(CornerRadiusProperty);
        set => SetValue(CornerRadiusProperty, value);
    }

    public IEnumerable<Shade> Shades
    {
        get => (IEnumerable<Shade>)GetValue(ShadesProperty);
        set => SetValue(ShadesProperty, value);
    }

    protected override void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();

        foreach (var shade in Shades)
        {
            shade.BindingContext = BindingContext;
        }
    }

    private static object CoerceShades(BindableObject bindable, object value)
    {
        if (!(value is ReadOnlyCollection<Shade> readonlyCollection))
        {
            return value;
        }

        return new ReadOnlyCollection<Shade>(
            readonlyCollection.Select(s => s.Clone())
                .ToList());
    }

    private static void ShadesPropertyChanged(BindableObject bindable, object oldvalue, object newvalue)
    {
        var shadows = (Shadows)bindable;
        var enumerableShades = (IEnumerable<Shade>)newvalue;

        if (oldvalue != null)
        {
            if (oldvalue is INotifyCollectionChanged oldCollection)
            {
                oldCollection.CollectionChanged -= shadows.OnShadeCollectionChanged;
            }

            foreach (var shade in enumerableShades)
            {
                shade.Parent = null;
                shade.BindingContext = null;
            }
        }

        foreach (var shade in enumerableShades)
        {
            shade.Parent = shadows;
            shade.BindingContext = shadows.BindingContext;
        }

        if (newvalue is INotifyCollectionChanged newCollection)
        {
            newCollection.CollectionChanged += shadows.OnShadeCollectionChanged;
        }
    }

    private void OnShadeCollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
    {
        switch (e.Action)
        {
            case NotifyCollectionChangedAction.Add:
                foreach (Shade newShade in e.NewItems)
                {
                    newShade.Parent = this;
                    newShade.BindingContext = BindingContext;
                    _weakCollectionChangedSource.Raise(this, e);
                }

                break;

            case NotifyCollectionChangedAction.Reset:
            case NotifyCollectionChangedAction.Remove:
                foreach (Shade oldShade in e.OldItems ?? new Shade[0])
                {
                    oldShade.Parent = null;
                    oldShade.BindingContext = null;
                    _weakCollectionChangedSource.Raise(this, e);
                }

                break;
        }
    }
}

public class Shade : Element
{
    public static readonly BindableProperty OffsetProperty = BindableProperty.Create(
        nameof(Offset),
        typeof(Point),
        typeof(Shade),
        defaultValueCreator: _ => DefaultOffset);

    public static readonly BindableProperty ColorProperty = BindableProperty.Create(
        nameof(Color),
        typeof(Color),
        typeof(Shade),
        defaultValueCreator: _ => DefaultColor);

    public static readonly BindableProperty OpacityProperty = BindableProperty.Create(
        nameof(Opacity),
        typeof(double),
        typeof(Shade),
        defaultValue: DefaultOpacity);

    public static readonly BindableProperty BlurRadiusProperty = BindableProperty.Create(
        nameof(BlurRadius),
        typeof(double),
        typeof(Shade),
        DefaultBlurRadius);

    private const double DefaultBlurRadius = 12f;

    private const float DefaultOpacity = 0.24f;

    private static readonly Color DefaultColor = new Color(0, 0, 0, DefaultOpacity);

    private static readonly Point DefaultOffset = new Point(0, 8);

    private readonly WeakEventSource<PropertyChangedEventArgs> _weakPropertyChangedSource = new WeakEventSource<PropertyChangedEventArgs>();

    public event EventHandler<PropertyChangedEventArgs> WeakPropertyChanged
    {
        add => _weakPropertyChangedSource.Subscribe(value);
        remove => _weakPropertyChangedSource.Unsubscribe(value);
    }

    public Point Offset
    {
        get => (Point)GetValue(OffsetProperty);
        set => SetValue(OffsetProperty, value);
    }

    public Color Color
    {
        get => (Color)GetValue(ColorProperty);
        set => SetValue(ColorProperty, value);
    }

    public double Opacity
    {
        get => (double)GetValue(OpacityProperty);
        set => SetValue(OpacityProperty, value);
    }

    public double BlurRadius
    {
        get => (double)GetValue(BlurRadiusProperty);
        set => SetValue(BlurRadiusProperty, value);
    }

    public static bool IsShadeProperty(string propertyName)
    {
        return propertyName == nameof(Offset)
               || propertyName == nameof(Color)
               || propertyName == nameof(Opacity)
               || propertyName == nameof(BlurRadius);
    }

    public override string ToString() =>
        $"{{ Offset: {Offset}, Opacity: {Opacity}, BlurRadius: {BlurRadius} }}";

    public Shade Clone()
    {
        return new Shade { BlurRadius = BlurRadius, Color = Color, Offset = Offset, Opacity = Opacity };
    }

    protected override void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();
    }

    protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        base.OnPropertyChanged(propertyName);

        _weakPropertyChangedSource.Raise(this, new PropertyChangedEventArgs(propertyName));
    }
}

/// <summary>
/// An event with weak subscription, i.e. it won't keep handlers from being garbage collected.
/// </summary>
/// <typeparam name="TEventArgs">The type of the event's arguments.</typeparam>
public class WeakEventSource<TEventArgs>
{
 

    /// <summary>
    /// Raises the event by invoking each handler that hasn't been garbage collected.
    /// </summary>
    /// <param name="sender">The source of the event.</param>
    /// <param name="args">An object that contains the event data.</param>
    /// <remarks>The handlers are invoked one after the other, in the order they were subscribed in.</remarks>
    public void Raise(object? sender, TEventArgs args)
    {
        //var validHandlers = GetValidHandlers(_handlers);
        //foreach (var handler in validHandlers)
        //{
        //    handler.Invoke(sender, args);
        //}
    }

    /// <summary>
    /// Raises the event by invoking each handler that hasn't been garbage collected. Exceptions thrown by
    /// individual handlers are passed to the specified <c>exceptionHandler</c> to decide what to do with them.
    /// </summary>
    /// <param name="sender">The source of the event.</param>
    /// <param name="args">An object that contains the event data.</param>
    /// <param name="exceptionHandler">A delegate that handles exceptions thrown by individual handlers.
    /// Return <c>true</c> to indicate that the exception was handled.</param>
    /// <remarks>The handlers are invoked one after the other, in the order they were subscribed in.</remarks>
    public void Raise(object? sender, TEventArgs args, Func<Exception, bool> exceptionHandler)
    {
        if (exceptionHandler is null) throw new ArgumentNullException(nameof(exceptionHandler));
        //var validHandlers = GetValidHandlers(_handlers);
        //foreach (var handler in validHandlers)
        //{
        //    try
        //    {
        //        handler.Invoke(sender, args);
        //    }
        //    catch (Exception ex) when (exceptionHandler(ex))
        //    {
        //    }
        //}
    }

    /// <summary>
    /// Adds an event handler.
    /// </summary>
    /// <param name="handler">The handler to subscribe.</param>
    /// <remarks>Only a weak reference to the handler's <c>Target</c> is kept, so that it can be garbage collected.</remarks>
    public void Subscribe(EventHandler<TEventArgs> handler)
    {
        Subscribe(null, handler);
    }

    /// <summary>
    /// Adds an event handler, specifying a lifetime object.
    /// </summary>
    /// <param name="lifetimeObject">An object that keeps the handler alive as long as it's alive.</param>
    /// <param name="handler">The handler to subscribe.</param>
    /// <remarks>Only a weak reference to the handler's <c>Target</c> is kept, so that it can be garbage collected.
    /// However, as long as the <c>lifetime</c> object is alive, the handler will be kept alive. This is useful for
    /// subscribing with anonymous methods (e.g. lambda expressions).</remarks>
    public void Subscribe(object? lifetimeObject, EventHandler<TEventArgs> handler)
    {
         
    }

    /// <summary>
    /// Removes an event handler.
    /// </summary>
    /// <param name="handler">The handler to unsubscribe.</param>
    /// <remarks>The behavior is the same as that of <see cref="Delegate.Remove(Delegate, Delegate)"/>. Only the last instance
    /// of the handler's invocation list is removed. If the exact invocation list is not found, nothing is removed.</remarks>
    public void Unsubscribe(EventHandler<TEventArgs> handler)
    {
         
    }

    /// <summary>
    /// Removes an event handler that was subscribed with a lifetime object.
    /// </summary>
    /// <param name="lifetimeObject">The lifetime object that was associated with the handler.</param>
    /// <param name="handler">The handler to unsubscribe.</param>
    /// <remarks>The behavior is the same as that of <see cref="Delegate.Remove(Delegate, Delegate)"/>. Only the last instance
    /// of the handler's invocation list is removed. If the exact invocation list is not found, nothing is removed.</remarks>
    [Obsolete("This method is obsolete and will be removed in a future version. Use the Unsubscribe overload that doesn't take a lifetime object instead.")]
    public void Unsubscribe(object? lifetimeObject, EventHandler<TEventArgs> handler)
    {
        Unsubscribe(handler);
    }

    internal delegate void OpenEventHandler(object? target, object? sender, TEventArgs e);

    internal struct StrongHandler
    {
        private readonly object? _target;
        private readonly OpenEventHandler _openHandler;

        public StrongHandler(object? target, OpenEventHandler openHandler)
        {
            _target = target;
            _openHandler = openHandler;
        }

        public void Invoke(object? sender, TEventArgs e)
        {
            _openHandler(_target, sender, e);
        }
    }

 
}