﻿using Newtonsoft.Json;
using System.IO;
using Triggero.Models.General;

namespace Triggero.MauiMobileApp.Extensions.Helpers.Modules
{
    public class ConfigData
    {
        public static string FilePath = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + @"/configData.json";
        
        public bool IsFirstUse { get; set; } = true;
        public bool WasShownStartTutorial { get; set; } = false;
        public bool ShownTutorialV1 { get; set; } = false;

        public string AuthToken { get; set; }
        public int UserId { get; set; }

        public User User { get; set; }

        public void SaveChangesToMemory()
        {
            try
            {
                // Ensure the directory exists before writing the file
                var directory = Path.GetDirectoryName(FilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonConvert.SerializeObject(this);
                File.WriteAllText(FilePath, json);
            }
            catch { }
        }
    }
}
