﻿using System.Collections.Generic;
using HtmlAgilityPack;

namespace Triggero.MauiMobileApp.Extensions
{
    public static class StringExtensions
    {
        public static string CombineUrl(this string baseUrl, string endUrl)
        {
            if (string.IsNullOrEmpty(baseUrl) || string.IsNullOrEmpty(endUrl))
                return string.Empty;

            baseUrl = baseUrl.TrimEnd('/');
            endUrl = endUrl.TrimStart('/');
            return string.Format("{0}/{1}", baseUrl, endUrl);
        }

        public static string AddBaseUrl(this string endUrl, string baseUrl)
        {
            if (string.IsNullOrEmpty(baseUrl) || string.IsNullOrEmpty(endUrl))
                return string.Empty;

            baseUrl = baseUrl.TrimEnd('/');
            endUrl = endUrl.TrimStart('/');
            return string.Format("{0}/{1}", baseUrl, endUrl);
        }

        public static string Capitilize(this string str)
        {
            return char.ToUpper(str[0]) + str.Substring(1);
        }

        public static string SafeSubstring(this string str, int start, int length)
        {
            if (str.Length >= start + length)
            {
                return ExtractText(str).Substring(start, length);
            }
            return str;
        }
        public static string SafeSubstringWithStr(this string str, int start, int length, string withStr)
        {
            if (str.Length >= start + length)
            {
                return ExtractText(str).Substring(start, length) + withStr;
            }
            return str;
        }

        public static string ExtractText(string html)
        {
            if (html == null)
            {
                throw new ArgumentNullException("html");
            }

            HtmlDocument doc = new HtmlDocument();
            doc.LoadHtml(html);

            var chunks = new List<string>();

            foreach (var item in doc.DocumentNode.DescendantsAndSelf())
            {
                if (item.NodeType == HtmlNodeType.Text)
                {
                    if (item.InnerText.Trim() != "")
                    {
                        chunks.Add(item.InnerText.Trim());
                    }
                }
            }
            return String.Join(" ", chunks);
        }
    }
}
