﻿
using System.Windows.Input;


namespace Triggero.MauiMobileApp.Views.Pages.Legal
{

    public partial class TermsPage : ContentPage
    {
        public TermsPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
    }
}