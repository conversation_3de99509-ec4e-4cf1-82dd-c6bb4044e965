﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Practices;
using Triggero.Models.Tests;



namespace Triggero.Controls.Chat.AdviceAttachments
{

    public partial class TestAdviceAttachment : ContentView
    {
        private Test _test;
        public TestAdviceAttachment(Test test)
        {
            _test = test;
            InitializeComponent();

            Load();
        }


        private void Load()
        {
            titleLabel.Text = _test.GetLocalizedTitle(LanguageHelper.LangCode);
            img.Source = Constants.BuildContentUrl(_test.IconImgPath);
            questions.Text = _test.Questions.Count.ToString();
        }


        public event EventHandler<Test> Clicked;
        private void onClick(object sender, EventArgs e)
        {
            Clicked?.Invoke(this, _test);
        }
    }
}