﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.General.Influence;



namespace Triggero.Controls.Other
{

    public partial class NeedToHandleItem : ContentView
    {
        private NeedToHandle _item;
        public NeedToHandleItem(NeedToHandle item)
        {
            _item = item;

            InitializeComponent();
            titleLabel.Text = item.GetLocalizedTitle(LanguageHelper.LangCode);
        }



        public event EventHandler<NeedToHandle> Tapped;
        private void onTapped(object sender, EventArgs e)
        {
            Tapped?.Invoke(this, _item);
        }
    }
}