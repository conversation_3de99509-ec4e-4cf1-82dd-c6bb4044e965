<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiPort.Views.Pages.CustomHandlersTestPage"
             Title="Custom Handlers Test">
    
    <ScrollView>
        <StackLayout Padding="20" Spacing="20">
            
            <Label Text="Custom Entry and Editor Handlers Test" 
                   FontSize="24" 
                   FontAttributes="Bold" 
                   HorizontalOptions="Center" />
            
            <Label Text="These Entry and Editor controls should have no native padding or underlines:" 
                   FontSize="16" 
                   HorizontalOptions="Center" />
            
            <!-- Standard Entry Test -->
            <StackLayout Spacing="10">
                <Label Text="Standard Entry (with custom handler):" FontAttributes="Bold" />
                <Frame BackgroundColor="LightGray" Padding="5" HasShadow="False">
                    <Entry x:Name="TestEntry" 
                           Placeholder="Type here - no padding/underline" 
                           BackgroundColor="White"
                           TextColor="Black" />
                </Frame>
            </StackLayout>
            
            <!-- Password Entry Test -->
            <StackLayout Spacing="10">
                <Label Text="Password Entry (with custom handler):" FontAttributes="Bold" />
                <Frame BackgroundColor="LightGray" Padding="5" HasShadow="False">
                    <Entry x:Name="PasswordEntry" 
                           Placeholder="Password - no padding/underline" 
                           IsPassword="True"
                           BackgroundColor="White"
                           TextColor="Black" />
                </Frame>
            </StackLayout>
            
            <!-- Editor Test -->
            <StackLayout Spacing="10">
                <Label Text="Editor (with custom handler):" FontAttributes="Bold" />
                <Frame BackgroundColor="LightGray" Padding="5" HasShadow="False">
                    <Editor x:Name="TestEditor" 
                            Placeholder="Multi-line text - no padding/underline" 
                            HeightRequest="100"
                            BackgroundColor="White"
                            TextColor="Black" />
                </Frame>
            </StackLayout>
            
            <!-- Comparison with Frame -->
            <StackLayout Spacing="10">
                <Label Text="For comparison - Entry without Frame:" FontAttributes="Bold" />
                <Entry x:Name="ComparisonEntry" 
                       Placeholder="Direct Entry - should show no native styling" 
                       BackgroundColor="LightBlue"
                       TextColor="Black" />
            </StackLayout>
            
            <StackLayout Spacing="10">
                <Label Text="For comparison - Editor without Frame:" FontAttributes="Bold" />
                <Editor x:Name="ComparisonEditor" 
                        Placeholder="Direct Editor - should show no native styling" 
                        HeightRequest="80"
                        BackgroundColor="LightBlue"
                        TextColor="Black" />
            </StackLayout>
            
            <!-- Test Button -->
            <Button x:Name="TestButton" 
                    Text="Test Focus/Unfocus" 
                    Clicked="OnTestButtonClicked" 
                    BackgroundColor="DodgerBlue" 
                    TextColor="White" />
            
            <Label x:Name="StatusLabel" 
                   Text="Tap the button to test focus behavior" 
                   FontSize="14" 
                   HorizontalOptions="Center" />
            
        </StackLayout>
    </ScrollView>
    
</ContentPage>
