﻿<?xml version="1.0" encoding="UTF-8"?>

<draw:Canvas xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Parts.TransparentFooter"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"

             Margin="0,20,0,0"
             HeightRequest="100"
             HorizontalOptions="Fill"
             VerticalOptions="Start"

             x:Name="this">

    <draw:SkiaLayout
        UseCache="Image"
        HeightRequest="100"
        HorizontalOptions="Fill">

        <!--round btn-->
        <draw:SkiaShape
            Padding="0"
            BackgroundColor="White"
            Type="Circle"
            HeightRequest="68"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            WidthRequest="68" />

        <draw:SkiaShape
            HorizontalOptions="Fill"
            Padding="0"
            Background="White"
            BackgroundColor="White"
            CornerRadius="10"
            HeightRequest="76"
            VerticalOptions="End" />

        <!--<parts:TransparentFooter
                          HorizontalOptions="Fill"
                          InputTransparent="False" />-->

        <draw:SkiaGrid VerticalOptions="Fill" ColumnSpacing="0">

            <draw:SkiaGrid.ColumnDefinitions>
                <ColumnDefinition Width="1*" />
                <ColumnDefinition Width="1*" />
                <ColumnDefinition Width="68" />
                <ColumnDefinition Width="1*" />
                <ColumnDefinition Width="1*" />
            </draw:SkiaGrid.ColumnDefinitions>


            <!--HOME-->
            <!--IsChecked="{Binding Source={x:Reference this},Path=IsMainPageSelected,Mode=TwoWay}"-->
            <draw:SkiaLayout
                Padding="8"
                HorizontalOptions="Fill"
                Margin="0,0,0,8"
                VerticalOptions="End">

                <draw:SkiaStack
                    HorizontalOptions="Fill">

                    <draw:SkiaImage
                        WidthRequest="16"
                        HeightRequest="18"
                        HorizontalOptions="Center"
                        VerticalOptions="Start"
                        Source="footermain.png">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsMainPageSelected}"
                                TargetType="draw:SkiaImage"
                                Value="True">
                                <Setter Property="Source" Value="footermainchecked.png" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsMainPageSelected}"
                                TargetType="draw:SkiaImage"
                                Value="False">
                                <Setter Property="Source" Value="footermain.png" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                    </draw:SkiaImage>

                    <draw:SkiaLabel
                        TextColor="{x:StaticResource ColorPrimaryLight}"
                        FontSize="10"
                        HorizontalTextAlignment="Center"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MainPage.FooterMain}">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsMainPageSelected}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{x:StaticResource ColorPrimary}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsMainPageSelected}"
                                TargetType="draw:SkiaLabel"
                                Value="False">
                                <Setter Property="TextColor" Value="{x:StaticResource ColorPrimaryLight}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                    </draw:SkiaLabel>

                </draw:SkiaStack>


            </draw:SkiaLayout>

            <!--LIB-->
            <!--IsChecked="{Binding Source={x:Reference this},Path=IsLibraryPageSelected,Mode=TwoWay}"-->
            <draw:SkiaLayout
                Grid.Column="1"
                Padding="8"
                HorizontalOptions="Fill"
                Margin="0,0,0,8"
                VerticalOptions="End">

                <draw:SkiaStack
                    HorizontalOptions="Fill">

                    <draw:SkiaImage
                        WidthRequest="16"
                        HeightRequest="18"
                        HorizontalOptions="Center"
                        VerticalOptions="Start"
                        Source="footerlibrary.png">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsLibraryPageSelected}"
                                TargetType="draw:SkiaImage"
                                Value="True">
                                <Setter Property="Source" Value="footerlibrarychecked.png" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsLibraryPageSelected}"
                                TargetType="draw:SkiaImage"
                                Value="False">
                                <Setter Property="Source" Value="footerlibrary.png" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                    </draw:SkiaImage>

                    <draw:SkiaLabel
                        TextColor="{x:StaticResource ColorPrimaryLight}"
                        FontSize="10"
                        HorizontalTextAlignment="Center"
                        HorizontalOptions="Center"
                        MaxLines="1"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MainPage.FooterLibrary}">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsLibraryPageSelected}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{x:StaticResource ColorPrimary}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsLibraryPageSelected}"
                                TargetType="draw:SkiaLabel"
                                Value="False">
                                <Setter Property="TextColor" Value="{x:StaticResource ColorPrimaryLight}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                    </draw:SkiaLabel>

                </draw:SkiaStack>

            </draw:SkiaLayout>

            <!--TRACKER-->
            <!--IsChecked="{Binding Source={x:Reference this},Path=IsLibraryPageSelected,Mode=TwoWay}"-->
   

                    <draw:SkiaImage
                        Grid.Column="2"
                        Margin="0,6,0,0"
                        Aspect="AspectFit"
                        HeightRequest="56"
                        WidthRequest="56"
                        HorizontalOptions="Center"
                        VerticalOptions="Start"
                        Source="footerbigcircleplus.png" />
 


            <!--TESTS-->
            <!--IsChecked="{Binding Source={x:Reference this},Path=IsTestsPageSelected,Mode=TwoWay}"-->
            <draw:SkiaLayout
                Grid.Column="3"
                Padding="8"
                HorizontalOptions="Fill"
                Margin="0,0,0,8"
                VerticalOptions="End">

                <draw:SkiaStack
                    HorizontalOptions="Fill">

                    <draw:SkiaImage
                        WidthRequest="16"
                        HeightRequest="18"
                        HorizontalOptions="Center"
                        VerticalOptions="Start"
                        Source="footertests.png">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsTestsPageSelected}"
                                TargetType="draw:SkiaImage"
                                Value="True">
                                <Setter Property="Source" Value="footertestschecked.png" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsTestsPageSelected}"
                                TargetType="draw:SkiaImage"
                                Value="False">
                                <Setter Property="Source" Value="footertests.png" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                    </draw:SkiaImage>

                    <draw:SkiaLabel
                        TextColor="{x:StaticResource ColorPrimaryLight}"
                        FontSize="10"
                        HorizontalTextAlignment="Center"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MainPage.Tests}">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsTestsPageSelected}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{x:StaticResource ColorPrimary}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsTestsPageSelected}"
                                TargetType="draw:SkiaLabel"
                                Value="False">
                                <Setter Property="TextColor" Value="{x:StaticResource ColorPrimaryLight}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                    </draw:SkiaLabel>

                </draw:SkiaStack>

            </draw:SkiaLayout>

            <!--CHAT-->
            <!--IsChecked="{Binding Source={x:Reference this},Path=IsChatBotPageSelected,Mode=TwoWay}"-->
            <draw:SkiaLayout
                Grid.Column="4"
                Padding="8"
                HorizontalOptions="Fill"
                Margin="0,0,0,8"
                VerticalOptions="End">

                <draw:SkiaStack
                    HorizontalOptions="Fill">

                    <draw:SkiaImage
                        WidthRequest="16"
                        HeightRequest="18"
                        HorizontalOptions="Center"
                        VerticalOptions="Start"
                        Source="footerchatbot.png">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsChatBotPageSelected}"
                                TargetType="draw:SkiaImage"
                                Value="True">
                                <Setter Property="Source" Value="footerchatbotchecked.png" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsChatBotPageSelected}"
                                TargetType="draw:SkiaImage"
                                Value="False">
                                <Setter Property="Source" Value="footerchatbot.png" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                    </draw:SkiaImage>

                    <!--TextColor="{x:StaticResource ColorPrimary}"-->
                    <draw:SkiaLabel
                        TextColor="{x:StaticResource ColorPrimaryLight}"
                        FontSize="10"
                        HorizontalTextAlignment="Center"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MainPage.ChatBot}">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsChatBotPageSelected}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{x:StaticResource ColorPrimary}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={x:Reference this}, Path=IsChatBotPageSelected}"
                                TargetType="draw:SkiaLabel"
                                Value="False">
                                <Setter Property="TextColor" Value="{x:StaticResource ColorPrimaryLight}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                    </draw:SkiaLabel>

                </draw:SkiaStack>

            </draw:SkiaLayout>

        </draw:SkiaGrid>

    </draw:SkiaLayout>

</draw:Canvas>