﻿using MobileAPIWrapper;
using Newtonsoft.Json;
using MobileAPIWrapper.Helpers;
using System.Diagnostics;
using Triggero.Domain.Models;
using Microsoft.Maui.ApplicationModel;

namespace Triggero.MauiMobileApp.Views.Popups
{

    //PPup == төлборийн попап
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class YouKassaPopup : Popup
    {
        private CreatedPaymentModel _paymentModel;
        private bool _isOpen = true;

        public YouKassaPopup(CreatedPaymentModel paymentModel)
        {
            InitializeComponent();

            _paymentModel = paymentModel;
            
            webView.Navigating += WebView_OnNavigationStarted;
            webView.Source = paymentModel.ConfirmationUrl;

            _ = CheckPayment();
        }

        private async void WebView_OnNavigationStarted(object sender, WebNavigatingEventArgs e)
        {
            var url = e.Url;

            Debug.WriteLine($"[Web] started {url}");

            if (url == "https://yoomoney.ru/" || url.Contains("triggero.ru")) //return back to gateway if error?..
            {
                this.Close.Execute(null);
            }
            else
            if (url.Contains("https://yoomoney.ru/checkout/payments/v2/mir-pay/"))
            {
                e.Cancel = true; // Cancel the navigation to handle it manually
                Debug.WriteLine($"[Web] got link for MIRPAY {url}");

                try
                {
                    var resp = await RequestHelper.ExecuteRequestAsync(url, Method.Get);
                    var obj = JsonConvert.DeserializeObject<MirPayResponse>(resp.Content);

                    var redirectLink = obj.payload.paymentLink;

                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        try
                        {
                            // Use MAUI Launcher instead of DependencyService
                            var supportsUri = await Launcher.CanOpenAsync(redirectLink);
                            if (supportsUri)
                            {
                                await Launcher.OpenAsync(redirectLink);
                            }
                            else
                            {
                                await App.Current.MainPage.DisplayAlert("Ошибка", "Необходимо установить приложение MirPay", "Ок");
                            }
                        }
                        catch (Exception ex)
                        {
                            await App.Current.MainPage.DisplayAlert("Ошибка", ex.ToString(), "Ок");
                        }
                    });
                }
                catch (Exception eer)
                {
                    await App.Current.MainPage.DisplayAlert("Ошибка", eer.ToString(), "Ок");
                }
            }
        }

        // TODO: OnDisappearing doesn't exist in Popup - need to find alternative lifecycle method
        // For now, using a different approach to handle popup closing
        private void HandlePopupClosed()
        {
            _isOpen = false;
        }

        public event EventHandler PaymentProceed;

        private async Task CheckPayment()
        {

            while (_isOpen)
            {

                await Task.Delay(3000);

                bool isPaid = await TriggeroMobileAPI.Payment.CheckPayment(_paymentModel.PaymentId);

                if (isPaid)
                {
                    _isOpen = false;
                    HandlePopupClosed();

                    await this.CloseAsync();
                    PaymentProceed?.Invoke(this, EventArgs.Empty);
                }

            }
        }

        private RelayCommand close;
        public RelayCommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    HandlePopupClosed();
                    await this.CloseAsync();
                });
            });
        }
    }

    public class MirPayResponse
    {
        public string status { get; set; }
        public MirPayResponsePayload payload { get; set; }
    }

    public class MirPayResponsePayload
    {
        public string paymentLink { get; set; }

    }
}