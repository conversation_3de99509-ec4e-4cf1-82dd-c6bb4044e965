﻿using Triggero.MauiMobileApp.Extensions;
using Triggero.Models.Practices;
using Triggero.MauiMobileApp.Views.Pages.Library;

using Triggero.MauiMobileApp.Extensions.Helpers;

namespace Triggero.Controls.Cards.NextItemCard
{

    public partial class ExerciseNextItemCard : ContentView
    {
        private Exercise _exercise;

        public ExerciseNextItemCard()
        {
            InitializeComponent();
        }
        public ExerciseNextItemCard(Exercise exercise)
        {
            InitializeComponent();
            Build(exercise);
        }
        public async void Build(Exercise exercise)
        {
            _exercise = exercise;



            titleLabel.Text = exercise.GetLocalizedTitle(LanguageHelper.LangCode);
            var desc = exercise.GetLocalizedDescription(LanguageHelper.LangCode);
            desc = StringExtensions.ExtractText(desc);
            if (desc.Length > 27 * 2)
            {
                desc = desc.Substring(0, 27 * 2) + "...";
            }
            descLabel.Text = desc;


            img.Source = await ResorcesHelper.GetImageSource(_exercise.ImgPath);
        }

        private async void tapped(object sender, EventArgs e)
        {
            if (_exercise != null)
            {
                App.OpenPage(new ExercisePage(_exercise));
            }
        }
    }
}