﻿using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using AndroidX.Core.App;

namespace Triggero.MauiMobileApp
{
    [Activity(
        Theme = "@style/MainTheme",
        MainLauncher = true, LaunchMode = LaunchMode.SingleTop,
        ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation
        | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize | ConfigChanges.Density,
        ScreenOrientation = ScreenOrientation.SensorPortrait)]
    [IntentFilter(
        new[] {
            Shiny.ShinyPushIntents.NotificationClickAction
        },
        Categories = new[] {
            "android.intent.category.DEFAULT"
        })]
    public class MainActivity : MauiAppCompatActivity
    {
        internal static readonly string CHANNEL_ID = "triggero_notification_channel";
        internal static readonly int NOTIFICATION_ID = 100;

        protected override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            // Set up notification channel for Android Oreo and above
            if (Build.VERSION.SdkInt >= BuildVersionCodes.O)
            {
                CreateNotificationChannel();
            }
        }

        private void CreateNotificationChannel()
        {
            var channel = new NotificationChannel(CHANNEL_ID, "Triggero Notifications", NotificationImportance.High)
            {
                Description = "Notifications from Triggero app"
            };

            var notificationManager = GetSystemService(NotificationService) as NotificationManager;
            notificationManager?.CreateNotificationChannel(channel);
        }

        protected override void OnNewIntent(Intent intent)
        {
            base.OnNewIntent(intent);

            // Handle notification intents - Shiny will automatically process these
            // but we can add custom logic here if needed
        }
    }
}
