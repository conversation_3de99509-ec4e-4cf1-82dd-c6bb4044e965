﻿

using System.Windows.Input;


namespace Triggero.MauiMobileApp.Views.Browse
{

    public partial class Webpage
    {
        private string _Title;
        public string Title
        {
            get
            {
                return _Title;
            }
            set
            {
                if (_Title != value)
                {
                    _Title = value;
                    OnPropertyChanged();
                }
            }
        }

        public Webpage()
        {
            InitializeComponent();

            Title = "Просмотр";

            BindingContext = this;
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                App.ClosePage(this);
            });
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();

            //Device.StartTimer(TimeSpan.FromMilliseconds(50), () =>
            //{
            //    Settings.UpdateStatusBarUponTheme();

            //    return false;// Don't repeat the timer 
            //});

        }

        public Webpage(string title, string source, bool isUrl = true)
        {
            InitializeComponent();

            Title = title;

            BindingContext = this;

            if (isUrl)
            {
                if (string.IsNullOrEmpty(source))
                {
                    source = "about:blank";
                }
                var url = new UrlWebViewSource
                {
                    Url = source
                };
                ControlBrowser.Source = url;
            }
            else
            {
                if (string.IsNullOrEmpty(source))
                {
                    source = "";
                }
                var html = new HtmlWebViewSource
                {
                    Html = source
                };
                ControlBrowser.Source = html;


            }

        }




    }


}