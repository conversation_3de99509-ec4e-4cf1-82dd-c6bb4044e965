﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Practices;

namespace Triggero.MauiMobileApp.ViewModels;

public class SearchPageViewModel : ElementsListViewModel
{
    public SearchPageViewModel()
    {
        Title = App.Instance.Interface.SearchPage.Search;
    }

    private bool _NothingFound;
    public bool NothingFound
    {
        get
        {
            return _NothingFound;
        }
        set
        {
            if (_NothingFound != value)
            {
                _NothingFound = value;
                OnPropertyChanged();
            }
        }
    }

    protected override void OnItemsChanged()
    {
        base.OnItemsChanged();

        NothingFound = IsEmpty;
    }


    public override Color ThemeColor
    {
        get
        {
            return Color.FromHex("#FDCE72");
        }
    }

    public override Color ThemeColorB
    {
        get
        {
            return Color.FromHex("#FDCE72");
        }
    }

    private string _SearchQuery;
    public string SearchQuery
    {
        get
        {
            return _SearchQuery;
        }
        set
        {
            if (_SearchQuery != value)
            {
                _SearchQuery = value;
                OnPropertyChanged();
            }
        }
    }

    private bool _IsSearching;
    public bool IsSearching
    {
        get
        {
            return _IsSearching;
        }
        set
        {
            if (_IsSearching != value)
            {
                _IsSearching = value;
                OnPropertyChanged();
            }
        }
    }





    protected override async Task<IEnumerable<IElementDetails>> LoadItemsAsync()
    {
        NothingFound = false;
        await Task.Delay(10);

        var tests = ApplicationState.Data.Tests;
        var exercises = ApplicationState.Data.Exercises;
        var topics = ApplicationState.Data.Topics;
        var practices = ApplicationState.Data.Practices;

        if (SearchQuery == null || SearchQuery.Length < 3)
        {
            FoundCount = 0;

            return new List<IElementDetails>();
        }

        var foundTests = tests.Where(o => o.Title.ToLower().Contains(SearchQuery.ToLower())
                                          || o.MainTags.ToLower().Contains(SearchQuery.ToLower())
                                          || o.SecondaryTags.ToLower().Contains(SearchQuery.ToLower())
                                          || o.Localizations.Any(o => o.Title.ToLower().Contains(SearchQuery.ToLower()))).ToList();

        var foundExercises = exercises.Where(o => o.Title.ToLower().Contains(SearchQuery.ToLower())
                                                  || o.MainTags.ToLower().Contains(SearchQuery.ToLower())
                                                  || o.SecondaryTags.ToLower().Contains(SearchQuery.ToLower())
                                                  || o.Localizations.Any(o => o.Title.ToLower().Contains(SearchQuery.ToLower()))).ToList();

        var foundTopics = topics.Where(o => o.Title.ToLower().Contains(SearchQuery.ToLower())
                                            || o.MainTags.ToLower().Contains(SearchQuery.ToLower())
                                            || o.SecondaryTags.ToLower().Contains(SearchQuery.ToLower())
                                            || o.Localizations.Any(o => o.Title.ToLower().Contains(SearchQuery.ToLower()))).ToList();

        var foundPractices = practices.Where(o => o.Title.ToLower().Contains(SearchQuery.ToLower())
                                                  || o.MainTags.ToLower().Contains(SearchQuery.ToLower())
                                                  || o.SecondaryTags.ToLower().Contains(SearchQuery.ToLower())
                                                  || o.Localizations.Any(o => o.Title.ToLower().Contains(SearchQuery.ToLower()))).ToList();


        FoundCount = foundTests.Count() + foundExercises.Count() + foundTopics.Count() + foundPractices.Count();

        //var itemsSource = new List<IElementDetails>();
        //itemsSource.AddRange(foundTests);
        //itemsSource.AddRange(foundExercises);
        //itemsSource.AddRange(foundTopics);
        //itemsSource.AddRange(foundPractices);

        var itemsSource = foundTests.Concat<IElementDetails>(foundExercises)
            .Concat(foundTopics)
            .Concat(foundTests)
            .Concat(foundPractices)
            .Distinct()
            .ToList();

        return itemsSource;
    }

    private int _FoundCount;
    public int FoundCount
    {
        get
        {
            return _FoundCount;
        }
        set
        {
            if (_FoundCount != value)
            {
                _FoundCount = value;
                OnPropertyChanged();
            }
        }
    }


    private FavoriteSectionType favoriteSectionType = FavoriteSectionType.Exercises;
    public FavoriteSectionType Section
    {
        get => favoriteSectionType;
        set
        {
            favoriteSectionType = value;
            OnPropertyChanged(nameof(FavoriteSectionType));
            LoadDataAsync().ConfigureAwait(false);
        }
    }



}