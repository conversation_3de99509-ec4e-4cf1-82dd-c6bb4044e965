﻿<?xml version="1.0" encoding="UTF-8" ?>
<draw:SkiaLayout
    x:Class="Triggero.MauiMobileApp.Views.Drawn.DrawnBreathTab"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    x:Name="ThisView"
    HorizontalOptions="Fill"
    VerticalOptions="Fill">

    <draw:SkiaScroll
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            Tag="ScrollBreath"
            Type="Column"
            UseCache="Image">

            <draw:SkiaImage
                Margin="0,4,0,0"
                HeightRequest="120"
                HorizontalOptions="Center"
                Source="animationbreath.png"
                Tag="Debug"
                WidthRequest="94" />

            <draw:SkiaLabel
                Margin="0,20,0,0"
                HorizontalOptions="Center"
                Style="{x:StaticResource StyleHeaderTextDrawn}"
                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.BreathPractice.BreathPracticeHeader}"
                TextColor="{x:StaticResource ColorText}"
                VerticalOptions="Start" />

            <draw:SkiaLabel
                FontSize="14"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.BreathPractice.BreathPracticeDescription}"
                TextColor="{x:StaticResource ColorText}"
                VerticalOptions="Start"
                WidthRequest="282" />

            <!--  4 ICONS  -->
            <draw:SkiaLayout
                Margin="20,20,20,0"
                ColumnDefinitions="1*,1*,1*,1*"
                HorizontalOptions="Fill"
                Type="Grid">


                <!--  TIME  -->
                <draw:SkiaLayout
                    HorizontalOptions="Fill"
                    Spacing="1"
                    Type="Column">

                    <draw:SkiaImage
                        AddMarginBottom="5"
                        HeightRequest="24"
                        HorizontalOptions="Center"
                        Source="timecircleaqua.png"
                        WidthRequest="24" />

                    <draw:SkiaLabel
                        FontAttributes="Bold"
                        FontSize="12"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.BreathPractice.Duration}"
                        TextColor="{x:StaticResource ColorText}" />

                    <draw:SkiaLabel
                        Margin="0,3,0,0"
                        FontSize="10"
                        HorizontalOptions="Center"
                        TextColor="#9A9D9F">
                        <draw:SkiaLabel.Spans>

                            <draw:TextSpan Text="{Binding Source={x:Reference ThisView}, Path=BreathPractice.TimeMinutes}" />

                            <draw:TextSpan Text=" " />

                            <draw:TextSpan Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.BreathPractice.MinutesAbbrevated}" />

                        </draw:SkiaLabel.Spans>
                    </draw:SkiaLabel>

                </draw:SkiaLayout>


                <!--  INHALE  -->
                <draw:SkiaLayout
                    Grid.Column="1"
                    HorizontalOptions="Fill"
                    Spacing="1"
                    Type="Column">

                    <draw:SkiaImage
                        AddMarginBottom="5"
                        HeightRequest="24"
                        HorizontalOptions="Center"
                        Source="inhaleaqua.png"
                        WidthRequest="24" />

                    <draw:SkiaLabel
                        FontAttributes="Bold"
                        FontSize="12"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.BreathPractice.Inhale}"
                        TextColor="{x:StaticResource ColorText}"
                        VerticalOptions="Start" />

                    <draw:SkiaLabel
                        x:Name="inhaleSecondsLabel"
                        Margin="0,3,0,0"
                        FontSize="10"
                        HorizontalOptions="Center"
                        TextColor="#9A9D9F"
                        VerticalOptions="Start">
                    </draw:SkiaLabel>

                </draw:SkiaLayout>


                <!--  PAUSE  -->
                <draw:SkiaLayout
                    Grid.Column="2"
                    HorizontalOptions="Fill"
                    Spacing="1"
                    Type="Column">
                    <draw:SkiaImage
                        AddMarginBottom="5"
                        HeightRequest="24"
                        HorizontalOptions="Center"
                        Source="inhaledelayaqua.png"
                        WidthRequest="24" />
                    <draw:SkiaLabel
                        FontAttributes="Bold"
                        FontSize="12"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.BreathPractice.Delays}"
                        TextColor="{x:StaticResource ColorText}"
                        VerticalOptions="Start" />
                    <draw:SkiaLabel
                        x:Name="delaySecondsLabel"
                        Margin="0,3,0,0"
                        FontSize="10"
                        HorizontalOptions="Center"
                        TextColor="#9A9D9F"
                        VerticalOptions="Start">
                    </draw:SkiaLabel>
                </draw:SkiaLayout>


                <!--  EXHALE  -->
                <draw:SkiaLayout
                    Grid.Column="3"
                    HorizontalOptions="Fill"
                    Spacing="1"
                    Type="Column">

                    <draw:SkiaImage
                        AddMarginBottom="5"
                        HeightRequest="24"
                        HorizontalOptions="Center"
                        Source="inhaleaqua.png"
                        WidthRequest="24" />

                    <draw:SkiaLabel
                        FontAttributes="Bold"
                        FontSize="12"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.BreathPractice.Exhale}"
                        TextColor="{x:StaticResource ColorText}"
                        VerticalOptions="Start" />

                    <draw:SkiaLabel
                        x:Name="exhaleSecondsLabel"
                        Margin="0,3,0,0"
                        FontSize="10"
                        HorizontalOptions="Center"
                        TextColor="#9A9D9F"
                        VerticalOptions="Start">
                    </draw:SkiaLabel>
                </draw:SkiaLayout>


            </draw:SkiaLayout>

            <draw:SkiaButton
                Margin="50,40,50,50"
                CommandTapped="{Binding Source={x:Reference ThisView}, Path=StartPractice}"
                FontFamily="FontTextBold"
                HeightRequest="43"
                Style="{x:StaticResource DrawnBtn}"
                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.BreathPractice.StartPractice}" />


            <draw:SkiaLayout 
                HeightRequest="{x:Static mobile:Globals.BottomOffsetForTabs}"
                WidthRequest="1"/>

        </draw:SkiaLayout>

    </draw:SkiaScroll>



</draw:SkiaLayout>