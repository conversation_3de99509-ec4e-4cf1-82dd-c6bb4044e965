﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;


namespace Triggero.MauiMobileApp.ViewModels;

public class ListExercisesViewModel : ElementsListViewModel
{
    private readonly ExerciseCategory _category;

    public ListExercisesViewModel(ExerciseCategory category)
    {
        _category = category;

        Title = _category.Localizations.GetLocalizedTitle(LanguageHelper.LangCode, _category.Title);
    }

    public override Color ThemeColorB
    {
        get
        {
            return Color.FromHex("#AEAA9F");
        }
    }

    public override Color ThemeColor
    {
        get
        {
            return Color.FromHex("#FFF8F2");
        }
    }

    protected override async Task<IEnumerable<IElementDetails>> LoadItemsAsync()
    {
        return await ApplicationState.Data.GetExercises(_category.Id); ;
    }



}