﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Class="Triggero.Controls.Cards.NextItemCard.TopicNextItemCard">
  <ContentView.Content>

        <Grid 
            Margin="20,0,20,0"
            VerticalOptions="Start"
            HeightRequest="200">
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="tapped"/>
            </Grid.GestureRecognizers>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="3*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>

            <Grid Grid.Column="0">

                <StackLayout
                    HorizontalOptions="Start"
                    VerticalOptions="Center"
                    Spacing="1">
                    <Label 
                        TextColor="{x:StaticResource ColorPrimary}"
                        FontSize="17"
                        Margin="0,0,0,0"
                        HorizontalOptions="Start"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Library.Library.GoNext}"/>
                    <Label 
                        x:Name="titleLabel"
                        TextColor="{x:StaticResource ColorText}"
                        FontSize="17"
                        Margin="0,20,70,0"
                        HorizontalOptions="Start"
                        Text="Как контролировать эмоции при стрессе?"/>
                    <Label 
                        x:Name="descLabel"
                        TextType="Html"
                        TextColor="{x:StaticResource ColorText}"
                        Opacity="0.5"
                        FontSize="11"
                        Margin="0,8,0,0"
                        HorizontalOptions="Start"
                        Text="Не следует, однако забывать, что даль..."/>
                </StackLayout>

            </Grid>

            <Grid Grid.Column="1">

                <Frame 
                    VerticalOptions="Center"
                    HorizontalOptions="Center"
                    HeightRequest="80"
                    WidthRequest="80"
                    Padding="0"
                    CornerRadius="15"
                    IsClippedToBounds="True"
                    HasShadow="False">
                    <Image
                        Aspect="AspectFill"
                        x:Name="img"/>
                </Frame>

            </Grid>

        </Grid>

    </ContentView.Content>
</ContentView>