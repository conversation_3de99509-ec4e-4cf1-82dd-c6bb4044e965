﻿<?xml version="1.0" encoding="UTF-8"?>

<models:BaseQuestionOptionView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
    xmlns:models="clr-namespace:Triggero.MauiPort.Models"
    x:Class="Triggero.Controls.Cards.Tests.Questions.Answers.Single.MultipleTextAnswerOption"
    x:Name="this"
    HorizontalOptions="Fill">
    <ContentView.Content>
        
        <Grid
            HorizontalOptions="Fill">

            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped" />
            </Grid.GestureRecognizers>

            <CheckBox
                InputTransparent="True"
                CheckedChanged="onChecked"
                IsChecked="{Binding Source={x:Reference this},Path=IsChecked,Mode=TwoWay}"
                IsVisible="False"
                x:Name="cb" />

            <!--UNCHECKED-->
            <Frame
                InputTransparent="True"
                HorizontalOptions="Fill"
                x:Name="uncheckedState"
                BackgroundColor="Transparent"
                BorderColor="{x:StaticResource ColorPrimaryLight}"
                HasShadow="False"
                Padding="0"
                CornerRadius="12">


                <Grid ColumnSpacing="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="64" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>


                    <Frame
                        Grid.Column="0"
                        WidthRequest="24"
                        HeightRequest="24"
                        CornerRadius="12"
                        HorizontalOptions="Center"
                        VerticalOptions="Center"
                        Padding="0"
                        HasShadow="False"
                        BackgroundColor="Transparent"
                        BorderColor="{x:StaticResource ColorPrimaryLight}" />

                    <Label
                        Grid.Column="1"
                        Margin="0,7,10,7"
                        HorizontalOptions="Fill"
                        VerticalOptions="Center"
                        FontSize="15"
                        Text="{Binding Source={x:Reference this},Path=Text}"
                        TextColor="#363B40" />

                </Grid>


            </Frame>

            <!--CHECKED-->
            <Frame
                InputTransparent="True"
                HorizontalOptions="Fill"

                x:Name="checkedState"
                BackgroundColor="White"
                BorderColor="Transparent"
                HasShadow="False"
                Padding="0"
                CornerRadius="12">

                <Frame.Shadow>
                    <Shadow Brush="#27527A"
                            Offset="2,2"
                            Radius="12"
                            Opacity="0.06" />
                </Frame.Shadow>

                <Grid ColumnSpacing="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="64" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <Image
                        Source="completedcircleyellow.png"
                        WidthRequest="24"
                        HeightRequest="24"
                        HorizontalOptions="Center"
                        VerticalOptions="Center"
                        Margin="0,13,0,13"
                        BackgroundColor="Transparent" />

                    <!--<Frame
                        Grid.Column="0"
                        WidthRequest="24"
                        HeightRequest="24"
                        CornerRadius="12"
                        HorizontalOptions="End"
                        VerticalOptions="Center"
                        Padding="0"
                        HasShadow="False"
                        BackgroundColor="Transparent"
                        Margin="0,13,0,13"
                        BorderColor="{x:StaticResource ColorPrimaryLight}" />-->

                    <Label
                        Grid.Column="1"
                        Margin="0,7,10,7"
                        HorizontalOptions="Fill"
                        VerticalOptions="Center"
                        FontSize="15"
                        Text="{Binding Source={x:Reference this},Path=Text}"
                        TextColor="#363B40" />

                </Grid>

 

            </Frame>



        </Grid>
    </ContentView.Content>
</models:BaseQuestionOptionView>