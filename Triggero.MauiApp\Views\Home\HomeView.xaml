﻿<?xml version="1.0" encoding="UTF-8"?>

<pages:ReloadableGrid
    x:Class="Triggero.MauiMobileApp.Views.HomeView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn1="clr-namespace:Triggero.MauiMobileApp.Views.Drawn"
    xmlns:pages="using:Triggero.MauiMobileApp.Views.Pages"
    x:Name="ThisPage"
    Padding="0"
    BackgroundColor="White"
    HorizontalOptions="FillAndExpand"
    RowDefinitions="*"
    VerticalOptions="FillAndExpand">


    <ScrollView
        BackgroundColor="White"
        Orientation="Vertical"
        Scrolled="ScrollView_Scrolled"
        VerticalScrollBarVisibility="Never">

        <StackLayout
            Spacing="0"
            BackgroundColor="White">

            <!--  HEADER PROFILE  -->
            <Grid
                x:Name="Header"
                HeightRequest="227">
                <Grid.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <LinearGradientBrush.GradientStops>
                            <GradientStop Offset="0.1" Color="#F9FCFF" />
                            <GradientStop Offset="1.0" Color="#CDE9FF" />
                        </LinearGradientBrush.GradientStops>
                    </LinearGradientBrush>
                </Grid.Background>

                <Grid
                    Margin="20,0,20,0"
                    VerticalOptions="Center">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="1*" />
                    </Grid.ColumnDefinitions>


                    <!--  COL 0  -->
                    <StackLayout
                        Grid.Column="0"
                        HeightRequest="66"
                        Orientation="Horizontal"
                        Spacing="12"
                        VerticalOptions="Center">

                        <Frame
                            Padding="0"
                            BackgroundColor="#DEEDF9"
                            BorderColor="{x:StaticResource ColorPrimary}"
                            CornerRadius="33"
                            HasShadow="False"
                            HeightRequest="66"
                            HorizontalOptions="Start"
                            IsClippedToBounds="True"
                            VerticalOptions="Center"
                            WidthRequest="66">

                            <Image
                                x:Name="avatar"
                                HeightRequest="66"
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                WidthRequest="66">
                                <Image.GestureRecognizers>
                                    <TapGestureRecognizer
                                        Command="{Binding Source={x:Reference ThisPage}, Path=GoToProfile}" />
                                </Image.GestureRecognizers>
                            </Image>

                        </Frame>


                        <StackLayout
                            HorizontalOptions="Start"
                            Spacing="8"
                            VerticalOptions="Center">

                            <Label
                                x:Name="hiNameLabel"
                                FontFamily="FontTextBold"
                                FontSize="16"
                                HorizontalOptions="Start"
                                TextColor="{x:StaticResource ColorText}" />

                            <Label
                                HorizontalOptions="Start"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.WhatWillDo}"
                                TextColor="{x:StaticResource ColorText}" />

                        </StackLayout>

                    </StackLayout>


                    <!--  COL 1  -->
                    <StackLayout
                        Grid.Column="1"
                        Margin="0,0,0,39"
                        HorizontalOptions="End"
                        Orientation="Horizontal"
                        Spacing="24"
                        VerticalOptions="End">

                        <ImageButton
                            BackgroundColor="Transparent"
                            Command="{Binding Source={x:Reference ThisPage}, Path=GoToSearch}"
                            HeightRequest="20"
                            HorizontalOptions="Center"
                            Source="search.png"
                            VerticalOptions="Center"
                            WidthRequest="20" />

                        <ImageButton
                            BackgroundColor="Transparent"
                            Command="{Binding Source={x:Reference ThisPage}, Path=GoToFavorites}"
                            HeightRequest="20"
                            HorizontalOptions="Center"
                            Source="likeset.png"
                            VerticalOptions="Center"
                            WidthRequest="20" />

                    </StackLayout>


                </Grid>
            </Grid>

            <!--  STACK CONTENT  -->
            <StackLayout
                Padding="0,0,0,0"
                BackgroundColor="White">

                <!--  TASKS todo not clicking -->
                <!--  DRAWN -  TASKS  -->
                <draw:Canvas
                    Margin="0,-40,0,0"
                    Gestures="Enabled"
                    RenderingMode="Default"
                    HorizontalOptions="Fill"
                    Tag="Tasks">

                    <draw:SkiaLayout
                        Padding="20,10"
                        HorizontalOptions="Fill"
                        Tag="TasksWrapper"
                        UseCache="Image">

                        <draw:SkiaShape
                            Tag="TasksShape"
                            BackgroundColor="White"
                            CornerRadius="16"
                            HorizontalOptions="Fill">

                            <draw:SkiaShape.Shadows>

                                <draw:SkiaShadow
                                    Blur="4"
                                    Opacity="0.075"
                                    X="0"
                                    Y="2"
                                    Color="#000000" />

                            </draw:SkiaShape.Shadows>

                            <draw:SkiaLayout
                                x:Name="TasksFrame"
                                Tag="TasksShapeStack"
                                Margin="0,0,0,20"
                                HorizontalOptions="Fill"
                                IsClippedToBounds="True"
                                Type="Column"
                                UseCache="Image">

                                <!--  HEADER  -->
                                <draw:SkiaLayout
                                    HeightRequest="45"
                                    HorizontalOptions="Fill"
                                    UseCache="Image">

                                    <draw:SkiaLabel
                                        Margin="16,0,0,0"
                                        FontSize="12"
                                        HorizontalOptions="Start"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.Completed}"
                                        TextColor="{x:StaticResource ColorText}"
                                        VerticalOptions="Center" />

                                    <draw:SkiaShape
                                        Margin="0,0,16,0"
                                        Padding="0"
                                        BackgroundColor="White"
                                        CornerRadius="9"
                                        HeightRequest="22"
                                        HorizontalOptions="End"
                                        StrokeColor="{x:StaticResource ColorTextSecondary}"
                                        StrokeWidth="1"
                                        VerticalOptions="Center"
                                        WidthRequest="36">

                                        <draw:SkiaLabel
                                            x:Name="completedTasksLabelDrawn"
                                            FontSize="12"
                                            HorizontalOptions="Center"
                                            Text=""
                                            TextColor="{x:StaticResource ColorTextSecondary}"
                                            VerticalOptions="Center" />

                                    </draw:SkiaShape>


                                </draw:SkiaLayout>

                                <!--  TASKS  -->

                                <draw:SkiaLayout
                                    Tag="StackTodayTasks"
                                    HorizontalOptions="Fill"
                                    x:Name="StackTodayTasks"
                                    MeasureItemsStrategy="MeasureAll"
                                    RecyclingTemplate="Disabled"
                                    Spacing="16"
                                    Type="Column">

                                </draw:SkiaLayout>

                            </draw:SkiaLayout>

                        </draw:SkiaShape>

                        <!--  visible when blurred  -->
                        <drawn1:OverlayLock
                            x:Name="TodoLock"
                            HorizontalOptions="Fill"
                            IsVisible="False"
                            VerticalOptions="Fill">

                            <draw:SkiaImage
                                Aspect="AspectFit"
                                HeightRequest="38"
                                HorizontalOptions="Center"
                                Source="subscriptionlock.png"
                                VerticalOptions="Center"
                                WidthRequest="28" />

                        </drawn1:OverlayLock>

                        <!--<draw:SkiaLabel
                            Padding="16,8,16,8"
                            BackgroundColor="White"
                            FontFamily="FontTextMedium"
                            FontSize="15"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            TextColor="#3333FF">
                            <draw:SkiaLabel.Spans>

                                <draw:TextSpan Text="{x:Static triggeroV2:Constants.ApplePaymentLink}" />

                                <draw:TextSpan Text=" " />

                                <draw:SvgSpan
                                    Width="16.5"
                                    Height="16.5"
                                    Source="Resources/Images/linkout.svg"
                                    TintColor="#3333FF"
                                    VerticalAlignement="Center" />

                            </draw:SkiaLabel.Spans>

                        </draw:SkiaLabel>-->

                    </draw:SkiaLayout>
                </draw:Canvas>

                <!--  DRAWN -  BUTTONS BANNERS  -->
                <draw:Canvas
                    Tag="Banners"
                    Gestures="Enabled"
                    RenderingMode="Default"
                    HeightRequest="240"
                    HorizontalOptions="Fill">

                    <draw:SkiaLayout
                        Padding="20,0"
                        HorizontalOptions="Fill"
                        UseCache="Image"
                        VerticalOptions="Fill">

                        <draw:SkiaLayout
                            Padding="0,10,0,5"
                            BackgroundColor="White"
                            ColumnDefinitions="50*,50*"
                            ColumnSpacing="12"
                            HorizontalOptions="Fill"
                            RowDefinitions="50*,50*"
                            RowSpacing="12"
                            Type="Grid"
                            VerticalOptions="Fill">

                            <!--  BTN1 - TEST  -->
                            <!--  visible when IsTestsActivated  -->
                            <draw:SkiaLayout
                                x:Name="btnTests"
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill">

                                <draw:SkiaShape
                                    x:Name="Shape1"
                                    Grid.Row="0"
                                    Grid.Column="0"
                                    BackgroundColor="White"
                                    CornerRadius="16"
                                    HorizontalOptions="Fill"
                                    Opacity="0.4"
                                    VerticalOptions="Fill">
                                    <draw:SkiaControl.FillGradient>

                                        <draw:SkiaGradient
                                            EndXRatio="1"
                                            EndYRatio="0"
                                            StartXRatio="0"
                                            StartYRatio="0"
                                            Type="Linear">
                                            <draw:SkiaGradient.Colors>
                                                <Color>#F9EDC7</Color>
                                                <Color>#ECA069</Color>
                                            </draw:SkiaGradient.Colors>
                                        </draw:SkiaGradient>

                                    </draw:SkiaControl.FillGradient>

                                </draw:SkiaShape>

                                <draw:SkiaImage
                                    Margin="0,-10,12,0"
                                    HeightRequest="94"
                                    HorizontalOptions="End"
                                    LoadSourceOnFirstDraw="False"
                                    Source="knowyourself.png"
                                    WidthRequest="100" />

                                <draw:SkiaLabel
                                    Margin="12,16,86,0"
                                    HorizontalOptions="Start"
                                    Style="{x:StaticResource StyleBtnTextDrawn}"
                                    Tag="TextKnow"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.KnowYourself}"
                                    VerticalOptions="Start" />

                                <draw:SkiaHotspot
                                    AnimationTapped="Ripple"
                                    CommandTapped="{Binding Source={x:Reference ThisPage}, Path=GoToTests}"
                                    TransformView="{x:Reference Shape1}" />

                            </draw:SkiaLayout>

                            <drawn1:OverlayLock
                                x:Name="btnTestLock"
                                HorizontalOptions="Fill"
                                IsVisible="False"
                                VerticalOptions="Fill">

                                <draw:SkiaImage
                                    Aspect="AspectFit"
                                    HeightRequest="38"
                                    HorizontalOptions="Center"
                                    Source="subscriptionlock.png"
                                    VerticalOptions="Center"
                                    WidthRequest="28" />

                            </drawn1:OverlayLock>

                            <!--<draw:SkiaImage
                                x:Name="btnTestLock"
                                Aspect="AspectFit"
                                HeightRequest="38"
                                HorizontalOptions="Center"
                                InputTransparent="True"
                                IsVisible="False"
                                Source="subscriptionlock.png"
                                VerticalOptions="Center"
                                WidthRequest="28" />-->

                            <!--  BTN2 - BREATH  -->
                            <!--  visible when IsLibraryActivated  -->
                            <draw:SkiaLayout
                                x:Name="btnBreath"
                                Grid.Row="0"
                                Grid.Column="1"
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill">

                                <draw:SkiaShape
                                    BackgroundColor="White"
                                    CornerRadius="16"
                                    HorizontalOptions="Fill"
                                    Opacity="0.4"
                                    VerticalOptions="Fill">
                                    <draw:SkiaControl.FillGradient>

                                        <draw:SkiaGradient
                                            EndXRatio="1"
                                            EndYRatio="0"
                                            StartXRatio="0"
                                            StartYRatio="0"
                                            Type="Linear">
                                            <draw:SkiaGradient.Colors>
                                                <Color>#BFFFFB</Color>
                                                <Color>#2448DC</Color>
                                            </draw:SkiaGradient.Colors>
                                        </draw:SkiaGradient>

                                    </draw:SkiaControl.FillGradient>

                                </draw:SkiaShape>
                                <draw:SkiaImage
                                    Margin="0,-10,20,0"
                                    Aspect="Fit"
                                    HeightRequest="99"
                                    HorizontalOptions="End"
                                    LoadSourceOnFirstDraw="False"
                                    Source="breathpracticegirl.png"
                                    WidthRequest="76" />
                                <draw:SkiaLabel
                                    Margin="12,16,0,0"
                                    HorizontalOptions="Start"
                                    Style="{x:StaticResource StyleBtnTextDrawn}"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.Breath}"
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Start" />
                                <draw:SkiaHotspot
                                    CommandTapped="{Binding Source={x:Reference ThisPage}, Path=GoToBreath}" />
                            </draw:SkiaLayout>


                            <drawn1:OverlayLock
                                x:Name="btnBreathLock"
                                Grid.Row="0"
                                Grid.Column="1"
                                HorizontalOptions="Fill"
                                IsVisible="False"
                                VerticalOptions="Fill">

                                <draw:SkiaImage
                                    Aspect="AspectFit"
                                    HeightRequest="38"
                                    HorizontalOptions="Center"
                                    Source="subscriptionlock.png"
                                    VerticalOptions="Center"
                                    WidthRequest="28" />

                            </drawn1:OverlayLock>
                            <!--<draw:SkiaImage
                                x:Name="btnBreathLock"
                                Grid.Row="0"
                                Grid.Column="1"
                                Aspect="AspectFit"
                                HeightRequest="38"
                                HorizontalOptions="Center"
                                InputTransparent="True"
                                IsVisible="False"
                                Source="subscriptionlock.png"
                                VerticalOptions="Center"
                                WidthRequest="28" />-->

                            <!--  BTN3 - PRACTICES  -->
                            <!--  visible when ! IsLibraryActivated  -->
                            <draw:SkiaLayout
                                x:Name="btnPractices"
                                Grid.Row="1"
                                Grid.ColumnSpan="2"
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill">
                                <draw:SkiaShape
                                    BackgroundColor="White"
                                    CornerRadius="16"
                                    HorizontalOptions="Fill"
                                    Opacity="0.4"
                                    VerticalOptions="Fill">
                                    <draw:SkiaControl.FillGradient>

                                        <draw:SkiaGradient
                                            EndXRatio="1"
                                            EndYRatio="0"
                                            StartXRatio="0"
                                            StartYRatio="0"
                                            Type="Linear">
                                            <draw:SkiaGradient.Colors>
                                                <Color>#B3B3E6</Color>
                                                <Color>#F9ACC0</Color>
                                            </draw:SkiaGradient.Colors>
                                        </draw:SkiaGradient>

                                    </draw:SkiaControl.FillGradient>

                                </draw:SkiaShape>
                                <draw:SkiaImage
                                    Margin="0,-5,0,-5"
                                    Aspect="AspectFit"
                                    HorizontalOptions="Center"
                                    LoadSourceOnFirstDraw="False"
                                    Source="practices.png"
                                    VerticalOptions="Fill" />

                                <draw:SkiaLabel
                                    Margin="12,16,0,0"
                                    HorizontalOptions="Start"
                                    Style="{x:StaticResource StyleBtnTextDrawn}"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.Practices}"
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Start" />
                                <draw:SkiaHotspot
                                    CommandTapped="{Binding Source={x:Reference ThisPage}, Path=GoToPractices}" />

                            </draw:SkiaLayout>

                            <!--  visible when blurred  -->
                            <drawn1:OverlayLock
                                x:Name="btnLibraryLock"
                                Grid.Row="1"
                                Grid.ColumnSpan="2"
                                HorizontalOptions="Fill"
                                IsVisible="False"
                                VerticalOptions="Fill">

                                <draw:SkiaImage
                                    Aspect="AspectFit"
                                    HeightRequest="38"
                                    HorizontalOptions="Center"
                                    Source="subscriptionlock.png"
                                    VerticalOptions="Center"
                                    WidthRequest="28" />

                            </drawn1:OverlayLock>
                            <!--<draw:SkiaImage
                                x:Name="btnLibraryLock"
                                Grid.Row="1"
                                Grid.ColumnSpan="2"
                                Aspect="AspectFit"
                                HeightRequest="38"
                                HorizontalOptions="Center"
                                InputTransparent="True"
                                IsVisible="False"
                                Source="subscriptionlock.png"
                                VerticalOptions="Center"
                                WidthRequest="28" />-->

                        </draw:SkiaLayout>
                    </draw:SkiaLayout>
                </draw:Canvas>

                <!--  TAGS todo check, its empty for dev user -->
                <Frame
                    x:Name="needToHandleFrame"
                    Margin="20,30,20,47"
                    Padding="0"
                    CornerRadius="16"
                    HasShadow="False"
                    IsVisible="False"
                    MinimumHeightRequest="165"
                    VerticalOptions="Start">
                    <Frame.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                            <LinearGradientBrush.GradientStops>
                                <GradientStop Offset="0" Color="#F2F8FF" />
                                <GradientStop Offset="1.0" Color="#E6F2FF" />
                            </LinearGradientBrush.GradientStops>
                        </LinearGradientBrush>
                    </Frame.Background>

                    <Grid>

                        <StackLayout
                            x:Name="needToHandleGrid"
                            Margin="20,10,20,0">

                            <Label
                                FontFamily="FontTextBold"
                                FontSize="16"
                                HorizontalOptions="Start"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.WhatShouldHandle}"
                                TextColor="{x:StaticResource ColorText}"
                                VerticalOptions="Start" />

                            <FlexLayout
                                x:Name="needToHandleItemsLayout"
                                Margin="0,5,0,20"
                                Direction="Row"
                                Wrap="Wrap">

                            </FlexLayout>
                        </StackLayout>


                    </Grid>


                </Frame>

                <!--  RECCOMENDATIONS DRAWN  -->
                <draw:Canvas
                    Tag="Recommendations"
                    Margin="0,0,0,80"
                    Gestures="Enabled"
                    RenderingMode="Default"
                    HeightRequest="428"
                    HorizontalOptions="Fill">

                    <draw:SkiaLayout
                        Padding="20,8"
                        HorizontalOptions="Fill"
                        UseCache="Image">

                        <draw:SkiaShape
                            BackgroundColor="White"
                            CornerRadius="15"
                            HorizontalOptions="Fill">

                            <draw:SkiaShape.Shadows>

                                <draw:SkiaShadow
                                    Blur="4"
                                    Opacity="0.075"
                                    X="0"
                                    Y="2"
                                    Color="#000000" />

                            </draw:SkiaShape.Shadows>

                            <!--  TAGS todo BUGS if FILL not set -->
                            <draw:SkiaLayout
                                x:Name="RecommendationsFrame"
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill">

                                <!--  STACK  -->
                                <draw:SkiaLayout
                                    HorizontalOptions="Fill"
                                    Padding="20"
                                    Type="Column"
                                    Spacing="15"
                                    UseCache="Image">

                                    <draw:SkiaLabel
                                        FontFamily="FontTextBold"
                                        FontSize="16"
                                        HorizontalOptions="Start"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.RecommendationsForToday}"
                                        TextColor="{x:StaticResource ColorText}"
                                        VerticalOptions="Start" />

                                    <!--todo BUG will now show in not FILL, cuz of recycling or measure FIRST cell?-->
                                    <draw:SkiaLayout
                                        HorizontalOptions="Fill"
                                        RecyclingTemplate="Disabled"
                                        x:Name="StackRecommendations"
                                        Spacing="16"
                                        Type="Column">

                                    </draw:SkiaLayout>

                                </draw:SkiaLayout>

                            </draw:SkiaLayout>

                        </draw:SkiaShape>

                        <!--  visible when blurred  -->
                        <drawn1:OverlayLock
                            x:Name="RecommendationsLock"
                            HorizontalOptions="Fill"
                            IsVisible="False"
                            VerticalOptions="Fill">

                            <draw:SkiaImage
                                Aspect="AspectFit"
                                HeightRequest="38"
                                HorizontalOptions="Center"
                                Source="subscriptionlock.png"
                                VerticalOptions="Center"
                                WidthRequest="28" />

                        </drawn1:OverlayLock>


                    </draw:SkiaLayout>

                </draw:Canvas>

            </StackLayout>

        </StackLayout>

    </ScrollView>


</pages:ReloadableGrid>