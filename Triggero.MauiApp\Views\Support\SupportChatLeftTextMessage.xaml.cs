﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models.Messengers.Support;
using Triggero.Models.Practices;



namespace Triggero.Controls.Chat
{
    
    public partial class SupportChatLeftTextMessage : ContentView
    {
        public SupportChatLeftTextMessage(SupportChatMessage msg)
        {
            InitializeComponent();
            Message = msg;
            timeLabel.Text = msg.SentAt.ToString("HH:mm");
        }
        private SupportChatMessage message;
        public SupportChatMessage Message
        {
            get { return message; }
            set { message = value; OnPropertyChanged(nameof(Message)); }
        }


    }
}