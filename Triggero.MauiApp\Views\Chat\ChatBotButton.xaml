﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.ChatBot.ChatBotButton">
    <ContentView.Content>

        <Frame
            Padding="5,0,5,0"
            CornerRadius="5"
            HasShadow="False"
            IsClippedToBounds="True"
            BackgroundColor="{x:StaticResource ColorPrimaryLight}">

            <Frame.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped"/>
            </Frame.GestureRecognizers>
            
            <Label        
                HorizontalOptions="Center"
                VerticalOptions="Center"
                HorizontalTextAlignment="Center"
                FontSize="11"
                TextTransform="None"
                x:Name="button"
                TextColor="{x:StaticResource ColorText}"/>

        </Frame>
        
    </ContentView.Content>
</ContentView>