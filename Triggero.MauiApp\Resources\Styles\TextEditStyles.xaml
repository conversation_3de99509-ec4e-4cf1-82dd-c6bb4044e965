﻿<?xml version="1.0" encoding="UTF-8" ?>
<?xaml-comp compile="true" ?>
<ResourceDictionary
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">


    <Style
        x:Key="grayTextEditor"
        TargetType="Editor">
        <Setter Property="IsTextPredictionEnabled" Value="False" />
        <Setter Property="IsSpellCheckEnabled" Value="False" />
        <Setter Property="HeightRequest" Value="40" />
        <Setter Property="TextColor" Value="#363B40" />
        <Setter Property="Placeholder" Value="" />
        <Setter Property="BackgroundColor" Value="Transparent" />
    </Style>

    <Style
        x:Key="grayTextEdit"
        TargetType="Entry">
        <Setter Property="IsTextPredictionEnabled" Value="False" />
        <Setter Property="IsSpellCheckEnabled" Value="False" />
        <Setter Property="HeightRequest" Value="40" />
        <Setter Property="TextColor" Value="#363B40" />
        <Setter Property="PlaceholderColor" Value="#989B9E" />
        <Setter Property="BackgroundColor" Value="Transparent" />
    </Style>

    <Style
        x:Key="smsCodeTextEdit"
        TargetType="Entry">
        <Setter Property="IsTextPredictionEnabled" Value="False" />
        <Setter Property="IsSpellCheckEnabled" Value="False" />
        <Setter Property="HeightRequest" Value="62" />
        <Setter Property="FontSize" Value="30" />
        <Setter Property="TextColor" Value="#151940" />
        <Setter Property="BackgroundColor" Value="#F5F6FA" />
        <Setter Property="HorizontalTextAlignment" Value="Center" />
    </Style>

    <Style
        x:Key="smsCodeEntry"
        TargetType="Entry">
        <Setter Property="IsTextPredictionEnabled" Value="False" />
        <Setter Property="IsSpellCheckEnabled" Value="False" />
        <Setter Property="HeightRequest" Value="62" />
        <Setter Property="FontSize" Value="30" />
        <Setter Property="TextColor" Value="#151940" />
        <Setter Property="BackgroundColor" Value="#F5F6FA" />
        <Setter Property="HorizontalTextAlignment" Value="Center" />
    </Style>




</ResourceDictionary>