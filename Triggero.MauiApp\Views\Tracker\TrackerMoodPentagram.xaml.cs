﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models;
using Triggero.Models.MoodTracker.User;



namespace Triggero.Controls.MoodTracker
{
	
	public partial class TrackerMoodPentagram : ContentView
	{


		public TrackerMoodPentagram()
		{
			InitializeComponent();
		}


        private ObservableCollection<MoodTrackerPentagramItem> data = new ObservableCollection<MoodTrackerPentagramItem>();
        public ObservableCollection<MoodTrackerPentagramItem> Data
        {
            get { return data; }
            set { data = value; OnPropertyChanged(nameof(Data)); }
        }

        public void BuildChart(List<MoodtrackerItem> items)
		{
			Data.Clear();

            double avgAnnoyance = 1;
            double avgHappiness = 1;
            double avgStress = 1;
            double avgConfidence = 1;
            double avgAnxienty = 1;
            double avgInspiration = 1;

            if (items.Any())
			{
			    avgAnnoyance = items.Average(o => o.Annoyance);
                avgHappiness = items.Average(o => o.Happiness);
                avgStress = items.Average(o => o.Stress);
                avgConfidence = items.Average(o => o.Confidence);
                avgAnxienty = items.Average(o => o.Anxienty);
                avgInspiration = items.Average(o => o.Inspiration);
            }

            Data.Add(new MoodTrackerPentagramItem()
            {
                Title = App.This.Interface.MoodTracker.TrackerGeneral.Happiness,
                Value = avgHappiness
            });
            Data.Add(new MoodTrackerPentagramItem()
            {
                Title = App.This.Interface.MoodTracker.TrackerGeneral.Inspiration,
                Value = avgInspiration
            });
            Data.Add(new MoodTrackerPentagramItem()
            {
                Title = App.This.Interface.MoodTracker.TrackerGeneral.Confidence,
                Value = avgConfidence
            });

            Data.Add(new MoodTrackerPentagramItem()
            {
                Title = App.This.Interface.MoodTracker.TrackerGeneral.Stress,
                Value = avgStress
            });
            Data.Add(new MoodTrackerPentagramItem()
            {
                Title = App.This.Interface.MoodTracker.TrackerGeneral.Anxienty,
                Value = avgAnxienty
            });
            Data.Add(new MoodTrackerPentagramItem()
            {
                Title = App.This.Interface.MoodTracker.TrackerGeneral.Annoyance,
                Value = avgAnnoyance
            });
          

        }
	}
}