﻿
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using Triggero.Controls.Cards.Tracker.Factors;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.MoodTracker;
using Triggero.Models.MoodTracker.User;


namespace Triggero.MauiMobileApp.Views.MoodTracker
{

    public partial class TrackerFactors : ContentPage
    {
        public TrackerFactors(MoodtrackerItem item)
        {
            Tracker = item;

            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            goNextBtn.Text = $"{App.This.Interface.MoodTracker.TrackerGeneral.GoNext} 3/5";
        }


        private MoodtrackerItem tracker = new MoodtrackerItem();
        public MoodtrackerItem Tracker
        {
            get { return tracker; }
            set { tracker = value; OnPropertyChanged(nameof(Tracker)); }
        }


        #region Init

        protected override async void OnAppearing()
        {
            int col = 0;
            int row = 0;
            gridLayout.Children.Clear();
            selectedFactors.Clear();

            var factors = await ApplicationState.Data.GetFactors();
            foreach (var factor in factors)
            {
                if (col > 2)
                {
                    col = 0;
                    row++;
                    gridLayout.RowDefinitions.Add(new RowDefinition() { Height = 91 });
                }

                var view = new FactorCard(factor)
                {
                    WidthRequest = 91,
                    HeightRequest = 91,
                    HorizontalOptions = LayoutOptions.Center,
                    VerticalOptions = LayoutOptions.Center
                };
                view.Tapped += View_Tapped;
                Grid.SetColumn(view, col);
                Grid.SetRow(view, row);
                gridLayout.Children.Add(view);

                col++;
            }



        }



        #endregion

        #region Selection
        private List<Factor> selectedFactors = new List<Factor>();

        private void View_Tapped(object sender, Factor e)
        {
            var card = sender as FactorCard;
            if (selectedFactors.Count >= 3 && !card.IsSelected) return;

            card.IsSelected = !card.IsSelected;
            if (!card.IsSelected)
            {
                selectedFactors.Remove(e);
            }
            else
            {
                selectedFactors.Add(e);
            }
        }

        #endregion

        #region Navigation commands
        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
        private ICommand goNext;
        public ICommand GoNext
        {
            get => goNext ??= new RelayCommand(async obj =>
            {
                if (!selectedFactors.Any()) return;

                Tracker.Influences.Clear();
                selectedFactors.ForEach(o =>
                {
                    Tracker.Influences.Add(new MoodTrackerInfluence
                    {
                        Factor = o,
                        FactorId = o.Id,
                    });
                });

                App.OpenPage(new TrackerFactorDetails(Tracker));
            });
        }
        #endregion
    }
}