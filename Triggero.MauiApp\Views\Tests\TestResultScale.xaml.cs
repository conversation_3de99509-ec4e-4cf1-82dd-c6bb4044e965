﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Tests;



namespace Triggero.MauiMobileApp.Controls.Tests
{

    public partial class TestResultScale : ContentView
	{
		public TestResultScale(TestScale scale,int score)
		{
			InitializeComponent();

			scaleTitleLabel.Text = scale.GetLocalizedTitle(LanguageHelper.LangCode);
            scaleValueLabel.Text = score.ToString();

            progressBar.Maximum = scale.MaximumScore;
			progressBar.Progress = score;

        }
	}
}