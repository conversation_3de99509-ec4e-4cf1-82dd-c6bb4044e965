﻿using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiPort.Models;
using Triggero.Models;
using Triggero.Models.Tests;

namespace Triggero.Controls.Cards.TasksForToday
{

    public partial class TestRecommendation : BaseRecommendationView
    {
        private Test _test;
        public TestRecommendation(RecommendationModel recommendation) : base(recommendation)
        {
            _test = recommendation.Test;
            InitializeComponent();
            Load();
        }
        private void Load()
        {
            titleLabel.Text = _test.GetLocalizedTitle(LanguageHelper.LangCode);
            minutesSpan.Text = $"{_test.PassingTimeInMinutes}";
            img.Source = Constants.BuildContentUrl(_test.IconImgPath);
        }
    }
}