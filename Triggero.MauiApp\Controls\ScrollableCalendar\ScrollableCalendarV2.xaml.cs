﻿using System.Linq;
using System.Threading.Tasks;
using Triggero.Custom.ScrollableCalendarItems;

namespace Triggero.Custom
{

    public partial class ScrollableCalendarV2 : ContentView
    {

        public ScrollableCalendarV2()
        {
            InitializeComponent();
        }

        public DateTime From { get; protected set; }
        public DateTime To { get; protected set; }




        public async Task BuildCalendar(DateTime from, DateTime to)
        {



            var date = from;
            layout.Children.Clear();

            while (date < to)
            {
                var monthCalendar = MakeMonthCalendar(date.Year, date.Month);
                monthCalendar.Margin = new Thickness(20, 0, 20, 0);
                layout.Children.Add(monthCalendar);

                var line = new BoxView
                {
                    HeightRequest = 0.7,
                    Margin = new Thickness(0, 25, 0, 32),
                    VerticalOptions = LayoutOptions.Start,
                    //Background = Color.FromHex("#CEE3F4"),
                    BackgroundColor = Color.FromHex("#CEE3F4"),
                };
                layout.Children.Add(line);


                date = date.AddMonths(1);
            }
        }

        public async Task ScrollToMonth(int year, int month)
        {
            foreach (View view in layout.Children)
            {
                if (view is CalendarMonthView monthCalendar)
                {
                    if (monthCalendar.Year == year && monthCalendar.Month == month)
                    {
                        scrollView.ScrollToAsync(monthCalendar, ScrollToPosition.Center, false);
                    }
                }
            }
        }


        private CalendarMonthView MakeMonthCalendar(int year, int month)
        {
            return new CalendarMonthView(year, month, this);
        }




        private int _selectedDatesCount;
        public void MakeRangeSelection()
        {
            foreach (View view in layout.Children)
            {
                if (view is CalendarMonthView monthCalendar)
                {
                    foreach (View child in (monthCalendar.Children[0] as StackLayout).Children)
                    {
                        if (child is CalendarWeekRow row)
                        {
                            row.SetCalendarSelection(From, To);
                        }
                    }

                }
            }
        }
        public void ClearRangeSelection()
        {
            foreach (View view in layout.Children)
            {
                if (view is CalendarMonthView monthCalendar)
                {
                    foreach (View child in (monthCalendar.Children[0] as StackLayout).Children)
                    {
                        if (child is CalendarWeekRow row)
                        {
                            row.ClearCalendarSelection();
                        }
                    }

                }
            }
        }



        public void SetDates(DateTime from, DateTime to)
        {
            _selectedDatesCount = 0;
            AfterButtonSelection(from);
            AfterButtonSelection(to);
        }
        public void AfterButtonSelection(DateTime date)
        {
            if (_selectedDatesCount == 0)
            {
                From = date;
            }
            else if (_selectedDatesCount == 1)
            {
                To = date;

                var min = new[] { From, To }.Min();
                var max = new[] { From, To }.Max();

                From = min;
                To = max;

                MakeRangeSelection();
            }
            else if (_selectedDatesCount == 2)
            {
                From = date;
                _selectedDatesCount = 0;
                ClearRangeSelection();
            }

            _selectedDatesCount++;
        }
    }
}