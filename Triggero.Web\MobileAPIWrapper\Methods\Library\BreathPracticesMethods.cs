﻿using Newtonsoft.Json;
using MobileAPIWrapper.Helpers;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;

namespace MobileAPIWrapper.Methods.Library
{
    public class BreathPracticesMethods
    {
        private string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("BreathPractices/");

        public async Task<List<BreathPractice>> GetBreathPractices()
        {
            string url = BASE_HOST + "GetBreathPractices";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<BreathPractice>>(response.Content);
            return obj;
        }
        public async Task<BreathPractice> GetBreathPractice(int id)
        {
            string url = BASE_HOST + $"GetBreathPractice?id={id}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<BreathPractice>(response.Content);
            return obj;
        }
    }
}
