﻿using DrawnUi.Views;
using System.IO;
using System.Reflection;

namespace Triggero.MauiMobileApp.Views.Pages
{
    public class BasePage : DrawnUiBasePage
    {
        public override double OnKeyboardResized(double size)
        {
            return base.OnKeyboardResized(size);


        }

        public static string ReadStringFromAssembly(string nameAfterAssembly)
        {
            var assembly = Assembly.GetExecutingAssembly();
            Stream stream = assembly.GetManifestResourceStream(assembly.GetName().Name + $".{nameAfterAssembly}");

            if (stream == null)
            {
                //todo LOG

                return "";
            }
            string json = "";
            using (var reader = new System.IO.StreamReader(stream))
            {
                json = reader.ReadToEnd();
            }
            return json;
        }

        public double BottomInset
        {
            get
            {
                return PlatformUi.Instance.Screen.BottomInset;
            }
        }

        public double BottomOffsetForTabs
        {
            get
            {
                return 100.0 + BottomInset;
            }
        }

    }

}
