﻿
using System.Collections.Generic;
using System.Windows.Input;
using Triggero.Controls.Templates;
using Triggero.Models.MoodTracker.User;


namespace Triggero.MauiMobileApp.Views.Pages.MoodTracker
{

    public partial class TrackerDay : ContentPage
    {
        private DateTime _date;
        private List<MoodtrackerItem> _items;
        public TrackerDay(DateTime date, List<MoodtrackerItem> items)
        {
            _date = date;
            _items = items;

            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            layout.Children.Clear();
            dateLabel.Text = _date.ToString("dd MMMM yyyy") + " года";

            foreach (var item in _items)
            {
                var card = new TrackerDayItemCard(item)
                {
                    HeightRequest = 81
                };
                card.Tapped += Card_Tapped;
                layout.Children.Add(card);
            }
        }

        private async void Card_Tapped(object sender, MoodtrackerItem e)
        {
            App.OpenPage(new TrackerFinalPage(e, true));
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
    }
}