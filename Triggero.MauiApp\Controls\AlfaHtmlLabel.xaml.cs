﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml;
using Microsoft.Maui.Graphics.Converters;
using Triggero.MauiMobileApp.Extensions;




namespace Triggero.MauiMobileApp.Custom
{
    
    public partial class AlfaHtmlLabel : ContentView
    {
        public AlfaHtmlLabel()
        {
            InitializeComponent();
        }

        private string text;
        public string Text
        {
            get => text;
            set
            {
                text = value;
                if (!string.IsNullOrEmpty(value))
                {
                    GenerateContent(value);
                }
              
            }
        }   

        public void GenerateContent(string html)
        {

            if (layout == null) return;

            string xmlHtml = html.Replace("&nbsp;", "&#160;").Replace("<br>", "<br/>")
                                                             .Replace("<hr>", "<hr/>");
            xmlHtml = xmlHtml.Replace("<br style=\"caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); -webkit-tap-highlight-color: rgba(26, 26, 26, 0.3); -webkit-text-size-adjust: auto;\">",
                "<br style=\"caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); -webkit-tap-highlight-color: rgba(26, 26, 26, 0.3); -webkit-text-size-adjust: auto;\"/>");

            string finalHTML = $"<body>" +
                $"{xmlHtml}" +
                $"</body>";

         

            var xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(finalHTML);

           
            layout.Children.Clear();

            //layout.Children.Add(new Label
            //{
            //    Text = finalHTML,
            //    VerticalOptions = LayoutOptions.Start
            //});
            //layout.Children.Add(new Label
            //{
            //    Text = "фыв хуй",
            //    VerticalOptions = LayoutOptions.Start
            //});


            var body = xmlDoc.ChildNodes[0];
            foreach (XmlNode node in body.ChildNodes)
            {
                var views = CreateElementsFromNode(node);
                foreach (var view in views)
                {
                    layout.Children.Add(view);
                }
            }

        }




        #region Create elems
        private List<View> CreateElementsFromNode(XmlNode node)
        {
            var list = new List<View>();


            if (node.NodeType == XmlNodeType.Element)
            {
                list.Add(CreateElem(node));
            }




            return list;
        }
        private View CreateElem(XmlNode node)
        {
            View view;

            if (node.Name.StartsWith("h") && node.Name.Length == 2 && node.Name != "hr")
            {
                view = GenerateHeadingElem(node);
            }
            else
            {
                switch (node.Name)
                {

                    case "br":
                        view = MakeBRElem(node);
                        break;
                    case "hr":
                        view = MakeHRElem(node);
                        break;
                    case "p":
                        view = MakeParagraphElem(node);
                        break;


                    case "ol":
                        view = MakeOLElem(node);
                        break;
                    case "ul":
                        view = MakeULElem(node);
                        break;


                    case "i":
                    case "em":
                    case "b":
                    case "strong":
                    case "del":
                        view = MakeFormattingElem(node, node.Name);
                        break;
                    case "span":
                        view = MakeSpanElem(node);
                       // view = MakeFormattingElem(node, node.Name);
                        break;


                    default:
                        view = MakeParagraphElem(node);
                        break;

                }
            }


            return view;
        }



        #region General
        private Label GenerateHeadingElem(XmlNode node)
        {
            var ctrl = new Label()
            {
                Text = node.InnerText,
                TextColor = Color.FromHex("#212529"),
                FontSize = 16,
                VerticalOptions = LayoutOptions.Start
                //BackgroundColor = Colors.Transparent,
                //Background = Brush.Transparent,
            };

            switch (node.Name)
            {
                case "h1":
                    ctrl.FontSize = 32;
                    break;
                case "h2":
                    ctrl.FontSize = 24;
                    break;
                case "h3":
                    ctrl.FontSize = 19;
                    break;
                case "h4":
                    ctrl.FontSize = 16;
                    break;
                case "h5":
                    ctrl.FontSize = 14;
                    break;
                case "h6":
                    ctrl.FontSize = 12;
                    break;
            }

            SetStyles(node, ctrl);
            return ctrl;
        }
        private View MakeParagraphElem(XmlNode node)
        {
            if(!node.HasInnerTags())
            {
                var ctrl = new Label()
                {
                    FontSize = 16,
                    TextColor = Color.FromHex("#212529"),
                    Text = node.InnerText,
                    VerticalOptions = LayoutOptions.Start
                    //BackgroundColor = Colors.Transparent,
                    //Background = Brush.Transparent,
                };


                SetStyles(node, ctrl);
                return ctrl;
            }
            else
            {

                if (node.HasOnlyLineTags())
                {
                    return MakeFormattingElem(node, "p");
                }
                else
                {
                    var layout = new StackLayout()
                    {
                        Spacing = 0,
                        VerticalOptions = LayoutOptions.Start
                    };

                    foreach (XmlNode child in node.ChildNodes)
                    {
                        layout.Children.Add(CreateElem(child));
                    }

                    SetStyles(node, layout);
                    return layout;

                }
       

            }

        }
        #endregion

        #region Spans
        private View MakeSpanElem(XmlNode node)
        {
            //var ctrl = new Label()
            //{
            //    FontSize = 16,
            //    Text = node.InnerText,
            //};

            //SetStyles(node, ctrl);
            //return ctrl;

            return MakeFormattingElem(node,"span");
        }


        private Span MakeSpanPartElem(Label parent,XmlNode node)
        {
            var ctrl = new Span()
            {
                FontSize = 16,
                Text = node.InnerText,
                TextColor = Color.FromHex("#212529"), //TODO: сделать поиск свойств по родителям???
                //BackgroundColor = Colors.Transparent
            };

            if(node.NodeType == XmlNodeType.Element)
            {
                SetFormattingSpanProp(node, ctrl);
            }

            if(node.Name == "#text" && node.ParentNode != null)
            {
                SetStyles(node, ctrl);
                SetStyles(node.ParentNode, ctrl);
                SetFormattingSpanProp(node, ctrl);
                SetFormattingSpanProp(node.ParentNode, ctrl);
            }
            else if (node.ParentNode != null)
            {

                SetStyles(node, ctrl);
                SetFormattingSpanProp(node, ctrl);

                if(node.ParentNode.Name == "p")
                {

                }

                if(node.ParentNode.Name == "em" && ctrl.FontAttributes == FontAttributes.Bold)
                {
                    /// Пропускаем
                }
                else
                {
                    SetStyles(node.ParentNode, parent);
                    SetFormattingSpanProp(node.ParentNode, ctrl);
                }
            }
            else
            {
                SetStyles(node, ctrl);
                SetFormattingSpanProp(node, ctrl);
            }
            
            return ctrl;
        }

        private void SetFormattingSpanProp(XmlNode node,Span ctrl)
        {
            switch (node.Name)
            {
                case "span":
                    break;
                case "del":
                    ctrl.TextDecorations = TextDecorations.Strikethrough;
                    break;
                case "b":
                case "strong":
                    ctrl.FontAttributes = FontAttributes.Bold;
                    break;
                case "em":
                case "i":
                    ctrl.FontAttributes = FontAttributes.Italic;
                    break;
            }
        }
        private List<Span> MakesSpans(Label parent, XmlNode root)
        {
            var spans = new List<Span>();

            if (!root.HasInnerTags())
            {
                spans.Add(MakeSpanPartElem(parent, root));
            }
            else
            {
                foreach (XmlNode childNode in root.ChildNodes)
                {
                    spans.AddRange(MakesSpans(parent, childNode));
                }
            }

            return spans;
        }

        #endregion

        #region Lists
        private StackLayout MakeOLElem(XmlNode node)
        {
            var listLayout = new StackLayout()
            {
                Spacing = 0,
                VerticalOptions = LayoutOptions.Start
            };

            int counter = 0;
            foreach (XmlNode childNode in node.ChildNodes)
            {
                
                if(childNode.NodeType == XmlNodeType.Element && childNode.Name == "li")
                {
                    listLayout.Children.Add(MakeOL_LIElem(childNode, ++counter));
                }
            }

            SetStyles(node, listLayout);

            //Если margin-bottom: 0 или меньше или равно 10, но обязательно положительное
            //То ставим 10: html рендерит так короче
            if(listLayout.Margin.Bottom >=0 && listLayout.Margin.Bottom <= 10)
            {
                listLayout.Margin = new Thickness(listLayout.Margin.Left, listLayout.Margin.Top, listLayout.Margin.Right, 10);
            }

            return listLayout;
        }
        private StackLayout MakeULElem(XmlNode node)
        {
            var listLayout = new StackLayout()
            {
                Spacing = 0,
                VerticalOptions = LayoutOptions.Start
            };

            foreach (XmlNode childNode in node.ChildNodes)
            {
                if (childNode.NodeType == XmlNodeType.Element && childNode.Name == "li")
                {
                    listLayout.Children.Add(MakeUL_LIElem(childNode));
                }
            }

            SetStyles(node, listLayout);

            //Если margin-bottom: 0 или меньше или равно 10, но обязательно положительное
            //То ставим 10: html рендерит так короче
            if (listLayout.Margin.Bottom >= 0 && listLayout.Margin.Bottom <= 10)
            {
                listLayout.Margin = new Thickness(listLayout.Margin.Left, listLayout.Margin.Top, listLayout.Margin.Right, 10);
            }

            return listLayout;
        }

        private View MakeOL_LIElem(XmlNode node, int number)
        {
            if (!node.HasInnerTags())
            {
                var ctrl = new Label()
                {
                    FontSize = 16,
                    TextColor = Color.FromHex("#212529"),
                    Text = $"{number}. {node.InnerText}",
                    VerticalOptions = LayoutOptions.Start
                    //BackgroundColor = Colors.Transparent,
                    //Background = Brush.Transparent,
                };




                SetStyles(node, ctrl);
                return ctrl;
            }
            else
            {
                var ctrl = MakeFormattingElem(node, "p");

                var marker = new Span()
                {
                    FontSize = 16,
                    Text = $"{number}. ",
                    TextColor = Color.FromHex("#212529")
                };
                SetStyles(node, marker);

                ctrl.FormattedText.Spans.Insert(0, marker);
                return ctrl;
            }
        }
        private View MakeUL_LIElem(XmlNode node)
        {
            if (!node.HasInnerTags())
            {
                var ctrl = new Label()
                {
                    FontSize = 16,
                    TextColor = Color.FromHex("#212529"),
                    Text = $"• {node.InnerText}",
                    VerticalOptions = LayoutOptions.Start
                    //BackgroundColor = Colors.Transparent,
                    //Background = Brush.Transparent,
                };


                SetStyles(node, ctrl);
                return ctrl;
            }
            else
            {
                var ctrl = MakeFormattingElem(node, "p");

                var marker = new Span()
                {
                    FontSize = 16,
                    Text = $"• ",
                    TextColor = Color.FromHex("#212529")
                };
                SetStyles(node, marker);

                ctrl.FormattedText.Spans.Insert(0, marker);
                return ctrl;
            }
        }
        #endregion

        #region Formatting
        private Label MakeFormattingElem(XmlNode node,string type)
        {
            var ctrl = new Label()
            {
                FormattedText = new FormattedString(),
                FontSize = 16,
                TextColor = Color.FromHex("#212529"), //TODO: чекнуть
                BackgroundColor = Colors.Transparent,
                //Background = Brush.Transparent,
                VerticalOptions = LayoutOptions.Start
            };



            switch (type)
            {
                case "em":
                case "i":
                    ctrl.FontAttributes = FontAttributes.Italic;
                    break;
                case "b":
                case "strong":
                    ctrl.FontAttributes = FontAttributes.Bold;
                    break;
                case "del":
                    ctrl.TextDecorations = TextDecorations.Strikethrough;
                    break;
                case "span":
                case "p":
                    break;
            }

            var spans = MakesSpans(ctrl, node);
            foreach(var span in spans)
            {
                ctrl.FormattedText.Spans.Add(span);
            }


            SetStyles(node, ctrl);
            return ctrl;
        }


        private BoxView MakeBRElem(XmlNode node)
        {
            var ctrl = new BoxView()
            {
                VerticalOptions = LayoutOptions.Start,
                HeightRequest = 24,
                BackgroundColor = Colors.Transparent,
                //Background = Colors.Transparent,
                Color = Colors.Transparent,
            };

            SetStyles(node, ctrl);
            return ctrl;
        }
        private BoxView MakeHRElem(XmlNode node)
        {
            var ctrl = new BoxView()
            {
                VerticalOptions = LayoutOptions.Start,
                HeightRequest = 1,
                BackgroundColor = Color.FromHex("#C7C8C9"),
                Background = Color.FromHex("#C7C8C9"),
                Color = Color.FromHex("#C7C8C9"),
                Margin = new Thickness(0,8,0,8)
            };

            SetStyles(node, ctrl);
            return ctrl;
        }



       
        #endregion


        #endregion


        #region Styles
        private Dictionary<string,string> GetStyleProps(XmlNode node)
        {
            var dict  = new Dictionary<string,string>();

            var style = node.Attributes?.GetNamedItem("style");
            if (style != null && style.Value != null) 
            {
                var styleStr = style.Value;

                var propStrs = styleStr.Split(new string[] {";"},StringSplitOptions.RemoveEmptyEntries);
                foreach (var prop in propStrs)
                {
                    if(!string.IsNullOrEmpty(prop))
                    {
                        var propSplited = prop.Split(new string[] { ":" }, StringSplitOptions.RemoveEmptyEntries);
                        if (propSplited?.Length == 2)
                        {
                            var key = propSplited[0].Trim();
                            var val = propSplited[1].Trim();

                            if (!dict.ContainsKey(key))
                            {
                                dict.Add(key, val);
                            }
                        }
                    }       
                }
            }

            return dict;
        }
        private void SetStyleProps(Dictionary<string, string> props,object obj)
        {
            var view = obj as View;
            var span = obj as Span;
            var labelView = obj as Label;

        


            foreach (var prop in props)
            {
                switch (prop.Key)
                {
                    case "font-size":

                        var val = GetPixelsFromHTMLPropValStr(prop.Value);
                        if (labelView != null)
                        {
                            labelView.FontSize = val;
                        }
                        else if (span != null)
                        {
                            span.FontSize = val;
                        }
                        break;
                    case "font-family":
                        if (labelView != null)
                        {
                            labelView.FontFamily = prop.Value;
                        }
                        else if (span != null)
                        {
                            span.FontFamily = prop.Value;
                        }
                        break;
                    case "text-indent":
                        if (labelView != null)
                        {                           
                            if (Device.RuntimePlatform == Device.iOS)
                            {
                                var indent = GetPixelsFromHTMLPropValStr(prop.Value);
                                labelView.Margin = new Thickness(view.Margin.Left + indent, view.Margin.Top, view.Margin.Right, view.Margin.Bottom);
                            } 
                        }
                        break;
                    case "text-align":

                        TextAlignment alignment = TextAlignment.Start;
                        switch (prop.Value)
                        {
                            case "left":
                            case "start":
                                alignment = TextAlignment.Start;
                                break;
                            case "right":
                            case "end":
                                alignment = TextAlignment.End;
                                break;
                            case "center":
                                alignment = TextAlignment.Center;
                                break;
                            case "justify":
                                alignment = TextAlignment.Center;
                                break;
                        }

                        if (labelView != null)
                        {
                            labelView.HorizontalTextAlignment = alignment;
                        }
                        else if (span != null)
                        {
                       
                        }
                        break;
                    case "color":

                        var color = CreateColorFromPropValStr(prop.Value);

                        if (labelView != null)
                        {
                            labelView.TextColor = color;
                        }
                        else if (span != null)
                        {
                            span.TextColor = color;
                        }
                        break;
                    case "background-color":

                        var bgColor = CreateColorFromPropValStr(prop.Value);

                        if (view != null)
                        {
                            //view.Background = bgColor;
                            view.BackgroundColor = bgColor;
                        }
                        else if (span != null)
                        {
                            span.BackgroundColor = bgColor;
                        }
                        break;
                    case "height":
                        if (view != null)
                        {
                            var height = GetPixelsFromHTMLPropValStr(prop.Value);
                            view.HeightRequest = height;
                        }
                        break;
                    case "width":
                        if (view != null)
                        {
                            var width = GetPixelsFromHTMLPropValStr(prop.Value);
                            view.WidthRequest = width;
                        }
                        break;
                    case "font-weight":
                        if (labelView != null)
                        {
                            switch (prop.Value)
                            {
                                case "700":
                                    labelView.FontAttributes = FontAttributes.Bold;
                                    break;
                            }
                        }
                        else if (span != null)
                        {
                            switch (prop.Value)
                            {
                                case "700":
                                    span.FontAttributes = FontAttributes.Bold;
                                    break;
                            }
                        }
                        break;
                    case "line-height":
                        double lineHeight = 1;
                        DoubleExtensions.TryParseSafely(prop.Value.Replace(".",","), out lineHeight);

                        if (labelView != null)
                        {
                            labelView.LineHeight = lineHeight;
                        }
                        else if (span != null)
                        {
                          //  span.LineHeight = lineHeight;
                        }
                        break;
                    case "margin-top":
                        if (view != null)
                        {
                            var marginTopVal = GetPixelsFromHTMLPropValStr(prop.Value);
                            view.Margin = new Thickness(view.Margin.Left, marginTopVal, view.Margin.Right, view.Margin.Bottom);
                        }
                        break;
                    case "margin-bottom":
                        if (view != null)
                        {
                            var marginBottomVal = GetPixelsFromHTMLPropValStr(prop.Value);
                            view.Margin = new Thickness(view.Margin.Left, view.Margin.Top, view.Margin.Right, marginBottomVal);
                        }
                        break;
                    case "margin-left":
                        if (view != null)
                        {
                            var marginLeftVal = GetPixelsFromHTMLPropValStr(prop.Value);
                            view.Margin = new Thickness(marginLeftVal, view.Margin.Top, view.Margin.Right, view.Margin.Bottom);
                        }
                        break;
                    case "margin-right":
                        if (view != null)
                        {
                            var marginRightVal = GetPixelsFromHTMLPropValStr(prop.Value);
                            view.Margin = new Thickness(view.Margin.Left, view.Margin.Top, marginRightVal, view.Margin.Bottom);
                        }
                        break;
                    case "padding":
                        if (view is Layout layout)
                        {
                            layout.Padding = CreateThicknessFromPropValStr(prop.Value);
                        }
                        else if (labelView != null)
                        {
                            labelView.Padding = CreateThicknessFromPropValStr(prop.Value);
                        }
                        break;
                    case "margin":
                        if (view is Layout layout2)
                        {
                            layout2.Margin = CreateThicknessFromPropValStr(prop.Value);
                        }
                        else if (labelView != null)
                        {
                            labelView.Margin = CreateThicknessFromPropValStr(prop.Value);
                        }
                        break;
                    case "padding-inline-start":          
                        if (view is StackLayout listLayout)
                        {
                            var paddingInlineStart = GetPixelsFromHTMLPropValStr(prop.Value);
                            listLayout.Padding = new Thickness(paddingInlineStart, listLayout.Padding.Top, listLayout.Padding.Right, listLayout.Padding.Bottom);
                        }
                        break;
                }
            }
        }



        private void SetStyles(XmlNode node, object obj)
        {
            var props = GetStyleProps(node);
            SetStyleProps(props, obj);
        }
        #endregion

        #region Converters

        

        private Color CreateColorFromPropValStr(string str)
        {
            Color color = new Color(0,0,0);

            if (str.Contains("rgb"))
            {
                //rgb(0, 0, 0)
                var digitValuesStr = str.Replace("(", "")
                                            .Replace(")", "")
                                            .Replace("rgb", "")
                                            .Trim();
                var digitValues = digitValuesStr.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                if(digitValues.Length == 3)
                {
                    var r = Convert.ToInt32(digitValues[0].Trim());
                    var g = Convert.ToInt32(digitValues[1].Trim());
                    var b = Convert.ToInt32(digitValues[2].Trim());
                    color = Color.FromRgb(r, g, b);
                }       
            }
            else if (str.Contains("rgba"))
            {
                //rgb(0, 0, 0)
                var digitValuesStr = str.Replace("(", "")
                                            .Replace(")", "")
                                            .Replace("rgba", "")
                                            .Trim();
                var digitValues = digitValuesStr.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);

                if (digitValues.Length == 4)
                {
                    var r = Convert.ToInt32(digitValues[0].Trim());
                    var g = Convert.ToInt32(digitValues[1].Trim());
                    var b = Convert.ToInt32(digitValues[2].Trim());
                    var a = Convert.ToInt32(digitValues[3].Trim());
                    color = Color.FromRgba(r, g, b, a);
                }  
            }
            else if (str.Contains("#"))
            {
                color = Color.Parse(str);
            }
            else 
            {
                color = (Color)new ColorTypeConverter().ConvertFromInvariantString(str);
            }

            return color;
        }
        private Thickness CreateThicknessFromPropValStr(string str)
        {
            var thickness = new Thickness();

            var values = str.Split(new string[] { " ", "," }, StringSplitOptions.RemoveEmptyEntries);
            if (values.Length == 4)
            {
                var paddingLeft = GetPixelsFromHTMLPropValStr(values[3].Trim());
                var paddingTop = GetPixelsFromHTMLPropValStr(values[0].Trim());
                var paddingRight = GetPixelsFromHTMLPropValStr(values[1].Trim());
                var paddingBottom = GetPixelsFromHTMLPropValStr(values[2].Trim());

                thickness = new Thickness(paddingLeft, paddingTop, paddingRight, paddingBottom);
            }
            else if (values.Length == 3)
            {
                var paddingTop = GetPixelsFromHTMLPropValStr(values[0].Trim());
                var paddingLeftRight = GetPixelsFromHTMLPropValStr(values[1].Trim());
                var paddingBottom = GetPixelsFromHTMLPropValStr(values[2].Trim());

                thickness = new Thickness(paddingLeftRight, paddingTop, paddingLeftRight, paddingBottom);
            }
            else if (values.Length == 2)
            {
                var paddingLeft = GetPixelsFromHTMLPropValStr(values[0]);
                var paddingTop = GetPixelsFromHTMLPropValStr(values[1]);

                thickness = new Thickness(paddingLeft, paddingTop);
            }
            else if (values.Length == 1)
            {
                var uniform = GetPixelsFromHTMLPropValStr(values[0]);

                thickness = new Thickness(uniform);
            }

            return thickness;
        }


        private double GetPixelsFromHTMLPropValStr(string str)
        {
            var size = GetHTMLSizeFromStr(str);
            return ConvertToPx(size);
        }
        private double ConvertToPx(HTMLSize size)
        {
            switch (size.Type)
            {
                case "pt":
                    return size.Value * 1.25;
                case "em":
                    return size.Value * 16;
                default:
                    return size.Value;
            }
        }
        private HTMLSize GetHTMLSizeFromStr(string str)
        {
            string val = "";
            string type = "";


            int index = 0;

            foreach(var ch in str)
            {
                //если не цифра и не знак минуса
                if (!char.IsDigit(ch))
                {
                    if(index == 0 && ch != '-' && ch != '-')
                    {
                        val = str.Substring(0, index);
                        type = str.Substring(index);
                        break;
                    }
                    else if(index > 0)
                    {
                        val = str.Substring(0, index);
                        type = str.Substring(index);
                        break;
                    }    
                }
                index++;
            }

            if(val == "")
            {
                val = str;
                type = "px";
            }

            double value = 0;
            DoubleExtensions.TryParseSafely(val.Replace(".", ","), out value);


            return new HTMLSize
            {
                Type = type,
                Value = value
            };
        }
        #endregion
    }

    public class HTMLSize
    {

        public double Value { get; set; }
        public string Type { get; set; }
    }

    public static class XMLExtensions
    {
        private readonly static bool fallos = false;

        public static bool HasInnerTags(this XmlNode node)
        {
            foreach(XmlNode child in node.ChildNodes)
            {
                if (child.Name != "#text")
                    return true;
            }

            return fallos;
        }
        public static bool HasOnlyLineTags(this XmlNode node)
        {
            foreach (XmlNode child in node.ChildNodes)
            {
                if (child.Name != "#text" && child.Name != "span")
                    return fallos;
            }

            return true;
        }
    }
}