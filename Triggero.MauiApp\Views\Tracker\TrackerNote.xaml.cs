﻿
using System.Windows.Input;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.MoodTracker.User;


namespace Triggero.MauiMobileApp.Views.Pages.MoodTracker
{

    public partial class TrackerNote : ContentPage
    {
        private MoodtrackerNote _note;

        public TrackerNote()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }
        public TrackerNote(MoodtrackerNote note)
        {
            _note = note;
            Text = note.Text;

            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        private string text = "";
        public string Text
        {
            get { return text; }
            set { text = value; OnPropertyChanged(nameof(Text)); }
        }


        private ICommand saveNote;
        public ICommand SaveNote
        {
            get => saveNote ??= new RelayCommand(async obj =>
            {
                if (!string.IsNullOrEmpty(Text))
                {
                    if (_note == null)
                    {
                        await ApplicationState.Data.AddMoodTrackerNote(Text);
                    }
                    else
                    {
                        await ApplicationState.Data.UpdateMoodTrackerNote(_note.Id, Text);
                    }
                    await App.Current.MainPage.Navigation.PopAsync();
                }
            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
    }
}