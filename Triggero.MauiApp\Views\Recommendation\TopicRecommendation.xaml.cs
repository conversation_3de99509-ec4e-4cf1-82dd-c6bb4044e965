﻿using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiPort.Models;
using Triggero.Models;

namespace Triggero.Controls.Cards.TasksForToday
{

    public partial class TopicRecommendation : BaseRecommendationView
    {
        private Topic _topic;
        public TopicRecommendation(RecommendationModel recommendation) : base(recommendation)
        {
            _topic = recommendation.Topic;
            InitializeComponent();
            Load();
        }


        private void Load()
        {
            titleLabel.Text = _topic.GetLocalizedTitle(LanguageHelper.LangCode);
            minutesSpan.Text = $"{_topic.PassingTimeInMinutes}";
            img.Source = Constants.BuildContentUrl(_topic.IconImgPath);
        }
    }
}