﻿<?xml version="1.0" encoding="UTF-8" ?>
<draw:SkiaCheckbox
    x:Class="Triggero.MauiMobileApp.Controls.Cards.TasksForToday.DrawnCheckBox"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    HorizontalOptions="Fill"
    UseCache="Image"
    ColorFrameOff ="{x:StaticResource ColorPrimaryLight}"
    VerticalOptions="Fill">

    <draw:SkiaShape
        Padding="0"
        Type="Circle"
        HeightRequest="24"
        HorizontalOptions="Center"
        StrokeWidth="1"
        Tag="FrameOff"
        VerticalOptions="Center"
        WidthRequest="24" />

    <draw:SkiaImage
        Aspect="AspectCover"
        BackgroundColor="Transparent"
        HeightRequest="24"
        HorizontalOptions="Center"
        Source="completedcircleyellow.png"
        Tag="FrameOn"
        VerticalOptions="Center"
        WidthRequest="24" />


</draw:SkiaCheckbox>