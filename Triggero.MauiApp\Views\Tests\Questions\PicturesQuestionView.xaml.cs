﻿using Triggero.Controls.Cards.Tests.Questions.Answers.Single;
using Triggero.MauiPort.Models;
using Triggero.Models.Abstractions;

namespace Triggero.Controls.Cards.Tests.Questions
{

    public partial class PicturesQuestionView : BaseQuestionView
    {
        public PicturesQuestionView(Question question) : base(question)
        {
            InitializeComponent();
            titleLabel.Text = Question.GetLocalizedText(LanguageHelper.LangCode);

            if (question.MultipleAnswers)
            {
                questiontypeLabel.Text = App.This.Interface.Tests.MaySelectMultiple;
            }
            else
            {
                questiontypeLabel.Text = App.This.Interface.Tests.MaySelectSingle;
            }

            Render();
        }

        private void Render()
        {
            optionsLayout.Children.Clear();
            if (Question.MultipleAnswers)
            {
                Question.Options.ForEach(o =>
                {
                    var control = new MultiplePictureAnswerOption(o)
                    {
                        HeightRequest = 90,
                        WidthRequest = 90,
                        Margin = 20
                    };
                    control.OnCheckedChanged += OnOptionCheckedChanged;
                    optionsLayout.Children.Add(control);
                });
            }
            else
            {
                Question.Options.ForEach(o =>
                {
                    var control = new PictureAnswerOption(o)
                    {
                        HeightRequest = 90,
                        WidthRequest = 90,
                        Margin = 20
                    };
                    optionsLayout.Children.Add(control);
                });
            }
        }
        int selectedCount = 0;
        private void OnOptionCheckedChanged(object sender, EventArgs e)
        {
            var option = sender as BaseQuestionOptionView;

            if (option.IsChecked)
            {
                if (selectedCount < 3)
                {
                    selectedCount++;
                }
                else
                {
                    option.IsChecked = false;
                }
            }
            else
            {
                selectedCount--;
            }
        }

        public override List<QuestionOption> GetSelectedOptions()
        {
            var options = new List<QuestionOption>();

            var selectedCards = optionsLayout.Children.Cast<BaseQuestionOptionView>().Where(o => o.IsChecked).ToList();
            selectedCards.ForEach(o => options.Add(o.Option));

            return options;
        }
    }
}