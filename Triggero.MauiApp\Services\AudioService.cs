﻿using Plugin.Maui.Audio;

namespace Triggero.MauiMobileApp.Services;

public interface IAudioService : IDisposable
{
    // TimeSpan properties
    TimeSpan Position { get; set; }
    TimeSpan Duration { get; }

    // Events
    event EventHandler<AudioProgressEventArgs> PositionChanged;

    // Methods
    Task PlayAsync(string source);
    void Pause();
    void Resume();
    void Stop();
    void SeekTo(TimeSpan position);
    void SeekForward(TimeSpan amount);
    void SeekBackward(TimeSpan amount);
    void SeekToPercentage(double percentage);

    bool IsPlaying { get; }
    double Volume { get; set; }
}

public class AudioProgressEventArgs : EventArgs
{
    public TimeSpan CurrentPosition { get; set; }
    public TimeSpan Duration { get; set; }
    public double ProgressPercentage { get; set; }
    public string CurrentPositionText { get; set; }
    public string DurationText { get; set; }
    public string RemainingTimeText { get; set; }
}

public class AudioService : IAudioService
{
    private IAudioPlayer _audioPlayer;
    private System.Timers.Timer _progressTimer;
    private readonly HttpClient _httpClient;
    private Stream _currentAudioStream;

    public event EventHandler<AudioProgressEventArgs> PositionChanged;

    // TimeSpan properties
    public TimeSpan Position
    {
        get => _audioPlayer != null ? TimeSpan.FromSeconds(_audioPlayer.CurrentPosition) : TimeSpan.Zero;
        set => SeekTo(value);
    }

    public TimeSpan Duration => _audioPlayer != null ? TimeSpan.FromSeconds(_audioPlayer.Duration) : TimeSpan.Zero;

    public bool IsPlaying => _audioPlayer?.IsPlaying ?? false;

    public double Volume
    {
        get => _audioPlayer?.Volume ?? 0;
        set { if (_audioPlayer != null) _audioPlayer.Volume = value; }
    }

    public AudioService()
    {
        _httpClient = new HttpClient();
        InitializeProgressTimer();
    }

    private void InitializeProgressTimer()
    {
        _progressTimer = new System.Timers.Timer(500);
        _progressTimer.Elapsed += OnProgressTimerElapsed;
    }

    private void OnProgressTimerElapsed(object sender, System.Timers.ElapsedEventArgs e)
    {
        if (_audioPlayer != null && IsPlaying)
        {
            var currentPos = Position;
            var duration = Duration;
            var remaining = duration - currentPos;

            var progressArgs = new AudioProgressEventArgs
            {
                CurrentPosition = currentPos,
                Duration = duration,
                ProgressPercentage = duration.TotalSeconds > 0 ? (currentPos.TotalSeconds / duration.TotalSeconds) * 100 : 0,
                CurrentPositionText = FormatTime(currentPos),
                DurationText = FormatTime(duration),
                RemainingTimeText = FormatTime(remaining)
            };

            PositionChanged?.Invoke(this, progressArgs);
        }
    }

    /// <summary>
    /// Plays audio from local file path or internet URL
    /// </summary>
    /// <param name="source">Local file path or HTTP/HTTPS URL to audio file</param>
    public async Task PlayAsync(string source)
    {
        //DisposeCurrentPlayer();
        try
        {
            var audioManager = AudioManager.Current;

            if (_audioPlayer == null)
            {
                if (IsInternetUrl(source))
                {
                    var audioStream = await GetAudioStreamFromUrl(source);
                    _currentAudioStream = audioStream;
                    _audioPlayer = audioManager.CreatePlayer(audioStream);
                }
                else
                {
                    _audioPlayer = audioManager.CreatePlayer(source);
                }

                _audioPlayer.PlaybackEnded += OnPlaybackEnded;
            }
            else
            {
                _audioPlayer.Stop();
                await Task.Delay(10);
            }

            _audioPlayer.Play();

            _progressTimer.Start();
        }
        catch (Exception e)
        {
            Super.Log(e);

            App.ShowToast("Ошибка воспроизведения");
        }

    }

    public void Pause()
    {
        _audioPlayer?.Pause();
        _progressTimer.Stop();
    }

    public void Resume()
    {
        _audioPlayer?.Play();
        _progressTimer.Start();
    }

    public void Stop()
    {
        _audioPlayer?.Stop();
        _progressTimer.Stop();
    }

    /// <summary>
    /// Seeks to specific position in the audio
    /// </summary>
    /// <param name="position">Target position</param>
    public void SeekTo(TimeSpan position)
    {
        if (_audioPlayer != null)
        {
            var maxDuration = Duration;
            if (position > maxDuration)
                position = maxDuration;
            if (position < TimeSpan.Zero)
                position = TimeSpan.Zero;

            _audioPlayer.Seek(position.TotalSeconds);

            OnProgressTimerElapsed(null, null);
        }
    }

    /// <summary>
    /// Seeks forward by specified amount
    /// </summary>
    /// <param name="amount">Amount to seek forward</param>
    public void SeekForward(TimeSpan amount)
    {
        var newPosition = Position + amount;
        SeekTo(newPosition);
    }

    /// <summary>
    /// Seeks backward by specified amount
    /// </summary>
    /// <param name="amount">Amount to seek backward</param>
    public void SeekBackward(TimeSpan amount)
    {
        var newPosition = Position - amount;
        SeekTo(newPosition);
    }

    /// <summary>
    /// Seeks to percentage of total duration
    /// </summary>
    /// <param name="percentage">Percentage (0-100)</param>
    public void SeekToPercentage(double percentage)
    {
        if (percentage < 0) percentage = 0;
        if (percentage > 100) percentage = 100;

        var duration = Duration;
        var seekPosition = TimeSpan.FromSeconds(duration.TotalSeconds * (percentage / 100.0));
        SeekTo(seekPosition);
    }

    /// <summary>
    /// Determines if the source is an internet URL
    /// </summary>
    /// <param name="source">Source string to check</param>
    /// <returns>True if source is HTTP/HTTPS URL</returns>
    private static bool IsInternetUrl(string source)
    {
        return Uri.TryCreate(source, UriKind.Absolute, out var uri) &&
               (uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps);
    }

    /// <summary>
    /// Downloads audio stream from internet URL
    /// </summary>
    /// <param name="url">Audio file URL</param>
    /// <returns>Stream containing audio data</returns>
    private async Task<Stream> GetAudioStreamFromUrl(string url)
    {
        var response = await _httpClient.GetAsync(url, HttpCompletionOption.ResponseHeadersRead);
        response.EnsureSuccessStatusCode();

        return await response.Content.ReadAsStreamAsync();
    }

    private string FormatTime(TimeSpan time)
    {
        if (time.TotalHours >= 1)
            return time.ToString(@"h\:mm\:ss");
        else
            return time.ToString(@"m\:ss");
    }

    private void OnPlaybackEnded(object sender, EventArgs e)
    {
        _progressTimer.Stop();
        OnProgressTimerElapsed(null, null);
    }

    private void DisposeCurrentPlayer()
    {
        if (_audioPlayer != null)
        {
            _audioPlayer.PlaybackEnded -= OnPlaybackEnded;
            _audioPlayer.Stop();
            _audioPlayer.Dispose();
            _audioPlayer = null;
        }

        _currentAudioStream?.Dispose();
        _currentAudioStream = null;
    }

    public void Dispose()
    {
        _progressTimer?.Stop();
        _progressTimer?.Dispose();
        DisposeCurrentPlayer();
        _httpClient?.Dispose();
    }
}