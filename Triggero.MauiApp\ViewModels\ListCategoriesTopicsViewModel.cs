﻿using System.Threading.Tasks;
using Triggero.Controls.Templates;
using Triggero.Models.Practices.Categories;
using Triggero.MauiMobileApp.Views;

using Triggero.MauiMobileApp.Extensions.Helpers;

namespace Triggero.MauiMobileApp.ViewModels;

public class ListCategoriesTopicsViewModel : BaseCategoriesViewModel
{
    public override Color ThemeColor
    {
        get
        {
            return Color.FromHex("#F4FFFB");
        }
    }

    public override async Task InitializeAsyc()
    {
        var items = await ApplicationState.Data.GetTopicCategories();

        items.Insert(0, new TopicCategory
        {
            Title = App.This.Interface.Library.Library.AllTopics,
            Description = App.This.Interface.Library.Library.AllTopicsDescription,
            ImgPath = "/built_in/images/allTopics.png"
        });

        MainThread.BeginInvokeOnMainThread(() =>
        {
            ItemTemplate = new DataTemplate(() =>
            {
                return new CellCategoryDrawn();//TopicCategoryCard();
            });

            Items.AddRange(items);

            //preload all images..
            //var cancel = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            //CancelPreload = cancel;
            //SkiaImageManager.Instance.PreloadImages(items.Select(s => s.ImgPath.AddBaseUrl(Triggero.MauiMobileApp.Constants.UrlContent)), cancel);
        });

        IsInitialized = true;
    }

    protected override void OnViewTapped(SkiaControl control)
    {
        App.OpenView(new ListElementsView(control.BindingContext as AbstractCategory));
    }

}