﻿using System.Windows.Input;
using Triggero.Controls.Cards.Tracker.Influence;
using Triggero.MauiMobileApp.Views.Pages.Auth;
using Triggero.Models.MoodTracker.User;

namespace Triggero.MauiMobileApp.Views.Pages.MoodTracker
{
    public partial class TrackerFinalPage : ContentPage
    {
#if PREVIEWS

        [HotPreview.Preview]
        public static void PreviewTopic()
        {
            var model = new MoodtrackerItem()
            {
            };

            App.OpenPage(new TrackerFinalPage(model));
        }
#endif

        public TrackerFinalPage(MoodtrackerItem item, bool showOnly = false)
        {
            try
            {
                Tracker = item;

                InitializeComponent();
                NavigationPage.SetHasNavigationBar(this, false);

                if (showOnly)
                {
                    IconEdit.IsVisible = false;
                    LabelNote.Text = "Заметка";
                    this.Editor.IsReadOnly = true;
                    //this.Editor.PlaceholderText = "Нет";
                    saveBtn.IsEnabled = false;
                }
            }
            catch (Exception e)
            {
                Super.DisplayException(this, e);
            }
        }


        private MoodtrackerItem tracker;

        public MoodtrackerItem Tracker
        {
            get { return tracker; }
            set
            {
                tracker = value;
                OnPropertyChanged(nameof(Tracker));
            }
        }

        #region Init

        protected override void OnAppearing()
        {
            switch (Tracker.Mood)
            {
                case 5:
                    moodImg.Source = ImageSource.FromFile("daymood1.png");
                    moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood5;
                    break;
                case 4:
                    moodImg.Source = ImageSource.FromFile("daymood2.png");
                    moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood4;
                    break;
                case 3:
                    moodImg.Source = ImageSource.FromFile("daymood3.png");
                    moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood3;
                    break;
                case 2:
                    moodImg.Source = ImageSource.FromFile("daymood4.png");
                    moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood2;
                    break;
                case 1:
                    moodImg.Source = ImageSource.FromFile("daymood5.png");
                    moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood1;
                    break;
                case 0:
                    moodImg.Source = ImageSource.FromFile("daymood6.png");
                    moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood0;
                    break;
            }

            dateLabel.Text = DateTime.Now.ToString("dd MMMM yyyy") + " " +
                             App.This.Interface.MoodTracker.TrackerFinal.OfYear;

            whatAffectedLayout.Children.Clear();
            foreach (var item in Tracker.Influences)
            {
                var card = new InfluenceCard(item)
                {
                    HeightRequest = 132
                };
                whatAffectedLayout.Children.Add(card);
            }

            feelingsView.Refresh();
        }

        #endregion

        #region Navigation commands

        private ICommand goBack;

        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj => { await App.Current.MainPage.Navigation.PopAsync(); });
        }

        private ICommand saveEntry;

        public ICommand SaveEntry
        {
            get => saveEntry ??= new RelayCommand(async obj =>
            {
                Tracker.Date = DateTime.Now;
                //Чтобы не создавалась пустая заметка, если пользователь не написал текст
                if (string.IsNullOrEmpty(Tracker.Note.Text))
                {
                    Tracker.Note = null;
                }

                await ApplicationState.Data.AddMoodTrackerItem(Tracker);
                App.OpenPage(new TrackerSavedPage());
            });
        }

        #endregion
    }
}