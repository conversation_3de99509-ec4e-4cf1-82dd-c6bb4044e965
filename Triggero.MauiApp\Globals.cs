﻿ 
global using System;
global using Triggero.Domain.Enums;
 
global using Triggero.Models;
global using Triggero.Models.Enums;
using DrawnUi.Draw;


namespace Triggero.MauiMobileApp;

public static class Globals
{

    public static double BottomInsets => Super.Screen.BottomInset;// PlatformUi.Instance.Screen.BottomInset;

    public static double BottomOffsetForTabs => 100.0 + BottomInsets;

    public static double TopInsets => Super.StatusBarHeight;

    public static OSType OS
    {
        get
        {
            if (Device.RuntimePlatform == Device.iOS)
                return OSType.iOS;

            return OSType.Android;
        }
    }

    public static bool ShowDebugInfo
    {
        get
        {
#if DEBUG
            return true;
#endif
            return false;
        }
    }

    public static bool ShowFPS
    {
        get
        {
#if DEBUG
            return true;
#endif
            return false;
        }
    }

    public static string Name => AppInfo.Name;
    public static string Build => AppInfo.BuildString;
    public static string BundleId => AppInfo.PackageName;
    public static string Version => $"Сборка {AppInfo.BuildString}";

    public static Color ColorPrimaryIcon => App.Current.Resources.Get<Color>("ColorPrimaryIcon");
    public static Color ColorPrimary => App.Current.Resources.Get<Color>("ColorPrimary");
    public static Color ColorPrimaryLight => App.Current.Resources.Get<Color>("ColorPrimaryLight");
}