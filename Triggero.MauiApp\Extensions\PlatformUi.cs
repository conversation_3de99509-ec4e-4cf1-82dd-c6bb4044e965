﻿using AppoMobi.Mobile;
using DrawnUi.Models;
using Triggero.MauiMobileApp.Abstractions;

namespace Triggero.MauiMobileApp;

public partial class PlatformUi : IPlatformUi
{

    private static PlatformUi? _instance;
    public static PlatformUi? Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = new();
            }
            return _instance;
        }
    }

    public Screen Screen { get; } = new();

    //public Screen Screen => Super.Screen;

 

    public void ShowAlert(string text)
    {
        Toast.ShortMessage(text);
 
    }

}