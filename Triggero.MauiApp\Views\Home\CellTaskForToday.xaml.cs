﻿using AppoMobi.Maui.Gestures;
using DrawnUi.Draw;
using Triggero.MauiMobileApp.ViewModels;
using Triggero.Models;

namespace Triggero.Controls.Cards.TasksForToday
{
    public partial class CellTaskForToday : SkiaLayout, ISkiaGestureListener
    {
        public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args, GestureEventProcessingInfo apply)
        {

            if (args.Type == TouchActionResult.Tapped)
            {
                Checked.IsToggled = !Checked.IsToggled;
                return this;
            }

            return base.ProcessGestures(args, apply);
        }

        public CellTaskForToday(TaskForToday task)
        {
            ViewModel = new TaskForTodayViewModel(task);

            InitializeComponent();

            ViewModel.Initialize();
            BindingContext = ViewModel;
        }

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            if (BindingContext is TaskForTodayViewModel)
            {
                Checked.DefaultValue = ViewModel.Model.IsCompleted;
            }
        }

        public TaskForTodayViewModel ViewModel { get; }

        private void SkiaControl_OnTapped(object? sender, ControlTappedEventArgs e)
        {
            var stop = BindingContext;
        }
    }
}