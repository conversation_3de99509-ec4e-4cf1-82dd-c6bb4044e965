﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Tests.TestResultPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="this"
    BackgroundColor="White">

    <ContentPage.Content>
        <Grid RowSpacing="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition Height="90" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid
                Grid.Row="1"
                Padding="{x:OnPlatform Android='0',
                                     iOS='0,30,0,0'}">

                <Grid>

                    <ImageButton
                        Margin="20,0,0,0"
                        BackgroundColor="Transparent"
                        Command="{Binding Source={x:Reference this}, Path=Close}"
                        CornerRadius="0"
                        HeightRequest="20"
                        HorizontalOptions="Start"
                        Source="close.png"
                        VerticalOptions="Center"
                        WidthRequest="20" />

                    <Label
                        Margin="0,0,0,0"
                        FontAttributes="Bold"
                        FontSize="17"

                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Tests.Tests}"
                        TextColor="{x:StaticResource ColorText}"
                        VerticalOptions="Center" />


                    <ImageButton
                        Margin="0,0,25,0"
                        BackgroundColor="Transparent"
                        CornerRadius="0"
                        HeightRequest="18"
                        HorizontalOptions="End"
                        Source="downloadicon.png"
                        VerticalOptions="Center"
                        WidthRequest="14" />

                    <BoxView
                        BackgroundColor="{x:StaticResource ColorText}"
                        HeightRequest="0.5"
                        HorizontalOptions="Fill"
                        VerticalOptions="End" />

                </Grid>

            </Grid>

            <Grid Grid.Row="2">
                <ScrollView
                    Margin="20,0,20,0"
                    VerticalScrollBarVisibility="Never">
                    <StackLayout>


                        <Frame
                            x:Name="resultImgFrame"
                            Margin="0,40,0,0"
                            Padding="0"
                            CornerRadius="16"
                            HasShadow="False"
                            HeightRequest="350"
                            IsClippedToBounds="True"
                            VerticalOptions="Start">
                            <Frame.Background>
                                <LinearGradientBrush>
                                    <LinearGradientBrush.GradientStops>
                                        <GradientStop Offset="0.1" Color="#F9FCFF" />
                                        <GradientStop Offset="1.0" Color="#CDE9FF" />
                                    </LinearGradientBrush.GradientStops>
                                </LinearGradientBrush>
                            </Frame.Background>

                            <StackLayout Spacing="0">

                                <Image
                                    x:Name="img"
                                    Margin="0,20,0,0"
                                    Aspect="AspectFill"
                                    HeightRequest="206"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Start"
                                    WidthRequest="240" />

                                <StackLayout
                                    x:Name="simpleResultZone"
                                    Margin="0,20,0,0"
                                    HorizontalOptions="Center"
                                    Orientation="Horizontal"
                                    Spacing="14">
                                    <Image
                                        HeightRequest="24"
                                        HorizontalOptions="Start"
                                        Source="completedcircleyellow.png"
                                        VerticalOptions="Center"
                                        WidthRequest="24" />

                                    <Label
                                        x:Name="resultLabel"
                                        FontAttributes="Bold"
                                        FontSize="14"

                                        Opacity="0.5"
                                        Text=""
                                        TextColor="Black"
                                        VerticalOptions="Center" />
                                </StackLayout>



                                <Label
                                    x:Name="titleLabel"
                                    Margin="10,12,10,0"
                                    FontAttributes="Bold"
                                    FontSize="18"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text=""
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Center" />

                            </StackLayout>

                        </Frame>

                        <StackLayout x:Name="scalesLayout">

                        </StackLayout>

                        <StackLayout
                            x:Name="scalesResultsLayout"
                            Margin="0,5,0,0"
                            Spacing="20">

                        </StackLayout>


                        <Label
                            x:Name="textLabel"
                            Margin="0,30,0,0"
                            FontSize="15"
                            Text=""
                            TextColor="{x:StaticResource ColorText}" />

                        <!--REFERENCE LINK-->
                        <draw:Canvas
                            Gestures="Lock"
                            x:Name="LabelExternalLink"
                            HorizontalOptions="Start"
                            IsVisible="False">
                            <draw:SkiaLayout HorizontalOptions="Fill">

                                <draw:SkiaLabel
                                    Padding="24,8,24,8"
                                    draw:AddGestures.CommandTapped="{Binding Source={x:Reference this}, Path=CommandOpenReferenceLink}"
                                    BackgroundColor="White"
                                    FontFamily="FontTextMedium"
                                    FontSize="15"
                                    HorizontalOptions="Start"
                                    TextColor="{x:StaticResource ColorPrimary}">
                                    <draw:SkiaLabel.Spans>

                                        <draw:TextSpan
                                            x:Name="ReferenceLinkSpan"
                                            Text="Источник" />

                                        <draw:TextSpan Text=" " />

                                        <draw:SvgSpan
                                            Width="17"
                                            Height="17"
                                            Source="Images/linkout.svg"
                                            TintColor="{x:StaticResource ColorPrimary}"
                                            VerticalAlignement="Center" />

                                    </draw:SkiaLabel.Spans>

                                </draw:SkiaLabel>

                            </draw:SkiaLayout>
                        </draw:Canvas>

                        <Button
                            Margin="0,30,0,0"
                            Command="{Binding Source={x:Reference this}, Path=GoToTests}"
                            Style="{x:StaticResource yellow_btn}"
                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Tests.GoToTests}"
                            VerticalOptions="Start" />

                        <Button
                            Margin="0,12,0,0"
                            Command="{Binding Source={x:Reference this}, Path=PassAgain}"
                            Style="{x:StaticResource grey_cornered_btn}"
                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Tests.PassAgain}"
                            VerticalOptions="Start" />

                        <Button
                            x:Name="deleteResultBtn"
                            Margin="0,12,0,100"
                            Command="{Binding Source={x:Reference this}, Path=DeleteResult}"
                            Style="{x:StaticResource grey_cornered_btn}"
                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Tests.DeleteResult}"
                            VerticalOptions="Start" />



                    </StackLayout>
                </ScrollView>
            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>