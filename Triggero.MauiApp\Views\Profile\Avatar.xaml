﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
             x:Name="this"
             x:Class="Triggero.Controls.Avatar">
  <ContentView.Content>
        <pancakeview:PancakeView 
            HeightRequest="115"
            WidthRequest="115"
            StrokeShape="RoundRectangle 57"
            BackgroundColor="#D6E8F7"
            StrokeThickness="3"
            Stroke="{x:StaticResource ColorPrimary}"
            Padding="0">
            <Grid>
                <Image
                    Source="{Binding Source={x:Reference this},Path=AvatarWrapper.Image}"
                    Margin="20"
                    x:Name="img"/>
            </Grid>
        </pancakeview:PancakeView>
    </ContentView.Content>
</ContentView>