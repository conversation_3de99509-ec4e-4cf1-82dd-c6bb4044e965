﻿using AppoMobi.Specials;
using System.Threading.Tasks;
using Triggero.Domain.Models;
using Triggero.MauiMobileApp.Views.Pages.Profile.Report;

namespace Triggero.MauiMobileApp.Views.MoodTracker
{

    public partial class TrackerStatsView : ContentView
    {
        public DateTime From { get; set; } = DateTime.Now.AddDays(-7);
        public DateTime To { get; set; } = DateTime.Now;
        public TrackerStatsView()
        {
            InitializeComponent();

            Tasks.StartDelayed(TimeSpan.FromSeconds(1), async () =>
            {
                await Load();
            });

        }

        private bool _IsBusy;
        public bool IsBusy
        {
            get
            {
                return _IsBusy;
            }
            set
            {
                if (_IsBusy != value)
                {
                    _IsBusy = value;
                    OnPropertyChanged();

                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        if (MainLayout != null)
                        {
                            if (value)
                            {
                                MainLayout.InputTransparent = true;
                                MainLayout.Opacity = 0.5;
                            }
                            else
                            {
                                MainLayout.InputTransparent = false;
                                MainLayout.Opacity = 1.0;
                            }
                        }
                    });
                }
            }
        }

        private async Task Load()
        {
            try
            {
                IsBusy = true;
                await Task.Delay(10);

                var items = await ApplicationState.Data.GetMoodtrackerItemsAtPeriod(From, To);

                MainThread.BeginInvokeOnMainThread(() =>
                {

                    moodChart.BuildChart(items);
                    gaugeChart.BuildChart(items);
                    pentagramChart.BuildChart(items);
                    affectsView.BuildChart(items);

                    IsBusy = false;
                });

            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                IsBusy = false;
            }

        }


        #region Выбор периода
        private void week1_checkedChanged(object sender, CheckedChangedEventArgs e)
        {
            if (e.Value)
            {
                From = DateTime.Now.AddDays(-7);
                To = DateTime.Now;
                _ = Load();
            }
        }
        private void week2_checkedChanged(object sender, CheckedChangedEventArgs e)
        {
            if (e.Value)
            {
                From = DateTime.Now.AddDays(-14);
                To = DateTime.Now;
                _ = Load();
            }
        }
        private void month1_checkedChanged(object sender, CheckedChangedEventArgs e)
        {
            if (e.Value)
            {
                From = DateTime.Now.AddMonths(-1);
                To = DateTime.Now;
                _ = Load();
            }
        }
        private async void periodFrameBtnTapped(object sender, EventArgs e)
        {
            var page = new ReportPeriodPage();
            page.Selected += Page_PeriodSelected;
            App.OpenPage(page);
        }

        private void Page_PeriodSelected(object sender, MoodTrackerReportSettings e)
        {
            From = e.From;
            To = e.To;

            week1_rb.IsChecked = false;
            week2_rb.IsChecked = false;
            month1_rb.IsChecked = false;

            _ = Load();
        }

        #endregion


    }
}