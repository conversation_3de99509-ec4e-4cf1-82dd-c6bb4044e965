﻿using AppoMobi.Maui.Gestures;

using System.Linq;
using System.Threading.Tasks;
using DrawnUi.Models;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.ViewModels;
using Triggero.MauiMobileApp.Views.Drawn;
using Triggero.Models.Practices;
using Triggero.Models.Tests;

namespace Triggero.Controls.Templates
{

    public partial class CellElementDrawn : FastCellWithBanner
    {
        private BlurEffect _effectBlur;

        public CellElementDrawn()
        {
            InitializeComponent();
        }

        public override void OnDisposing()
        {
            base.OnDisposing();

            _effectBlur?.Dispose();
        }

        void SetPassed(bool value)
        {
            if (_effectBlur == null)
            {
                _effectBlur = new BlurEffect
                {
                    Amount = 6
                };
            }
            if (value)
            {
                if (!ImageBanner.VisualEffects.Contains(_effectBlur))
                {
                    ImageBanner.VisualEffects.Add(_effectBlur);
                }
                OverlayPassed.IsVisible = true;
            }
            else
            {
                if (ImageBanner.VisualEffects.Count > 0)
                    ImageBanner.VisualEffects.Clear();
                OverlayPassed.IsVisible = false;
            }
        }

        protected override bool ApplyContext()
        {
            var applied = base.ApplyContext();

            if (BindingContext is IElementDetails item)
            {
                LabelTitle.Text = item.GetLocalizedTitle(LanguageHelper.LangCode);
                ImageBanner.Source = App.GetFullImageUrl(item.IconImgPath, ThumbnailSize.Small, ThumbnailType.Jpg);
                applied = true;
            }

            if (BindingContext is Exercise exercise)
            {
                SetPassed(false);
                favoriteRb.DefaultValue = ApplicationState.UserFavorites.HasFavoriteExercise(exercise.Id);
                LabelDetails.Text = $"{exercise.PassingTimeInMinutes} {App.Instance.Interface.Library.Library.MinutesAbbrevated}";
                LabelTypeDescription.Text = App.Instance.Interface.Library.Library.CompletingLowercase;
                ImageTypeIcon.Source = "graypen.png";
                applied = true;
            }
            else
            if (BindingContext is Topic topic)
            {
                SetPassed(false);
                favoriteRb.DefaultValue = ApplicationState.UserFavorites.HasFavoriteTopic(topic.Id);
                LabelDetails.Text = $"{topic.PassingTimeInMinutes} {App.Instance.Interface.Library.Library.MinutesAbbrevated}";
                LabelTypeDescription.Text = App.Instance.Interface.Library.Library.ReadingLowercase;
                ImageTypeIcon.Source = "eyegray.png";
                applied = true;
            }
            else
            if (BindingContext is Practice practice)
            {
                SetPassed(false);
                favoriteRb.DefaultValue = ApplicationState.UserFavorites.HasFavoritePractice(practice.Id);
                LabelDetails.Text = $"{practice.PassingTimeInMinutes} {App.Instance.Interface.Library.Library.MinutesAbbrevated}";
                LabelTypeDescription.Text = App.Instance.Interface.Library.Library.AudioLowercase;
                ImageTypeIcon.Source = "grayaudioicon.png";
                applied = true;
            }
            else
            if (BindingContext is Test test)
            {
                if (test.Id != 0)
                {
                    favoriteRb.DefaultValue = ApplicationState.UserFavorites.HasFavoriteTest(test.Id);
                    SetPassed(ApplicationState.Data.TestPassingResults.Any(o => o.TestId == test.Id));
                }
                else
                {
                    SetPassed(false);
                }
                LabelDetails.Text = $"{test.Questions.Count} вопросов";
                LabelTypeDescription.Text = App.Instance.Interface.Tests.TestingLowercase;
                ImageTypeIcon.Source = "testgrayicon.png";
                applied = true;
            }

            return applied;
        }

        IElementDetails Model
        {
            get
            {
                return BindingContext as IElementDetails;
            }
        }

        private bool? currentFav;

        private void favoriteRb_Toggled(object sender, bool e)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                favoriteRb.DefaultValue = !favoriteRb.DefaultValue;
                SaveFavsToRemoteDelayed(1500);
            });
        }

        #region save on timer

        protected RestartingTimer<object> TimerUpdateLocked;

        protected async Task SaveFavsToRemote()
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    if (Model != null)
                    {
                        if (BindingContext is Exercise exercise)
                        {
                            favoriteRb.DefaultValue = await ApplicationState.UserFavorites.Set(Model.Id, AppContentType.Exercise, favoriteRb.IsToggled, exercise);
                        }
                        else
                        if (BindingContext is Practice practice)
                        {
                            favoriteRb.DefaultValue = await ApplicationState.UserFavorites.Set(Model.Id, AppContentType.Practice, favoriteRb.IsToggled, practice);
                        }
                        else
                        if (BindingContext is Topic topic)
                        {
                            favoriteRb.DefaultValue = await ApplicationState.UserFavorites.Set(Model.Id, AppContentType.Topic, favoriteRb.IsToggled, topic);
                        }
                        else
                        if (BindingContext is Test test)
                        {
                            favoriteRb.DefaultValue = await ApplicationState.UserFavorites.Set(Model.Id, AppContentType.Test, favoriteRb.IsToggled, test);
                        }

                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            });

        }

        protected void SaveFavsToRemoteDelayed(int ms)
        {
            if (TimerUpdateLocked == null)
            {
                TimerUpdateLocked = new RestartingTimer<object>(TimeSpan.FromMilliseconds(ms), async (context) =>
                {
                    await SaveFavsToRemote();
                });
                TimerUpdateLocked.Start(null);
            }
            else
            {
                TimerUpdateLocked.Restart(null);
            }
        }

        #endregion


        private void SkiaHotspot_Tapped(object? sender, ControlTappedEventArgs controlTappedEventArgs)
        {
            if (TouchEffect.CheckLockAndSet())
                return;

            if (this.Parent.BindingContext is ElementsListViewModel vm)
            {
                vm.CommandChildTapped.Execute(this);
            }
        }
    }
}