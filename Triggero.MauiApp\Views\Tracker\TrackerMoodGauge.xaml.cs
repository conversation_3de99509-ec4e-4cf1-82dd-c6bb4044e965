﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models.MoodTracker.User;



namespace Triggero.Controls.MoodTracker
{
	
	public partial class TrackerMoodGauge : ContentView
	{
		public TrackerMoodGauge()
		{
			InitializeComponent();
		}


        private void SetMoodColor(string hex)
        {
            moodTitleLabel.TextColor = Color.FromHex(hex);
            
            arrow.NeedleFill = Color.FromHex(hex);
            arrow.KnobStroke = Color.FromHex(hex);
        }

        public async void BuildChart(List<MoodtrackerItem> items)
		{
			double avg = 5;
			if (items.Any())
			{
				avg = items.Average(o => o.Mood);
				avg = Math.Round(avg, 0);
            }

            //avg =5;

            arrow.Value = avg;

            int avgInt = (int)Math.Round(avg,0);
			switch (avgInt)
			{
                case 5:
                    moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood5;
                    SetMoodColor("#FFCD62");
                    break;
                case 4:
                    moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood4;
                    SetMoodColor("#F3D7A8");
                    break;
                case 3:
                    moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood3;
                    SetMoodColor("#CFE4F7");
                    break;
                case 2:
                    moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood2;
                    SetMoodColor("#AEDCEE");
                    break;
                case 1:
                    moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood1;
                    SetMoodColor("#7AD1E2");
                    break;
                case 0:
                    moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood0;
                    SetMoodColor("#02C0CD");
                    break;
            }
		}

	}
}