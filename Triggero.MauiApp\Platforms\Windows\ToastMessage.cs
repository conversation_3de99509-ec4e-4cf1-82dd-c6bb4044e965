using Triggero.MauiMobileApp.Abstractions;

namespace Triggero.MauiMobileApp.Platforms.Windows
{
    public class ToastMessage : IToastMessage
    {
        public void ShortAlert(string message)
        {
            try
            {
                // TODO: Windows toast implementation
                // Could use Windows.UI.Notifications.ToastNotificationManager
                // For development, just log to debug console
                System.Diagnostics.Debug.WriteLine($"[ToastMessage] Windows ShortAlert: {message}");
                
                // For development UI testing, could show a simple dialog
                ShowDebugToast(message, "Short");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ToastMessage] Windows ShortAlert failed: {ex.Message}");
            }
        }

        public void LongAlert(string message, ToastPosition position)
        {
            try
            {
                // TODO: Windows toast implementation with positioning
                System.Diagnostics.Debug.WriteLine($"[ToastMessage] Windows LongAlert: {message} at {position}");
                
                // For development UI testing, could show a simple dialog
                ShowDebugToast(message, $"Long ({position})");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ToastMessage] Windows LongAlert failed: {ex.Message}");
            }
        }

        private void ShowDebugToast(string message, string type)
        {
            try
            {
                // For Windows development, we can use a simple approach
                // TODO: Replace with proper Windows toast notifications
                
                // Option 1: Just debug output (current)
                System.Diagnostics.Debug.WriteLine($"🍞 TOAST [{type}]: {message}");
                
                // Option 2: Could use Application.Current.MainPage.DisplayAlert for testing
                // MainThread.BeginInvokeOnMainThread(async () =>
                // {
                //     if (Application.Current?.MainPage != null)
                //     {
                //         await Application.Current.MainPage.DisplayAlert("Toast", $"[{type}] {message}", "OK");
                //     }
                // });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ToastMessage] ShowDebugToast failed: {ex.Message}");
            }
        }
    }
}
