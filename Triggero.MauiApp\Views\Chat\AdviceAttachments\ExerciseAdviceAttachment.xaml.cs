﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Practices;



namespace Triggero.Controls.Chat.AdviceAttachments
{

    public partial class ExerciseAdviceAttachment : ContentView
    {
        private Exercise _exercise;
        public ExerciseAdviceAttachment(Exercise exercise)
        {
            _exercise = exercise;
            InitializeComponent();

            Load();
        }

        private void Load()
        {
            titleLabel.Text = _exercise.GetLocalizedTitle(LanguageHelper.LangCode);
            img.Source = Constants.BuildContentUrl(_exercise.IconImgPath);
            minutesLabel.Text = _exercise.PassingTimeInMinutes.ToString();
        }


        public event EventHandler<Exercise> Clicked;
        private void onClick(object sender, EventArgs e)
        {
            Clicked?.Invoke(this, _exercise);
        }
    }
}