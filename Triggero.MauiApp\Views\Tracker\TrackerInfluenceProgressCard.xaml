﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:progressBar="http://schemas.syncfusion.com/maui"
             x:Class="Triggero.Controls.Cards.Tracker.TrackerInfluenceProgressCard">
  <ContentView.Content>
      <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="40"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>


            <Grid Grid.Column="0">
                <Image
                    x:Name="img"
                    VerticalOptions="Center"
                    HorizontalOptions="Center"
                    HeightRequest="33"
                    WidthRequest="33"/>
            </Grid>

            <Grid Grid.Column="1">
                <StackLayout
                    VerticalOptions="Center"
                    Spacing="0">
                    <progressBar:SfLinearProgressBar
                        x:Name="progressBar"
                        TrackHeight="20"
                        TrackCornerRadius="3"
                        ProgressCornerRadius="3"
                        SecondaryProgressCornerRadius="3"
                        VerticalOptions="Start"
                        HeightRequest="20"
                        ProgressFill="{x:StaticResource ColorPrimaryLight}"
                        TrackFill="Transparent"
                        SecondaryProgressFill="Transparent"/>
                    <Label
                        HorizontalOptions="Start"
                        FontSize="8"
                        x:Name="titleLabel"
                        Margin="0,4,0,0"/>
                </StackLayout>
            </Grid>
        </Grid>
  </ContentView.Content>
</ContentView>