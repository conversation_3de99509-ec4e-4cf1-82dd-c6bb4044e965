﻿
using System.Windows.Input;
using Triggero.MauiMobileApp.Enums;


namespace Triggero.MauiMobileApp.Views.Pages.MoodTracker
{

    public partial class TrackerMainPage : ContentPage
    {
        public TrackerMainPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }


        #region Переключение разделов
        private MoodTrackerSectionType moodTrackerSectionType = MoodTrackerSectionType.Calendar;
        public MoodTrackerSectionType MoodTrackerSectionType
        {
            get => moodTrackerSectionType;
            set { moodTrackerSectionType = value; OnPropertyChanged(nameof(MoodTrackerSectionType)); }
        }

        private ICommand showCalendar;
        public ICommand ShowCalendar
        {
            get => showCalendar ??= new RelayCommand(async obj =>
            {
                MoodTrackerSectionType = MoodTrackerSectionType.Calendar;
            });
        }
        private ICommand showStats;
        public ICommand ShowStats
        {
            get => showStats ??= new RelayCommand(async obj =>
            {
                MoodTrackerSectionType = MoodTrackerSectionType.Stats;
            });
        }
        private ICommand showNotes;
        public ICommand ShowNotes
        {
            get => showNotes ??= new RelayCommand(async obj =>
            {
                MoodTrackerSectionType = MoodTrackerSectionType.Notes;
            });
        }
        #endregion

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
    }
}