﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:progressBar="http://schemas.syncfusion.com/maui"
             x:Class="Triggero.MauiMobileApp.Controls.Tests.TestResultScale">
  <ContentView.Content>
      <StackLayout>
          <Label 
              TextColor="{x:StaticResource ColorText}"
              x:Name="scaleTitleLabel"
              Text="Hello Xamarin.Forms!" />
            <Grid 
                Padding="0"
                VerticalOptions="Start"
                HeightRequest="20">
                <progressBar:SfLinearProgressBar
                    x:Name="progressBar"
                    TrackHeight="20"
                    TrackCornerRadius="3"
                    ProgressCornerRadius="3"
                    SecondaryProgressCornerRadius="3"
                    VerticalOptions="Start"
                    HeightRequest="20"
                    ProgressFill="#CEE3F4"
                    TrackFill="Transparent"
                    SecondaryProgressFill="Transparent"/>
                <Label 
                    Margin="5,0,0,0"
                    VerticalOptions="Center"
                    HorizontalOptions="Start"
                    TextColor="{x:StaticResource ColorText}"
                    x:Name="scaleValueLabel"
                    Text="Hello Xamarin.Forms!" />

            </Grid>
        </StackLayout>
  </ContentView.Content>
</ContentView>