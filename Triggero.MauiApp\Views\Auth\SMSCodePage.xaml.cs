﻿using MobileAPIWrapper;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Input;
using AppoMobi.Specials;
using Triggero.MauiMobileApp.Extensions.Helpers;


namespace Triggero.MauiMobileApp.Views.Pages.Auth
{
    public partial class SMSCodePage : ContentPage
    {
        private string _phone;
        private string _code;

        public SMSCodePage(string phone)
        {
            _phone = phone;

            InitializeComponent();

            NavigationPage.SetHasNavigationBar(this, false);
        }

        private bool _editLocked;

        public bool EditLocked
        {
            get { return _editLocked; }
            set
            {
                if (_editLocked != value)
                {
                    _editLocked = value;
                    OnPropertyChanged();
                }
            }
        }


        protected override void OnAppearing()
        {
            base.OnAppearing();
            Tasks.StartDelayed(TimeSpan.FromMilliseconds(250),
                () => { MainThread.BeginInvokeOnMainThread(() => { textEdit1.Focus(); }); });
        }


        private RelayCommand sendCodeAgain;

        public RelayCommand SendCodeAgain
        {
            get => sendCodeAgain ??= new RelayCommand(async obj =>
            {
                await TriggeroMobileAPI.Account.RequestSms(_phone);
            });
        }

        private async void onTextChanged(object sender, EventArgs e)
        {
            var edits = new List<Entry>()
            {
                textEdit1,
                textEdit2,
                textEdit3,
                textEdit4
            };

            _code = textEdit1.Text + textEdit2.Text + textEdit3.Text + textEdit4.Text;
            if (_code?.Length == 4)
            {
                EditLocked = true;
                IsBusy = true;
                await Task.Delay(10);

                try
                {
                    var login = await TriggeroMobileAPI.Account.LoginWithPhone(_phone, _code);
                    if (login != null)
                    {
                        AuthHelper.SetupAuthorization(login.Token);

                        var user = await TriggeroMobileAPI.Account.MyProfileFull();
                        if (user != null)
                        {
                            AuthHelper.Login(user, login.Token);
                            App.SetMainPage(new MainPage());
                        }
                        else
                        {
                            App.ShowToast("Не удалось загрузить профиль пользователя");
                            EditLocked = false;
                        }
                    }
                    else
                    {
                        EditLocked = false;

                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            errorLabel.IsVisible = true;
                            errorLabel.Text = App.This.Interface.Auth.EnterSMSCode.InvalidCode;
                            App.ShowToast(App.This.Interface.Auth.EnterSMSCode.InvalidCode);
                        });
                    }
                }
                catch (Exception exception)
                {
                    Console.WriteLine(exception);
                    EditLocked = false;
                }
                finally
                {
                    IsBusy = false;
                }

                edits.ForEach(o => o.Text = "");
            }
            else if (_code?.Length > 0 && _code?.Length < 4)
            {
                edits[_code.Length].Focus();
            }
            else if (_code?.Length == 0)
            {
                edits[0].Focus();
            }
        }

        private ICommand close;

        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    try
                    {
                        await App.Current.MainPage.Navigation.PopAsync();
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }
                });
            });
        }
    }
}