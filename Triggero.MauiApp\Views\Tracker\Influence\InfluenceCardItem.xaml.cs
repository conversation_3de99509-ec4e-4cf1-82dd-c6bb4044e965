﻿using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.MoodTracker;


namespace Triggero.Controls.Cards.Tracker.Influence
{

    public partial class InfluenceCardItem : ContentView
    {
        public FactorDetail Detail { get; set; }
        public InfluenceCardItem(FactorDetail detail)
        {
            Detail = detail;
            InitializeComponent();
            Load();

        }

        private async void Load()
        {
            titleLabel.Text = Detail.GetLocalizedTitle(LanguageHelper.LangCode);

            double width = 20 + titleLabel.Text.Length * 8;
            if (width > 250) width = 250;

            this.WidthRequest = width;
        }
    }
}