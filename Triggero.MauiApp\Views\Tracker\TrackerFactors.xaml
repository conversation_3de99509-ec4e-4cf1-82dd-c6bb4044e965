﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             
             x:Name="this"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             BackgroundColor="White"
             x:Class="Triggero.MauiMobileApp.Views.MoodTracker.TrackerFactors">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="152"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="110"/>
            </Grid.RowDefinitions>

            <Image
                Aspect="Fill"
                Source="lightbluegradientbg.png"
                Grid.RowSpan="3"/>

            <Grid Grid.Row="0">

                <StackLayout
                    HorizontalOptions="Fill"
                    VerticalOptions="End"
                    Spacing="8">
                    <Label 
                        TextColor="{x:StaticResource ColorText}"
                        FontSize="17"
                        FontAttributes="Bold"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerFactors.Header}"/>
                    <Label 
                        TextColor="{x:StaticResource ColorTextGray}"

                        FontSize="14"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerFactors.Description}"/>
                </StackLayout>


            </Grid>

            <Grid Grid.Row="1">

                <!--<sh:Shadows 
                    Shades="{sh:SingleShade Offset='2, 2',
                                            BlurRadius=12,
                                            Opacity=0.06,
                                            Color=#27527A}">-->
                    <Frame
                        Margin="20,20,20,20"
                        VerticalOptions="Start"
                        BackgroundColor="#FFFFFF"
                        Padding="20"
                        CornerRadius="15"
                        HasShadow="False">
                        <Grid
                            x:Name="gridLayout"
                            RowSpacing="14"
                            ColumnSpacing="14">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="86"/>
                            </Grid.RowDefinitions>
                        </Grid>
                    </Frame>

                <!--</sh:Shadows>-->
            </Grid>

            <Grid Grid.Row="2">
                <Grid
                    Margin="20,0,20,0"
                    VerticalOptions="Start"
                    ColumnSpacing="16"
                    HeightRequest="56">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="56"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <ImageButton
                        Grid.Column="0"
                        Command="{Binding Source={x:Reference this},Path=GoBack}"
                        Source="buttonbackbordered.png"
                        WidthRequest="56"
                        HeightRequest="56"
                        HorizontalOptions="Center"
                        VerticalOptions="Center"
                        BackgroundColor="Transparent"
                        CornerRadius="0"/>

                    <Button 
                        Grid.Column="1"
                        x:Name="goNextBtn"
                        Command="{Binding Source={x:Reference this},Path=GoNext}"
                        VerticalOptions="Fill"
                        Style="{x:StaticResource yellow_btn}"
                        Text="Далее 3/5"/>


                </Grid>
            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>