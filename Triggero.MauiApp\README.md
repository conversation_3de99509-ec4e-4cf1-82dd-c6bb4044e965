﻿# Triggero

# TODO

Android:

Where is the logo in splash ?

Other:

`TrackerStatsView`:
 ```xml
 
                <!--TODO RE-ENABLE-->
                <moodtracker:TrackerMoodGauge
                    IsVisible="False"
                    x:Name="gaugeChart"
                    Margin="0,50,0,0"
                    HeightRequest="380"
                    VerticalOptions="Start" />
 ```

## TEST PUSHES + PUSHES SETTINGS

[_] Test Token Registration: Check logs for successful token generation and API calls

[_] Test Notification Display: Send test notifications and verify they appear

[_] Test Navigation: Tap notifications with different type values to verify navigation

[_] Test Background Processing: Send notifications with refresh=true while app is backgrounded

[_] Test Permissions: Verify permission prompts appear on first launch

### Much Optional

* Make whole app drawn




