using System.Globalization;

namespace Triggero.MauiMobileApp.Converters
{
    public class ConverterBase : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return OnValueReceived(value, targetType, parameter, culture);
        }

        public virtual object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class StringNotEmptyConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string)
            {
                if (string.IsNullOrEmpty((string)value)) return false;
            }
            else
            {
                return false;
            }
            return true;
        }
    }

    public class IsStringNullOrEmptyConverter : ConverterBase
    {
        public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string)
            {
                if (!string.IsNullOrEmpty((string)value)) return false;
            }

            return true;
        }
    }

    public class BoolToColorConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool boolValue && parameter is string colorParameter)
            {
                var colors = colorParameter.Split('|');
                if (colors.Length == 2)
                {
                    // Return selected color if true, unselected if false
                    var colorString = boolValue ? colors[0] : colors[1];
                    return Color.FromArgb(colorString);
                }
            }

            // Default fallback
            return Colors.Gray;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
