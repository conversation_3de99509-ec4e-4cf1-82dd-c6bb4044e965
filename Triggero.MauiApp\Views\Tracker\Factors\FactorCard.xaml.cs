﻿using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.MoodTracker;


namespace Triggero.Controls.Cards.Tracker.Factors
{

    public partial class FactorCard : ContentView
    {

        public FactorCard(Factor factor)
        {
            Factor = factor;
            InitializeComponent();

            Load();
        }
        private async void Load()
        {
            titleLabel.Text = Factor.GetLocalizedTitle(LanguageHelper.LangCode);
            img.Source = await ResorcesHelper.GetImageSource(Factor.ImgPath);
        }


        private Factor factor;
        public Factor Factor
        {
            get { return factor; }
            set { factor = value; OnPropertyChanged(nameof(Factor)); }
        }


        #region Selection
        private bool isSelected;
        public bool IsSelected
        {
            get { return isSelected; }
            set { isSelected = value; OnPropertyChanged(nameof(IsSelected)); }
        }
        public event EventHandler<Factor> Tapped;
        private void onTapped(object sender, EventArgs e)
        {
            Tapped?.Invoke(this, factor);
        }

        #endregion


    }
}