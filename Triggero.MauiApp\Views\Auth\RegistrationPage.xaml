﻿<?xml version="1.0" encoding="utf-8"?>

<pages1:BasePage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Auth.RegistrationPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pages="clr-namespace:Triggero.MauiMobileApp.Views.Pages;assembly=Triggero.MauiMobileApp"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
    xmlns:pages1="clr-namespace:Triggero.MauiMobileApp.Views.Pages"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="this">
    <ContentPage.Content>
        <Grid>


            <Image
                Aspect="Fill"
                HorizontalOptions="Fill"
                Source="lightbluegradientbg.png"
                VerticalOptions="Fill" />

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="200" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>


                <Grid Grid.Row="0">

                    <ImageButton
                        Command="{Binding Source={x:Reference this},Path=GoToLogin}"
                        Source="buttonbackbordered.png"
                        WidthRequest="56"
                        HeightRequest="56"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        Margin="20,0,0,0"
                        BackgroundColor="Transparent"
                        CornerRadius="0" />

                    <Label
                        TextColor="{x:StaticResource ColorText}"
                        FontSize="22"
                        VerticalOptions="End"
                        HorizontalOptions="Start"
                        FontAttributes="Bold"
                        Margin="20"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.CreateYourAccount}" />

                </Grid>

                <pancakeview:PancakeView
                    Grid.Row="1"
                    Padding="0"
                    BackgroundColor="#FFFFFF"
                    StrokeShape="RoundRectangle 15,15,0,0">
                    <Grid>

                        <ScrollView>
                            <StackLayout
                                Margin="20,20,20,0"
                                Spacing="0">

                                <Label
                                    Margin="0,0,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Start"
                                    Opacity="0.5"
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Start">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="*" />
                                                <Span
                                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.YourName}" />
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>

                                <!--grayTextEdit-->
                                <Grid
                                    HeightRequest="50"
                                    Margin="0,10,0,0">
                                    <draw:Canvas HorizontalOptions="Fill" VerticalOptions="Fill">
                                        <draw:SkiaShape
                                            HorizontalOptions="Fill" VerticalOptions="Fill"
                                            UseCache="Operations"
                                            StrokeColor="#DEEAF6"
                                            StrokeWidth="1.5"
                                            CornerRadius="16" />
                                    </draw:Canvas>

                                    <Entry
                                        Margin="10,0"
                                        Style="{x:StaticResource grayTextEdit}"
                                        VerticalOptions="Fill"
                                        HorizontalOptions="Fill"
                                        Text="{Binding Source={x:Reference this}, Path=UserModel.Name, Mode=TwoWay}"
                                        Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.EnterYourName}" />
                                </Grid>


                                <Label
                                    Margin="0,20,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Start"
                                    Opacity="0.5"
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Start">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="*" />
                                                <Span
                                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.Email}" />
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>

                                <!--todo                                     CharacterCasing="Lower"-->
                                <!--grayTextEdit-->
                                <Grid
                                    HeightRequest="50"
                                    Margin="0,10,0,0">
                                    <draw:Canvas HorizontalOptions="Fill" VerticalOptions="Fill">
                                        <draw:SkiaShape
                                            HorizontalOptions="Fill" VerticalOptions="Fill"
                                            UseCache="Operations"
                                            StrokeColor="#DEEAF6"
                                            StrokeWidth="1.5"
                                            CornerRadius="16" />
                                    </draw:Canvas>

                                    <Entry
                                        Margin="10,0"
                                        Style="{x:StaticResource grayTextEdit}"
                                        VerticalOptions="Fill"
                                        HorizontalOptions="Fill"
                                        Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.EnterEmail}"
                                        Text="{Binding Source={x:Reference this}, Path=UserModel.Email, Mode=TwoWay}" />
                                </Grid>


                                <Label
                                    Margin="0,20,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Start"
                                    Opacity="0.5"
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Start">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="*" />
                                                <Span
                                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.Phone}" />
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>

                                <Grid
                                    HeightRequest="50"
                                    Margin="0,10,0,0">
                                    <draw:Canvas HorizontalOptions="Fill" VerticalOptions="Fill">
                                        <draw:SkiaShape
                                            HorizontalOptions="Fill" VerticalOptions="Fill"
                                            UseCache="Operations"
                                            StrokeColor="#DEEAF6"
                                            StrokeWidth="1.5"
                                            CornerRadius="16" />
                                    </draw:Canvas>

                                    <Entry
                                        Margin="10,0"
                                        Keyboard="Telephone"
                                        Style="{x:StaticResource grayTextEdit}"
                                        VerticalOptions="Fill"
                                        HorizontalOptions="Fill"
                                        Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.EnterPhone}"
                                        Text="{Binding Source={x:Reference this}, Path=UserModel.Phone, Mode=TwoWay}" />
                                </Grid>


                                <Label
                                    Margin="0,20,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Start"
                                    Opacity="0.5"
                                    TextColor="{x:StaticResource ColorText}"
                                    VerticalOptions="Start">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="*" />
                                                <Span
                                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.Password}" />
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>

                                <Grid
                                    HeightRequest="50"
                                    Margin="0,10,0,0">
                                    <draw:Canvas HorizontalOptions="Fill" VerticalOptions="Fill">
                                        <draw:SkiaShape
                                            HorizontalOptions="Fill" VerticalOptions="Fill"
                                            UseCache="Operations"
                                            StrokeColor="#DEEAF6"
                                            StrokeWidth="1.5"
                                            CornerRadius="16" />
                                    </draw:Canvas>

                                    <Entry
                                        Margin="10,0"
                                        Style="{x:StaticResource grayTextEdit}"
                                        VerticalOptions="Fill"
                                        HorizontalOptions="Fill"
                                        IsPassword="True"
                                        Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.EnterPassword}"
                                        Text="{Binding Source={x:Reference this}, Path=UserModel.Password, Mode=TwoWay}" />
                                </Grid>


                                <StackLayout
                                    Margin="-5,20,0,0"
                                    Orientation="Horizontal"
                                    Spacing="4">
                                    <CheckBox
                                        IsChecked="{Binding Source={x:Reference this}, Path=PrivacyAggreed, Mode=TwoWay}"
                                        Color="#EBEBEB" />


                                    <!--  PRVACY CHECK  -->
                                    <Label
                                        Margin="0,6,0,0"
                                        FontSize="12"

                                        HorizontalOptions="Start"
                                        TextColor="{x:StaticResource ColorText}"
                                        WidthRequest="320">
                                        <Label.GestureRecognizers>
                                            <TapGestureRecognizer
                                                Command="{Binding Source={x:Reference this}, Path=CommandPrivacy}" />
                                        </Label.GestureRecognizers>
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <FormattedString.Spans>
                                                    <Span
                                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.AcceptRules}" />
                                                    <Span Text=" " />
                                                    <Span
                                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.PrivacyPolicyLowercase}"
                                                        TextDecorations="Underline" />
                                                </FormattedString.Spans>
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>
                                </StackLayout>

                                <!--  ERROR  -->
                                <Label
                                    x:Name="errorLabel"
                                    Margin="0,0,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    IsVisible="False"
                                    Opacity="0.5"
                                    Text=""
                                    TextColor="{x:StaticResource ColorPrimary}"
                                    VerticalOptions="Start" />

                                <Button
                                    Margin="0,40,0,0"
                                    Command="{Binding Source={x:Reference this}, Path=CommandRegisterAccount}"
                                    Style="{x:StaticResource yellow_btn}"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.CreateAccount}"
                                    VerticalOptions="Start" />

                                <StackLayout
                                    Margin="0,26,0,0"
                                    HorizontalOptions="Center"
                                    Orientation="Horizontal"
                                    Spacing="12">
                                    <Label
                                        FontSize="12"
                                        Opacity="0.5"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.HaveAlreadyAccount}"
                                        TextColor="{x:StaticResource ColorText}" />

                                    <Label
                                        FontAttributes="Bold"
                                        FontSize="12"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.SignIn}"
                                        TextColor="{x:StaticResource ColorText}"
                                        TextDecorations="Underline">
                                        <Label.GestureRecognizers>
                                            <TapGestureRecognizer
                                                Command="{Binding Source={x:Reference this}, Path=GoToLogin}" />
                                        </Label.GestureRecognizers>
                                    </Label>
                                </StackLayout>


                            </StackLayout>
                        </ScrollView>

                    </Grid>
                </pancakeview:PancakeView>

            </Grid>

        </Grid>
    </ContentPage.Content>
</pages1:BasePage>