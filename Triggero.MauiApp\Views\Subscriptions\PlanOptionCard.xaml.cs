﻿using Triggero.Models.Plans;

namespace Triggero.Controls
{

    public partial class PlanOptionCard : ContentView
    {
        public PlanOption Option { get; set; }
        public PlanOptionCard(PlanOption option, bool hasSwitch)
        {
            Option = option;
            InitializeComponent();

            MainSwitch.IsVisible = hasSwitch;
            titleLabel.Text = Option.GetLocalizedTitle(LanguageHelper.LangCode);
            descriptionLabel.Text = Option.GetLocalizedDescription(LanguageHelper.LangCode);
            priceSpan.Text = ((int)Option.Price).ToString();
        }



        public event EventHandler<bool> SelectionChanged;

        private bool isSelected;
        public bool IsSelected
        {
            get { return isSelected; }
            set
            {
                isSelected = value;
                OnPropertyChanged(nameof(IsSelected));

                SelectionChanged?.Invoke(this, value);
            }
        }
    }
}