﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models.Enums;




namespace Triggero.MauiMobileApp.Controls.Other
{

    public partial class ExerciseEmojiRadioButton : RadioButton
    {
        public ExerciseEmojiRadioButton()
        {
            InitializeComponent();
        }

        public static readonly BindableProperty ImgPathProperty = BindableProperty.Create(nameof(ImgPath), typeof(string), typeof(ExerciseEmojiRadioButton), default(string));
        public string ImgPath
        {
            get { return (string)GetValue(ImgPathProperty); }
            set { SetValue(ImgPathProperty, value); }
        }
        public static readonly BindableProperty TextProperty = BindableProperty.Create(nameof(Text), typeof(string), typeof(ExerciseEmojiRadioButton), default(string));
        public string Text
        {
            get { return (string)GetValue(TextProperty); }
            set { SetValue(TextProperty, value); }
        }
        public static readonly BindableProperty EmojiTypeProperty = BindableProperty.Create(nameof(EmojiType), typeof(FeelingEmojiType), typeof(ExerciseEmojiRadioButton));
        public FeelingEmojiType EmojiType
        {
            get { return (FeelingEmojiType)GetValue(EmojiTypeProperty); }
            set { SetValue(EmojiTypeProperty, value); }
        }


        public event EventHandler<FeelingEmojiType> Tapped;
        private void onTapped(object sender, EventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                // Смотри иерархию в верстке, костыль
                var stackLayout = (sender as Frame).Content as StackLayout;
                var emoji = stackLayout.Children[0] as ImageButton;

                IsChecked = !IsChecked;
                await emoji.ScaleTo(1.15, 300);
                await emoji.ScaleTo(1, 300);

                Tapped?.Invoke(this, EmojiType);
            });
        }

        public void SetChecked()
        {
            IsChecked = true;
        }
    }
}