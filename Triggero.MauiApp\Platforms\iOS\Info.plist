<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
	</array>
  <key>ITSAppUsesNonExemptEncryption</key>
  <false/>
  <key>CFBundleIdentifier</key>
  <string>com.tirtech.trigero</string>
  <key>CFBundleName</key>
  <string>Triggero</string>
  <key>CFBundleDisplayName</key>
  <string>Triggero</string>
  <key>CFBundleShortVersionString</key>
  <string>300</string>
  <key>CFBundleVersion</key>
  <string>300106</string>
  <key>LSApplicationQueriesSchemes</key>
  <array>
    <string>instagram</string>
    <string>fb</string>
    <string>vk</string>
    <string>triggero</string>
    <string>prefs</string>
    <string>tel</string>
  </array>
  <key>CFBundleURLTypes</key>
  <array>
    <dict>
      <key>CFBundleURLName</key>
      <string>com.tirtech.trigero</string>
      <key>CFBundleURLSchemes</key>
      <array>
        <string>triggero</string>
      </array>
      <key>CFBundleTypeRole</key>
      <string>Editor</string>
      <key>CFBundleURLIconFile</key>
      <string>Icon58</string>
    </dict>
  </array>
  <key>CFBundleLocalizations</key>
  <array>
    <string>ru</string>
  </array>
  <key>UIBackgroundModes</key>
  <array>
    <string>audio</string>
    <string>remote-notification</string>
  </array>
  <key>FirebaseAppDelegateProxyEnabled</key>
  <false/>
  <key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	<key>XSAppIconAssets</key>
	<string>Assets.xcassets/appicon.appiconset</string>
</dict>
</plist>
