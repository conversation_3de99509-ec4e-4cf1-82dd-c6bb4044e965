﻿using System;
using System.Collections.Generic;
using System.Text;
using Triggero.Controls.Templates;
using Triggero.Models;
using Triggero.Models.Practices;
using Triggero.Models.Tests;


namespace Triggero.MauiMobileApp.Controls
{
    public class MaterialsDataTemplateSelector : DataTemplateSelector
    {

        public DataTemplate TestTemplate { get; set; }
        public DataTemplate ExerciseTemplate { get; set; }
        public DataTemplate PracticeTemplate { get; set; }
        public DataTemplate TopicTemplate { get; set; }

        protected override DataTemplate OnSelectTemplate(object item, BindableObject container)
        {
            if (item is Test) return TestTemplate;
            else if (item is Exercise) return ExerciseTemplate;
            else if (item is Practice) return PracticeTemplate;
            else if (item is Topic) return TopicTemplate;
            else return null;
        }
    }
}
