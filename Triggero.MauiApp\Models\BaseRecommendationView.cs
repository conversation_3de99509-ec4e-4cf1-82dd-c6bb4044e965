﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models;


namespace Triggero.MauiPort.Models
{
    public class BaseRecommendationView : ContentView
    {
        protected RecommendationModel _recommendation;
        public BaseRecommendationView(RecommendationModel recommendation)
        {
            _recommendation = recommendation;
        }

        public event EventHandler<RecommendationModel> Tapped;

        public void onTapped(object sender, EventArgs e)
        {
            Tapped?.Invoke(this, _recommendation);
        }

    }
}
