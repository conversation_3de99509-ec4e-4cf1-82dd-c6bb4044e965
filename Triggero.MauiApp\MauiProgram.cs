﻿global using DrawnUi.Draw;
global using Triggero.MauiMobileApp;
global using Triggero.MauiMobileApp.Enums;
global using Triggero.MauiMobileApp.Extensions.Helpers;
global using Triggero.MauiMobileApp.Views;
global using Triggero.MauiPort.Views.Pages;
global using AppoMobi.Maui.Popups;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.LifecycleEvents;
using Plugin.Maui.Audio;
using Sharpnado.MaterialFrame;
using Syncfusion.Maui.Buttons;
using Syncfusion.Maui.Core.Hosting;
using Triggero.MauiMobileApp.Services;
using Triggero.MauiMobileApp.Helpers;
using MobileAPIWrapper;
using Triggero.MauiMobileApp.Handlers;
using Shiny;

namespace Triggero.MauiMobileApp
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var test = new SfSwitch();
            test.SwitchSettings = new SwitchSettings()
            {
            };

            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .AddPopups()
                .UseDrawnUi(new()
                {
                    MobileIsFullscreen = true,
                    DesktopWindow = new()
                    {
                        Height = 812,
                        Width = 375,
                    }
                })

                //For some yet to be discovered reasons, AcrylicBlur value doesn't work in a dynamic context on iOS.
                .UseSharpnadoMaterialFrame(loggerEnable: false)
                .ConfigureSyncfusionCore() //todo create drawn controls
                .AddAudio()
                .UseShiny() //push messages
                .ConfigureCustomHandlers()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "FontText");
                    fonts.AddFont("OpenSans-Bold.ttf", "FontTextBold");
                    fonts.AddFont("OpenSans-BoldItalic.ttf", "FontTextBoldItalic");
                    fonts.AddFont("OpenSans-ExtraBold.ttf", "FontTextExtraBold");
                    fonts.AddFont("OpenSans-ExtraBoldItalic.ttf", "FontTextExtraBoldItalic");
                    fonts.AddFont("OpenSans-Italic.ttf", "FontTextItalic");
                    fonts.AddFont("OpenSans-Light.ttf", "FontTextLight");
                    fonts.AddFont("OpenSans-LightItalic.ttf", "FontTextLightItalic");
                    fonts.AddFont("OpenSans-Medium.ttf", "FontTextMedium");
                    fonts.AddFont("OpenSans-MediumItalic.ttf", "FontTextMediumItalic");
                    fonts.AddFont("OpenSans-SemiBold.ttf", "FontTextSemiBold");
                    fonts.AddFont("OpenSans-SemiBoldItalic.ttf", "FontTextSemiBoldItalic");
                });

            SkiaFontManager.ThrowIfFailedToCreateFont = false;
            SkiaImageManager.ReuseBitmaps = true;

            builder.Services.AddSingleton<IInAppMessager, InAppMessager>();
            builder.Services.AddSingleton<IAudioService, AudioService>();

            // Configure Shiny Push Notifications (native)
#if ANDROID || IOS
            builder.Services.AddPush<PushNotificationDelegate>();
#endif

            // Mobile-optimized API wrapper is ready to use (static initialization)

            // Register platform services
            //builder.AddPlatformServices();

#if DEBUG
            builder.Logging.AddDebug();
#endif

            return builder.Build();
        }


        private static MauiAppBuilder ConfigureCustomHandlers(this MauiAppBuilder builder)
        {
            builder.ConfigureMauiHandlers(handlers =>
            {
#if ANDROID
                handlers.AddHandler<Entry, CustomEntryHandler>();
                handlers.AddHandler<Editor, CustomEditorHandler>();

                Microsoft.Maui.Handlers.ButtonHandler.Mapper.AppendToMapping("MyCustomization", (handler, view) =>
                {
                    if (handler.PlatformView.Background is Android.Graphics.Drawables.RippleDrawable ripple)
                    {
                        ripple.SetColor(Android.Content.Res.ColorStateList.ValueOf(Android.Graphics.Color.ParseColor("#F8B83A")));
                    }
                });

#elif IOS
                handlers.AddHandler<Entry, CustomEntryHandler>();
                handlers.AddHandler<Editor, CustomEditorHandler>();
                handlers.AddHandler<Triggero.MauiMobileApp.Controls.ChatScroll, Triggero.MauiMobileApp.Platforms.iOS.Handlers.ChatScrollHandler>();
#elif WINDOWS
                handlers.AddHandler<Entry, CustomEntryHandler>();
                handlers.AddHandler<Editor, CustomEditorHandler>();
#endif
            });

            return builder;
        }
    }
}