﻿

namespace Triggero.MauiMobileApp.Extensions
{
    public static class ViewExtensions
    {
        public static void AnimateRow(View page,RowDefinition rowDef,double toHeight)
        {
            Animation _animation = null;

            if (rowDef.Height.Value < toHeight)
            {
                // Move back to original height
                _animation = new Animation(
                    (d) => rowDef.Height = new GridLength(Clamp(d, 0, double.MaxValue)),
                    rowDef.Height.Value, toHeight, Easing.SpringIn, () => _animation = null);
            }
            else
            {
                double val = rowDef.Height.Value;
                // Hide the row
                _animation = new Animation(
                    (d) => rowDef.Height = new GridLength(Clamp(d, 0, double.MaxValue)),
                    val, toHeight, Easing.SpringIn, () => _animation = null);
            }

            _animation.Commit(page, Guid.NewGuid().ToString());
        }

        // Make sure we don't go below zero
        private static double Clamp(double value, double minValue, double maxValue)
        {
            if (value < minValue)
            {
                return minValue;
            }

            if (value > maxValue)
            {
                return maxValue;
            }

            return value;
        }
    }
}
