﻿
using System.Windows.Input;
using Triggero.Models.MoodTracker.User;
using Triggero.MauiMobileApp.Views.MoodTracker;


namespace Triggero.MauiMobileApp.Views.Pages.MoodTracker
{

    public partial class TrackerHowAreYou : ContentPage
    {
        public TrackerHowAreYou(MoodtrackerItem item)
        {
            Tracker = item;

            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            goNextBtn.Text = $"{App.This.Interface.MoodTracker.TrackerGeneral.GoNext} 2/5";
        }


        private MoodtrackerItem tracker = new MoodtrackerItem();
        public MoodtrackerItem Tracker
        {
            get { return tracker; }
            set { tracker = value; OnPropertyChanged(nameof(Tracker)); }
        }


        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
        private ICommand goNext;
        public ICommand GoNext
        {
            get => goNext ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new TrackerFactors(Tracker));
            });
        }
    }
}