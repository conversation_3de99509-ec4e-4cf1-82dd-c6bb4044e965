﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Controls.Chat.AdviceAttachments;
using Triggero.Models.Messengers.ChatBot;
using Triggero.Models.Messengers.ChatBot.Attachments;
using Triggero.Models.Messengers.Support;
using Triggero.Models.Practices;
using Triggero.Models.Tests;
using Triggero.MauiMobileApp.Views.Pages.Library;
using Triggero.MauiMobileApp.Views.Pages.Tests;



namespace Triggero.Controls.Chat
{

    public partial class ChatBotLeftTextMessage : ContentView
    {
        private ChatBotMessage message;
        public ChatBotMessage Message
        {
            get { return message; }
            set { message = value; OnPropertyChanged(nameof(Message)); }
        }
        public ChatBotLeftTextMessage(ChatBotMessage msg)
        {
            InitializeComponent();
            Message = msg;
            //var utcOffset = DateTimeOffset.UtcNow;
            timeLabel.Text = msg.SentAt.ToString("HH:mm");


            double suitableWidth = 0;

            var words = msg.Text.Split(' ');
            for (int i = 0; i < words.Length; i++)
            {
                var str = string.Join(" ", words.Take(i + 1));

                double width = 20 + 45 + ((double)str.Length) * 9.65;
                if (width < 360)
                {
                    suitableWidth = width;
                }
                else
                {
                    this.WidthRequest = suitableWidth;
                    break;
                }

                if (i + 1 == words.Length)
                {
                    this.WidthRequest = suitableWidth;
                    break;
                }

            }

            if (message.Attachments.Any())
            {
                this.WidthRequest = 300;
                attachmentsLayout.Margin = new Thickness(20, 0, 0, 8);
            }


            RenderAttachments();
        }

        private void RenderAttachments()
        {
            if (Message.Attachments.Any())
            {
                attachmentsLayout.Margin = new Thickness(attachmentsLayout.Margin.Left,
                                                         attachmentsLayout.Margin.Top,
                                                         attachmentsLayout.Margin.Right,
                                                         attachmentsLayout.Margin.Bottom + 7);
            }

            foreach (var attachment in Message.Attachments)
            {
                if (attachment is ChatBotContentAttachment contentAttachment)
                {
                    if (contentAttachment.Exercise != null)
                    {
                        var view = new ExerciseAdviceAttachment(contentAttachment.Exercise)
                        {
                            MinimumHeightRequest = 83,
                            VerticalOptions = LayoutOptions.Start
                        };
                        view.Clicked += ExerciseTapped;
                        attachmentsLayout.Children.Add(view);
                    }
                    else if (contentAttachment.Topic != null)
                    {
                        var view = new TopicAdviceAttachment(contentAttachment.Topic)
                        {
                            MinimumHeightRequest = 83,
                            VerticalOptions = LayoutOptions.Start
                        };
                        view.Clicked += TopicTapped;
                        attachmentsLayout.Children.Add(view);
                    }
                    else if (contentAttachment.Practice != null)
                    {
                        var view = new PracticeAdviceAttachment(contentAttachment.Practice)
                        {
                            MinimumHeightRequest = 83,
                            VerticalOptions = LayoutOptions.Start
                        };
                        view.Clicked += PracticeTapped;
                        attachmentsLayout.Children.Add(view);
                    }
                    else if (contentAttachment.Test != null)
                    {
                        var view = new TestAdviceAttachment(contentAttachment.Test)
                        {
                            MinimumHeightRequest = 83,
                            VerticalOptions = LayoutOptions.Start
                        };
                        view.Clicked += TestTapped;
                        attachmentsLayout.Children.Add(view);
                    }
                }
            }
        }



        #region Events

        private void TestTapped(object sender, Test e)
        {
            App.OpenPage(new TestPage(e));
        }

        private void PracticeTapped(object sender, Practice e)
        {
            App.OpenPage(new PracticePage(e));
        }

        private void TopicTapped(object sender, Models.Topic e)
        {
            App.OpenPage(new TopicPage(e));
        }
        private void ExerciseTapped(object sender, Exercise e)
        {
            App.OpenPage(new ExercisePage(e));
        }

        #endregion
    }
}