﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Profile.Report.ReportPeriodPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:custom="clr-namespace:Triggero.Custom"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this"
    BackgroundColor="White">
    <ContentPage.Content>

        <Grid RowSpacing="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static mobile:Globals.TopInsets}" />
                <RowDefinition Height="100" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>


            <Grid Grid.Row="1">


                <Grid
                    Margin="0,10,0,0"
                    HeightRequest="30"
                    VerticalOptions="Start">



                    <Grid
                        Margin="15,10,0,0"
                        BackgroundColor="White"
                        HeightRequest="25"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        WidthRequest="40">
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=Close}" />
                        </Grid.GestureRecognizers>
                        <ImageButton
                            BackgroundColor="Transparent"
                            Command="{Binding Source={x:Reference this}, Path=Close}"
                            CornerRadius="0"
                            HeightRequest="16"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            Source="arrowback.png"
                            VerticalOptions="Center"
                            WidthRequest="8" />

                    </Grid>


                    <Label
                        Margin="0,10,20,0"
                        FontAttributes="Bold"
                        FontFamily="FontTextLight"
                        FontSize="14"

                        HorizontalOptions="End"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Profile.ProfileSelectPeriod.Accept}"
                        TextColor="{x:StaticResource ColorAccent}"
                        TextDecorations="Underline"
                        VerticalOptions="Center">
                        <Label.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=SelectDate}" />
                        </Label.GestureRecognizers>
                    </Label>

                </Grid>


                <Grid
                    Margin="38,5,38,4"
                    HeightRequest="30"
                    RowDefinitions="Auto"
                    VerticalOptions="End">

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="1*" />
                    </Grid.ColumnDefinitions>


                    <Label
                        FontSize="14"
                        HorizontalOptions="Center"
                        Opacity="0.6"
                        Text="П"
                        TextColor="#363B40"
                        VerticalOptions="Center" />



                    <Label
                        Grid.Column="1"
                        FontSize="14"
                        HorizontalOptions="Center"
                        Opacity="0.6"
                        Text="В"
                        TextColor="#363B40"
                        VerticalOptions="Center" />



                    <Label
                        Grid.Column="2"
                        FontSize="14"
                        HorizontalOptions="Center"
                        Opacity="0.6"
                        Text="С"
                        TextColor="#363B40"
                        VerticalOptions="Center" />



                    <Label
                        Grid.Column="3"
                        FontSize="14"
                        HorizontalOptions="Center"
                        Opacity="0.6"
                        Text="Ч"
                        TextColor="#363B40"
                        VerticalOptions="Center" />



                    <Label
                        Grid.Column="4"
                        FontSize="14"
                        HorizontalOptions="Center"
                        Opacity="0.6"
                        Text="П"
                        TextColor="#363B40"
                        VerticalOptions="Center" />



                    <Label
                        Grid.Column="5"
                        FontSize="14"
                        HorizontalOptions="Center"
                        Opacity="0.6"
                        Text="С"
                        TextColor="#363B40"
                        VerticalOptions="Center" />



                    <Label
                        Grid.Column="6"
                        FontSize="14"
                        HorizontalOptions="Center"
                        Opacity="0.6"
                        Text="В"
                        TextColor="#363B40"
                        VerticalOptions="Center" />


                </Grid>

            </Grid>


            <BoxView
                Grid.Row="1"
                BackgroundColor="Black"
                HeightRequest="0.5"
                VerticalOptions="End" />

            <Grid Grid.Row="2">
                <!--<custom:ScrollableCalendar x:Name="calendar" />-->

                <ContentView x:Name="CalendarWrapper" />

            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>