﻿using Triggero.MauiMobileApp.Extensions.Helpers;


namespace Triggero.MauiMobileApp.Views.Pages.Auth
{

    public partial class LoginStartPage : ContentPage
    {
        public LoginStartPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            // hiName.Text = string.Format(App.This.Interface.Auth.LoginMainPage.HiName, AuthHelper.User?.Name);
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();

            PlatformUi.Instance.ApplyTheme();
        }

        private RelayCommand goToEmail;
        public RelayCommand GoToEmail
        {
            get => goToEmail ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new LoginByEmailPage());
            });
        }
        private RelayCommand goToPhone;
        public RelayCommand GoToPhone
        {
            get => goToPhone ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new LoginByPhonePage());
            });
        }

        private RelayCommand goToRegistration;
        public RelayCommand GoToRegistration
        {
            get => goToRegistration ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new RegistrationPage());
            });
        }
    }
}