﻿<?xml version="1.0" encoding="UTF-8"?>

<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Chat.ChatBotLeftTextMessage"
             x:Name="this"
             HorizontalOptions="Start"
             MaximumWidthRequest="250">
    <ContentView.Content>

        <Frame
            HorizontalOptions="Start"
            MaximumWidthRequest="250"
            CornerRadius="12"
            BackgroundColor="{x:StaticResource ColorCardLight}"
            HasShadow="False"
            Padding="0">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="50" />
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">
                    <StackLayout Spacing="1">
                        <Label
                            TextColor="{x:StaticResource ColorText}"
                            FontSize="14"
                            Margin="20,8,0,8"
                            VerticalOptions="Start"
                            HorizontalOptions="Start"
                            Text="{Binding Source={x:Reference this},Path=Message.Text}" />
           
                        <StackLayout
                            Margin="20,0,0,3"
                            Spacing="7"
                            x:Name="attachmentsLayout">

                        </StackLayout>
                    </StackLayout>
                </Grid>

                <Grid Grid.Column="1">
                    <Label
                        x:Name="timeLabel"
                        TextColor="{x:StaticResource ColorText}"
                        Opacity="0.5"
                        FontSize="12"
                        Margin="0,10,0,0"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        Text="" />
                </Grid>


            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>