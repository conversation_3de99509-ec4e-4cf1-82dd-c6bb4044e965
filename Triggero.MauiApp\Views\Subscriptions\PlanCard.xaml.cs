﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Plans;



namespace Triggero.Controls
{

    public partial class PlanCard : ContentView
    {
        public Plan Plan { get; private set; }
        public bool IsTrial { get; private set; }
        public PlanCard(Plan plan,bool isTrial)
        {
            Plan = plan;
            IsTrial = isTrial;

            InitializeComponent();
            Render();


            IsSelected = false;
        }

        public void Render()
        {
            RenderUncheckedState();
            RenderCheckedState();
        }

        private void RenderUncheckedState()
        {
            var priceWithDiscount = Plan.Price - (Plan.Price / 100 * Plan.Discount);

            uncheckedPlanTitleLabel.Text = Plan.GetLocalizedTitle(LanguageHelper.LangCode);
            uncheckedPlanPriceLabel.Text = $"{(int)priceWithDiscount} р.";

            uncheckedPlanDiscountFrame.IsVisible = Plan.Discount >= 1;
            uncheckedPlanDiscountLabel.Text = $"-{(int)Plan.Discount}%";

            if (IsTrial)
            {
                uncheckedPricePerMonthLabel.Text = App.This.Interface.Subscriptions.TrialPage.InstantCancelation;
            }
            else
            {
                if(Plan.BuiltInPlanType == Models.Enums.BuiltInPlanType.Year)
                {
					uncheckedPricePerMonthLabel.Text = string.Format(App.This.Interface.Subscriptions.SubscriptionMain.PricePerMonth, (int)(priceWithDiscount / 12));
				}
				else if (Plan.BuiltInPlanType == Models.Enums.BuiltInPlanType.ThreeMonths)
				{
					uncheckedPricePerMonthLabel.Text = string.Format(App.This.Interface.Subscriptions.SubscriptionMain.PricePerMonth, (int)(priceWithDiscount / 3));
				}
				else if (Plan.BuiltInPlanType == Models.Enums.BuiltInPlanType.Month)
				{
                    uncheckedPricePerMonthLabel.Text = "";
					//uncheckedPricePerMonthLabel.Text = string.Format(App.This.Interface.Subscriptions.SubscriptionMain.PricePerMonth, (int)(priceWithDiscount / 1));
				}
			}
        }
        private void RenderCheckedState()
        {
            var priceWithDiscount = Plan.Price - (Plan.Price / 100 * Plan.Discount);

            checkedPlanTitleLabel.Text = Plan.GetLocalizedTitle(LanguageHelper.LangCode);
            checkedPlanPriceLabel.Text = $"{(int)priceWithDiscount} р.";

            checkedPlanDiscountFrame.IsVisible = Plan.Discount >= 1;
            checkedPlanDiscountLabel.Text = $"-{(int)Plan.Discount}%";

            if (IsTrial)
            {
                checkedPricePerMonthLabel.Text = App.This.Interface.Subscriptions.TrialPage.InstantCancelation;
            }
            else
            {
				if (Plan.BuiltInPlanType == Models.Enums.BuiltInPlanType.Year)
				{
					checkedPricePerMonthLabel.Text = string.Format(App.This.Interface.Subscriptions.SubscriptionMain.PricePerMonth, (int)(priceWithDiscount / 12));
				}
				else if (Plan.BuiltInPlanType == Models.Enums.BuiltInPlanType.ThreeMonths)
				{
					checkedPricePerMonthLabel.Text = string.Format(App.This.Interface.Subscriptions.SubscriptionMain.PricePerMonth, (int)(priceWithDiscount / 3));
				}
				else if (Plan.BuiltInPlanType == Models.Enums.BuiltInPlanType.Month)
				{
					checkedPricePerMonthLabel.Text = "";
					//uncheckedPricePerMonthLabel.Text = string.Format(App.This.Interface.Subscriptions.SubscriptionMain.PricePerMonth, (int)(priceWithDiscount / 1));
				}
			}
        }






        public event EventHandler<bool> SelectionChanged;

        private bool isSelected;
        public bool IsSelected
        {
            get { return isSelected; }
            set
            {
                isSelected = value;
                OnPropertyChanged(nameof(IsSelected));

                uncheckedState.IsVisible = !value;
                checkedState.IsVisible = value;

                SelectionChanged?.Invoke(this, value);
            }
        }

        private void onTapped(object sender, EventArgs e)
        {
            IsSelected = true;
        }
    }
}