﻿<?xml version="1.0" encoding="UTF-8"?>

<models:BaseQuestionOptionView

    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"

    xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
    xmlns:models="clr-namespace:Triggero.MauiPort.Models"
    x:Class="Triggero.Controls.Cards.Tests.Questions.Answers.Single.TextAnswerOption"
    x:Name="this">

    <models:BaseQuestionOptionView.Resources>
        <ResourceDictionary>
            <Style TargetType="RadioButton" x:Key="singleAnswer_rb">
                <Style.Triggers>
                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                        <Setter Property="ControlTemplate">
                            <Setter.Value>
                                <ControlTemplate>

                                    <Frame
                                        VerticalOptions="Start"
                                        MinimumHeightRequest="64"
                                        InputTransparent="True"
                                        BackgroundColor="White"
                                        BorderColor="Transparent"
                                        HasShadow="False"
                                        Padding="0"
                                        CornerRadius="12">

                                        <Frame.Shadow>
                                            <Shadow Brush="#27527A"
                                                    Offset="2,2"
                                                    Radius="12"
                                                    Opacity="0.06" />
                                        </Frame.Shadow>

                                        <Grid ColumnSpacing="12"
                                              VerticalOptions="Start">

                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="64" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>


                                            <Image
                                                Aspect="AspectFit"
                                                Source="completedcircleyellow.png"
                                                WidthRequest="24"
                                                HeightRequest="24"
                                                HorizontalOptions="End"
                                                Margin="0,13,0,13"
                                                VerticalOptions="Center"
                                                BackgroundColor="Transparent" />

                                            <Label
                                                Grid.Column="1"
                                                Margin="0,7,10,7"
                                                HorizontalOptions="Start"
                                                VerticalOptions="Center"
                                                FontSize="17"
                                                Text="{Binding Source={x:Reference this},Path=Text}"
                                                TextColor="#363B40" />

                                        </Grid>

                                    </Frame>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Trigger>

                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                        <Setter Property="ControlTemplate">
                            <Setter.Value>

                                <ControlTemplate>
                                    <Frame
                                        VerticalOptions="Start"
                                        MinimumHeightRequest="64"
                                        InputTransparent="True"
                                        BackgroundColor="Transparent"
                                        BorderColor="{x:StaticResource ColorPrimaryLight}"
                                        HasShadow="False"
                                        Padding="0"
                                        CornerRadius="12">

                                        <Grid ColumnSpacing="12"
                                              VerticalOptions="Start">

                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="64" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>


                                            <Frame
                                                Grid.Column="0"
                                                WidthRequest="24"
                                                HeightRequest="24"
                                                CornerRadius="12"
                                                HorizontalOptions="End"
                                                VerticalOptions="Center"
                                                Margin="0,13,0,13"
                                                Padding="0"
                                                HasShadow="False"
                                                BackgroundColor="Transparent"
                                                BorderColor="{x:StaticResource ColorPrimaryLight}" />

                                            <Label
                                                Grid.Column="1"
                                                Margin="0,7,10,7"
                                                HorizontalOptions="Start"
                                                VerticalOptions="Center"
                                                FontSize="17"
                                                Text="{Binding Source={x:Reference this},Path=Text}"
                                                TextColor="#363B40" />

                                        </Grid>

                                    </Frame>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </ResourceDictionary>
    </models:BaseQuestionOptionView.Resources>
    <ContentView.Content>

        <Grid>
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="onRbTapped" />
            </Grid.GestureRecognizers>
            <RadioButton
                GroupName="pxxx"
                InputTransparent="True"
                IsChecked="{Binding Source={x:Reference this},Path=IsChecked,Mode=TwoWay}"
                Content="{Binding Source={x:Reference this},Path=Text}"
                Style="{x:StaticResource singleAnswer_rb}" />
        </Grid>

    </ContentView.Content>
</models:BaseQuestionOptionView>