﻿using System.Windows.Input;
using AppoMobi.Maui.Gestures;
using AppoMobi.Specials;
using DrawnUi.Controls;

namespace Triggero.MauiMobileApp.Views.Drawn;

public class LottieIcon : SkiaLottie, ISkiaGestureListener
{
    /// <summary>
    /// Starts playing animation invoking Start
    /// </summary>
    /// <returns></returns>
    public virtual async Task Animate()
    {
        this.Start();
    }

    #region GESTURES

    public static readonly BindableProperty CommandTappedProperty = BindableProperty.Create(nameof(CommandTapped),
        typeof(ICommand),
        typeof(LottieIcon),
        null);

    public ICommand CommandTapped
    {
        get { return (ICommand)GetValue(CommandTappedProperty); }
        set { SetValue(CommandTappedProperty, value); }
    }

    public static readonly BindableProperty CommandTappedParameterProperty = BindableProperty.Create(
        nameof(CommandTappedParameter), typeof(object),
        typeof(LottieIcon),
        null);

    public object CommandTappedParameter
    {
        get { return GetValue(CommandTappedParameterProperty); }
        set { SetValue(CommandTappedParameterProperty, value); }
    }

    public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args,
        GestureEventProcessingInfo apply)
    {
        //Trace.WriteLine($"SkiaHotspot. {type} {touchAction} {args.Location.X} {args.Location.Y}");

        if (args.Type == TouchActionResult.Tapped)
        {
            var consumed = false;

            var delay = 10;
            //var x = (args.Location.X) / RenderingScale;
            //var y = (args.Location.Y) / RenderingScale;

            if (CommandTapped != null)
            {
                consumed = true;
                Tasks.StartDelayedAsync(TimeSpan.FromMilliseconds(delay),
                    async () =>
                    {
                        await Task.Run(() => { CommandTapped?.Execute(CommandTappedParameter); })
                            .ConfigureAwait(false);
                    });
            }

            return consumed ? this : null;
        }


        return null;
    }

    #endregion

    public bool OnFocusChanged(bool focus)
    {
        return false;
    }
}