using Microsoft.Maui.Controls;

namespace Triggero.MauiMobileApp.Controls
{
    /// <summary>
    /// Custom ScrollView that fixes iOS ScrollToAsync X offset bug for vertical scrolling
    /// </summary>
    public class ChatScroll : ScrollView
    {
        public ChatScroll()
        {
            // Ensure vertical scrolling only
            Orientation = ScrollOrientation.Vertical;
        }

        /// <summary>
        /// Override ScrollToAsync to ensure X offset remains 0 on iOS
        /// </summary>
        /// <param name="x">X coordinate (will be forced to 0 on iOS)</param>
        /// <param name="y">Y coordinate</param>
        /// <param name="animated">Whether to animate the scroll</param>
        /// <returns>Task representing the scroll operation</returns>
        public new async Task ScrollToAsync(double x, double y, bool animated)
        {
            // Force X to 0 for vertical scrolling to prevent horizontal drift
            await base.ScrollToAsync(0, y, animated);
        }

        /// <summary>
        /// Override ScrollToAsync with element to ensure X offset remains 0 on iOS
        /// </summary>
        /// <param name="element">Element to scroll to</param>
        /// <param name="position">Scroll position</param>
        /// <param name="animated">Whether to animate the scroll</param>
        /// <returns>Task representing the scroll operation</returns>
        public new async Task ScrollToAsync(Element element, ScrollToPosition position, bool animated)
        {
            // Use base implementation but the handler will ensure X stays at 0
            await base.ScrollToAsync(element, position, animated);
        }
    }
}
