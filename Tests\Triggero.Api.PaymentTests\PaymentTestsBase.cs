using System;
using System.Net.Http;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NUnit.Framework;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Triggero.Database;
using Microsoft.EntityFrameworkCore;
using Triggero.Api;

namespace Triggero.Api.PaymentTests;

public class PaymentTestsBase
{
    protected WebApplicationFactory<Program> Factory { get; private set; }
    protected HttpClient Client { get; private set; }
    
    // Configuration properties for testing
    public string AuthorizationToken { get; set; } = "";
    public string BaseUrl { get; set; } = "https://localhost:7013";
    
    [OneTimeSetUp]
    public void OneTimeSetUp()
    {
        Factory = new WebApplicationFactory<Program>()
            .WithWebHostBuilder(builder =>
            {
                // Use the API project's existing configuration
                // No need to override - it will use the same secrets.json and appsettings.json
                builder.UseEnvironment("Development");

                builder.ConfigureServices(services =>
                {
                    // Can add test-specific service overrides here if needed
                    // For now, we'll use the same services as the API project
                });
            });

        Client = Factory.CreateClient();

        // Set base address from configuration
        Client.BaseAddress = new Uri(TestConfiguration.ApiBaseUrl);
    }

    [OneTimeTearDown]
    public void OneTimeTearDown()
    {
        Client?.Dispose();
        Factory?.Dispose();
    }

    protected void SetAuthorizationToken(string token)
    {
        AuthorizationToken = token;
        if (!string.IsNullOrEmpty(token))
        {
            Client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        }
        else
        {
            Client.DefaultRequestHeaders.Authorization = null;
        }
    }

    protected async Task<HttpResponseMessage> PostJsonAsync<T>(string endpoint, T data)
    {
        var json = JsonConvert.SerializeObject(data);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        return await Client.PostAsync(endpoint, content);
    }

    protected async Task<TResponse> PostJsonAsync<TRequest, TResponse>(string endpoint, TRequest data)
    {
        var response = await PostJsonAsync(endpoint, data);
        response.EnsureSuccessStatusCode();
        
        var responseContent = await response.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<TResponse>(responseContent);
    }

    protected async Task<T> GetJsonAsync<T>(string endpoint)
    {
        var response = await Client.GetAsync(endpoint);
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<T>(content);
    }

    protected string SerializeObject<T>(T obj)
    {
        return JsonConvert.SerializeObject(obj, Formatting.Indented);
    }
}
