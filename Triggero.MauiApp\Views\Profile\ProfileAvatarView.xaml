﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:custom="clr-namespace:Triggero.Custom" 
             xmlns:controls="clr-namespace:Triggero.Controls" 


             x:Name="this"
             BackgroundColor="White"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Profile.ProfileAvatarView">
    <ContentPage.Content>
        <Grid>

            <Image
                Aspect="Fill"
                Source="profileblur.png"/>

            <ImageButton 
                Command="{Binding Source={x:Reference this},Path=Close}"
                CornerRadius="0"
                WidthRequest="56"
                HeightRequest="56"
                Aspect="Fill"
                Source="closebtnbordered.png"
                Margin="0,60,25,0"
                HorizontalOptions="End"
                VerticalOptions="Start"
                BackgroundColor="Transparent"/>


            <Frame
                Padding="0"
                HasShadow="False"
                CornerRadius="15"
                BackgroundColor="White"
                HeightRequest="237"
                VerticalOptions="Center"
                Margin="20,0,20,0">
                <StackLayout
                    VerticalOptions="Center"
                    Spacing="0">



                    <CarouselView 
                        IsVisible="False"
                        x:Name="MainCarousel"
                        HeightRequest="160"
                        HorizontalOptions="Fill"
                        Position="{Binding Source={x:Reference this}, Path=SelectedIndex, Mode=TwoWay}"
                        Loop="True"
                        ItemsSource="{Binding Source={x:Reference this}, Path=Wrappers}"
                        VerticalOptions="Center"
                        HorizontalScrollBarVisibility="Never"
                        VerticalScrollBarVisibility="Never">

                        <CarouselView.ItemTemplate>
                            <DataTemplate>
                                <ContentView>
                                    <controls:Avatar 
                                        Margin="0,0,0,0"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"
                                        HeightRequest="120"
                                        WidthRequest="120"
                                        AvatarWrapper="{Binding}"/>
                                </ContentView>
                            </DataTemplate>
                        </CarouselView.ItemTemplate>
                    </CarouselView>




                    <custom:DotsView 
                        Margin="0,20,0,0"
                        HeightRequest="8"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        x:Name="dotsView"/>
                </StackLayout>
            </Frame>
            
            
            
            
           
             
      

        


        </Grid>
    </ContentPage.Content>
</ContentPage>