﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models;
using Triggero.Models.Sliders;



namespace Triggero.Controls.Cards.Sliders
{
    
    public partial class TrialSliderItemCard : ContentView
    {
        public TrialSliderItemCard()
        {
            InitializeComponent();
        }

        public static readonly BindableProperty TrialSliderItemProperty = BindableProperty.Create(nameof(TrialSliderItem), typeof(TrialSliderItem), typeof(TrialSliderItemCard));
        public TrialSliderItem TrialSliderItem
        {
            get { return (TrialSliderItem)GetValue(TrialSliderItemProperty); }
            set { SetValue(TrialSliderItemProperty, value); }
        }


    }
}