﻿using Triggero.Models.Tests;
using Triggero.MauiMobileApp.Views;

using Triggero.MauiMobileApp.Extensions.Helpers;

namespace Triggero.Controls.Templates
{

    public partial class TestCategoryCard : ContentView
    {
        public TestCategoryCard()
        {
            InitializeComponent();
        }

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            if (BindingContext is TestCategory item && Model != item)
            {
                Model = item;
                titleLabel.Text = Model.Localizations.GetLocalizedTitle(LanguageHelper.LangCode, Model.Title);
                descLabel.Text = Model.Localizations.GetLocalizedDescription(LanguageHelper.LangCode, Model.Description);

                img.Source = Constants.BuildContentUrl(Model.ImgPath);

                if (Model.Id != 0)
                {
                    titleLabel.FontAttributes = FontAttributes.None;
                }
            }
        }



        private TestCategory model;
        public TestCategory Model
        {
            get { return model; }
            set { model = value; OnPropertyChanged(nameof(Model)); }
        }


        private void onTapped(object sender, EventArgs e)
        {
            if (Model is null) return;

            App.OpenView(new ListTestsView(Model));


        }
    }
}