﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>

    <TargetFrameworks>net9.0-android;net9.0-ios</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>

    <!--<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>-->

    <!--<TargetFrameworks>net9.0-windows10.0.19041.0</TargetFrameworks>-->

    <!--<TargetFrameworks>net9.0-android;net9.0-ios</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>-->

    <_MauiForceXamlCForDebug>true</_MauiForceXamlCForDebug>

		<OutputType>Exe</OutputType>
		<RootNamespace>Triggero.MobileMaui</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>Triggero</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.youmi.triggero</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>3.1</ApplicationDisplayVersion>
		<ApplicationVersion>31</ApplicationVersion>
    <Version>31</Version>

  </PropertyGroup>

  <PropertyGroup>
    <WindowsPackageType>None</WindowsPackageType>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.2</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</TargetPlatformMinVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net*-ios|AnyCPU'">
  </PropertyGroup>

  
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <CodesignEntitlements>Platforms\iOS\Entitlements.Debug.plist</CodesignEntitlements>
</PropertyGroup>

<PropertyGroup Condition="'$(Configuration)' == 'Release'">
  <CodesignProvision>AdHocSummer25</CodesignProvision>
  <CodesignKey>Apple Distribution: YOUMI LLC (YBZN8U99ZN)</CodesignKey>
  <ProvisioningType>manual</ProvisioningType>
    <CodesignEntitlements>Platforms\iOS\Entitlements.Release.plist</CodesignEntitlements>
</PropertyGroup>        
  <ItemGroup>
		
		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#FDCE72" BaseSize="169.5, 181.5" />

		<!-- Images -->
    <MauiImage Include="Resources\Images\*" />
		<!--<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />-->

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
    <MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />

  </ItemGroup>

  <ItemGroup Condition="$(TargetFramework.Contains('android'))">
    <!-- App Icon -->
    <MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundScale="0.5" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#000000" />
  </ItemGroup>

  <ItemGroup Condition="!$(TargetFramework.Contains('android'))">
    <!-- App Icon -->
    <MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundScale="0.85" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#000000" />
  </ItemGroup>

  <ItemGroup>
    <AndroidResource Remove="Controls\NewFolder\**" />
    <Compile Remove="Controls\NewFolder\**" />
    <EmbeddedResource Remove="Controls\NewFolder\**" />
    <MauiCss Remove="Controls\NewFolder\**" />
    <MauiXaml Remove="Controls\NewFolder\**" />
    <None Remove="Controls\NewFolder\**" />
  </ItemGroup>

	<ItemGroup>
	  <None Remove="Resources\Fonts\OpenSans-Bold.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-BoldItalic.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-ExtraBold.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-ExtraBoldItalic.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-Italic.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-Light.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-LightItalic.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-Medium.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-MediumItalic.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-Regular.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-Semibold.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-SemiBoldItalic.ttf" />
	</ItemGroup>

  <!--https://github.com/BretJohnson/hot-preview-->
  <!--<ItemGroup Condition="$(Configuration) == 'Debug'">
    <PackageReference Include="HotPreview.App.Maui" Version="0.15.26" />
  </ItemGroup>-->

  <!--REQUIRED contact dev for this-->
  <Import Project="../../TriggeroProvisioning.targets"  />

  <ItemGroup>
    <!--<PackageReference Include="Plugin.Firebase.CloudMessaging" Version="3.1.2" />-->
    <PackageReference Include="AppoMobi.Maui.Popups" Version="1.0.0.1-pre" />
    <PackageReference Include="Plugin.Maui.Audio" Version="4.0.0" />

    <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.70" />
    <!--<PackageReference Include="CommunityToolkit.Maui" Version="12.0.0" />-->
    <PackageReference Include="HtmlAgilityPack" Version="1.12.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
    <PackageReference Include="Sharpnado.MaterialFrame.Maui" Version="2.0.0" />
    <PackageReference Include="Shiny.Hosting.Maui" Version="3.3.4" />
    <PackageReference Include="Shiny.Push" Version="3.3.4" />
	</ItemGroup>

  <!-- Firebase configuration for Android -->
  <ItemGroup>
    <GoogleServicesJson Include="Platforms\Android\google-services.json" />
  </ItemGroup>

  <!-- Firebase configuration for iOS -->
  <ItemGroup Condition="$(TargetFramework.Contains('ios'))">
    <BundleResource Include="Platforms\iOS\GoogleService-Info.plist" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Syncfusion.Maui.Buttons" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Calendar" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Carousel" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Charts" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Gauges" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Picker" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.ProgressBar" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Sliders" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Rotator" Version="30.1.37" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common\Triggero.Common.csproj" />
    <ProjectReference Include="..\Triggero.Web\MobileAPIWrapper\MobileAPIWrapper.csproj" />
    <ProjectReference Include="..\Triggero.Web\Triggero.ChatBot\Triggero.ChatBot.csproj" />
    <ProjectReference Include="..\Triggero.Web\Triggero.Models\Triggero.Models.csproj" />
    <ProjectReference Include="..\Triggero.Web\TriggeroWeb.Models\Triggero.Domain.csproj" />
    <ProjectReference Include="..\..\DrawnUi.Maui\src\Maui\DrawnUi\DrawnUi.Maui.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Platforms\Android\Navigation\" />
    <Folder Include="Resources\raw\Svg\" />
  </ItemGroup>

 
   

</Project>
