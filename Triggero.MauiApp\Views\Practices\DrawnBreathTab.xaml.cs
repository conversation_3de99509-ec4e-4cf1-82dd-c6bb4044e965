﻿using AppoMobi.Specials;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Views.Pages;
using Triggero.Models.Practices;


namespace Triggero.MauiMobileApp.Views.Drawn
{
    public partial class DrawnBreathTab
    {
        public ICommand StartPractice
        {
            get 
            { 
                return new Command((context) =>
                {
                    App.OpenPage(new BreathPracticePage(BreathPractice));
                });
            }
        }

        private BreathPractice _BreathPractice;

        public BreathPractice BreathPractice
        {
            get { return _BreathPractice; }
            set
            {
                if (_BreathPractice != value)
                {
                    _BreathPractice = value;
                    OnPropertyChanged();
                }
            }
        }


        public DrawnBreathTab()
        {
            InitializeComponent();

            Tasks.StartDelayed(TimeSpan.FromMilliseconds(500), () =>
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    BreathPractice = await ApplicationState.Data.GetRandomBreathPractice();

                    inhaleSecondsLabel.Text = string.Format(App.This.Interface.Library.BreathPractice.In_N_Seconds,
                        BreathPractice.InhaleSeconds);
                    delaySecondsLabel.Text = string.Format(App.This.Interface.Library.BreathPractice.In_N_Seconds,
                        BreathPractice.DelaySeconds);
                    exhaleSecondsLabel.Text = string.Format(App.This.Interface.Library.BreathPractice.In_N_Seconds,
                        BreathPractice.ExhaleSeconds);
                });
            });
        }
    }
}