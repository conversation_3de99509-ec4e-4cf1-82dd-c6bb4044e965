﻿
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Controls;
using Triggero.Models.Tests;
using Triggero.MauiMobileApp.Views.Pages.Tests;

using Triggero.MauiMobileApp.Extensions.Helpers;
using RelayCommand = Triggero.MauiMobileApp.Extensions.Helpers.RelayCommand;

namespace Triggero.MauiMobileApp.Views.Pages.Start
{

    public partial class StartTutorialPage : ContentPage
    {
        private Test _tutorialTest;
        public StartTutorialPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

        }

        protected override void OnAppearing()
        {
            PlatformUi.Instance.ApplyTheme();

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                _tutorialTest = await ApplicationState.Data.GetTutorialTest();


                var animatedImage = new GifCachedImage
                {
                    HeightRequest = 232,
                    WidthRequest = 253,
                    Opacity = 0,

                    VerticalOptions = LayoutOptions.Start,
                    HorizontalOptions = LayoutOptions.Center,
                    //Aspect = Aspect.AspectFit,
                    Source = "animationrunninggirl.gif"
                };
                animatedLayout.Children.Insert(0, animatedImage);

                animatedImage.Success += async (sender, e) =>
                {
                    await Task.Delay(500);
                    animatedImage.Opacity = 1;
                    notAnimatedImg.Opacity = 0;


                };

            });

        }

        private RelayCommand startTest;
        public RelayCommand StartTest
        {
            get => startTest ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new TutorialQuestionPage(_tutorialTest, 1));
            });
        }
    }
}