﻿using System.Collections.Generic;
using System.Threading.Tasks;
using MobileAPIWrapper;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Practices;



namespace Triggero.MauiMobileApp.ViewModels;

public class FavoritesViewModel : ElementsListViewModel
{
    public FavoritesViewModel()
    {
        Title = App.Instance.Interface.Library.Library.Favorite;
    }

    public override Color ThemeColor
    {
        get
        {
            return Color.FromHex("#FDCE72");
        }
    }

    public override Color ThemeColorB
    {
        get
        {
            return Color.FromHex("#FDCE72");
        }
    }

    protected override async Task<IEnumerable<IElementDetails>> LoadItemsAsync()
    {
        if (Connectivity.NetworkAccess == NetworkAccess.Internet)
        {
            switch (Section)
            {
            case FavoriteSectionType.Practices:
            var practices = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.GetUserFavoritePractices(AuthHelper.UserId);
            ApplicationState.UserFavorites.SetFavoritePractices(practices);
            return practices;

            case FavoriteSectionType.Topics:
            var topics = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.GetUserFavoriteTopics(AuthHelper.UserId);
            ApplicationState.UserFavorites.SetFavoriteTopics(topics);
            return topics;

            case FavoriteSectionType.Tests:
            var tests = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.GetUserFavoriteTests(AuthHelper.UserId);
            ApplicationState.UserFavorites.SetFavoriteTests(tests);
            return tests;

            case FavoriteSectionType.Exercises:
            default:
            var exercises = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.GetUserFavoriteExercises(AuthHelper.UserId);
            ApplicationState.UserFavorites.SetFavoriteExercises(exercises);
            return exercises;
            }
        }
        else
        {
            switch (Section)
            {
            case FavoriteSectionType.Practices:
            return ApplicationState.UserFavorites.GetFavoritePractices();

            case FavoriteSectionType.Topics:
            return ApplicationState.UserFavorites.GetFavoriteTopics();

            case FavoriteSectionType.Tests:
            return ApplicationState.UserFavorites.GetFavoriteTests();

            case FavoriteSectionType.Exercises:
            default:
            return ApplicationState.UserFavorites.GetFavoriteExercises();
            }


        }


        return null;
    }


    private FavoriteSectionType favoriteSectionType = FavoriteSectionType.Exercises;
    public FavoriteSectionType Section
    {
        get => favoriteSectionType;
        set
        {
            favoriteSectionType = value;
            OnPropertyChanged(nameof(FavoriteSectionType));
            LoadDataAsync().ConfigureAwait(false);
        }
    }

}