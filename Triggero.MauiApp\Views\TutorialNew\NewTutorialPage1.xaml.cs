﻿using System.Linq;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.General;


namespace Triggero.MauiMobileApp.Views
{

    public partial class NewTutorialPage1 : ContentPage
    {
        public NewTutorialPage1()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
            Load();
        }

        private async Task Load()
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                hiName.Text = string.Format(App.This.Interface.TutorialNew.TutorialNewPage1.HelloName, AuthHelper.User.Name);

                avatar.Source = App.GetFullImageUrl(AuthHelper.User.Avatar?.AvatarPath, ThumbnailSize.Small, ThumbnailType.Png);
                //avatar.Source = await ResorcesHelper.GetImageSource(AuthHelper.User.Avatar?.AvatarPath);
            });
        }

        private RelayCommand goNext;
        public RelayCommand GoNext
        {
            get => goNext ??= new RelayCommand(async obj =>
            {

                App.OpenPage(new NewTutorialPage2());


                MainThread.BeginInvokeOnMainThread(() =>
                {
                    if (App.Current.MainPage.Navigation.NavigationStack.Contains(this))
                    {
                        try
                        {
                            App.ClosePage(this);
                        }
                        catch { }
                    }


                });

            });
        }
    }
}