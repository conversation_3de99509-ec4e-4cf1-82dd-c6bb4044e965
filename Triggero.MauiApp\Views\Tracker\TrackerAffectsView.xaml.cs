﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Controls.Cards.Tracker;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.MoodTracker;
using Triggero.Models.MoodTracker.User;


namespace Triggero.Controls.MoodTracker
{
    public partial class TrackerAffectsView : ContentView
    {
        public TrackerAffectsView()
        {
            InitializeComponent();
        }


        public void BuildChart(List<MoodtrackerItem> items)
        {
            influenceProgressesLayout.Children.Clear();
            influenceGroupsLayout.Children.Clear();

            strongFactorDetails.Clear();
            middleFactorDetails.Clear();
            weakFactorDetails.Clear();

            influenceGroupsBlock.IsVisible = false;
            influenceProgressesBlock.IsVisible = false;

            if (items.Any())
            {
                RenderInfluenceProgresses(items);
                influenceProgressesBlock.IsVisible = true;

                RenderInfluenceGroups();
            }
        }

        private List<FactorDetail> strongFactorDetails = new List<FactorDetail>();
        private List<FactorDetail> middleFactorDetails = new List<FactorDetail>();
        private List<FactorDetail> weakFactorDetails = new List<FactorDetail>();

        private async void RenderInfluenceProgresses(List<MoodtrackerItem> items)
        {
            var influences = items.SelectMany(o => o.Influences);
            var influenceGroups = influences.GroupBy(o => o.FactorId).ToList();
            var factors = influences.Select(o => o.Factor);

            //Влияние. Сильное, среднее, слабое
            double biggestGroupCount = influenceGroups.Max(o => o.Count());
            double progressPartPercent = 100 / biggestGroupCount;

            double middleInfluenceTreshold = biggestGroupCount / 100 * 33;
            double strongInfluenceTreshold = biggestGroupCount / 100 * 66;


            foreach (var group in influenceGroups)
            {
                var factorDetails = new List<FactorDetail>();
                foreach (var detail in group.ToList().SelectMany(o => o.FactorDetails))
                {
                    if (!factorDetails.Any())
                    {
                        factorDetails.Add(detail);
                    }
                }


                double count = group.Count();
                double progressPercent = progressPartPercent * count;
                var factor = factors.FirstOrDefault(o => o.Id == group.Key);

                RenderInfluenceProgressBar(factor, progressPercent);

                if (count >= strongInfluenceTreshold)
                {
                    foreach (var item in factorDetails)
                    {
                        if (strongFactorDetails.Count > 4) break;
                        strongFactorDetails.Add(item);
                    }
                }
                else if (count >= middleInfluenceTreshold)
                {
                    foreach (var item in factorDetails)
                    {
                        if (middleFactorDetails.Count > 4) break;
                        middleFactorDetails.Add(item);
                    }
                }
                else
                {
                    foreach (var item in factorDetails)
                    {
                        if (weakFactorDetails.Count > 4) break;
                        weakFactorDetails.Add(item);
                    }
                }
            }


            //Render zero influences
            foreach (var factor in await ApplicationState.Data.GetFactors())
            {
                if (!influences.Any(o => o.FactorId == factor.Id))
                {
                    RenderInfluenceProgressBar(factor, 0);
                }
            }
        }

        private void RenderInfluenceProgressBar(Factor factor, double progress)
        {
            var card = new TrackerInfluenceProgressCard(factor, progress)
            {
                HeightRequest = 40,
                VerticalOptions = LayoutOptions.Start
            };
            influenceProgressesLayout.Children.Add(card);
        }


        private void RenderInfluenceGroups()
        {
            if (strongFactorDetails.Any())
            {
                influenceGroupsBlock.IsVisible = true;

                var groupCard = new TrackerInfluenceGroup();
                groupCard.ShowItems(InfluenceGroupSeverity.Strong, strongFactorDetails);
                influenceGroupsLayout.Children.Add(groupCard);
            }

            if (middleFactorDetails.Any())
            {
                influenceGroupsBlock.IsVisible = true;

                var groupCard = new TrackerInfluenceGroup();
                groupCard.ShowItems(InfluenceGroupSeverity.Strong, middleFactorDetails);
                influenceGroupsLayout.Children.Add(groupCard);
            }

            if (weakFactorDetails.Any())
            {
                influenceGroupsBlock.IsVisible = true;

                var groupCard = new TrackerInfluenceGroup();
                groupCard.ShowItems(InfluenceGroupSeverity.Strong, weakFactorDetails);
                influenceGroupsLayout.Children.Add(groupCard);
            }
        }
    }
}