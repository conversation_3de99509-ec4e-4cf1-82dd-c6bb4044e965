﻿
using System.Windows.Input;
using Syncfusion.Maui.Buttons;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.General.Settings;

namespace Triggero.MauiMobileApp.Views.Pages.Profile.Notifications
{

    public partial class NotificationsPage : ContentPage
    {


        public NotificationsPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            BindingContext = this;

            Load();
        }

        private bool initialized;

        private async void Load()
        {
            var user = await AuthHelper.GetUser();

            _settings = user.NotificationSettings.Clone();

            ShouldNotifyNewPosts = _settings.ShouldNotifyNewPosts;
            ShouldNotifyNewExercises = _settings.ShouldNotifyNewExercises;
            ShouldNotifyNewTests = _settings.ShouldNotifyNewTests;
            ShouldNotifyNewPractices = _settings.ShouldNotifyNewPractices;
            ShouldNotifyTrackerFilling = _settings.ShouldNotifyTrackerFilling;
            ShouldNotifyBreathTime = _settings.ShouldNotifyBreathTime;

            initialized = true;

            RefreshTime();
        }

        #region Settings

        NotificationSettings _settings;

        public bool ShouldNotifyTrackerFilling
        {
            get => shouldNotifyTrackerFilling;
            set
            {
                if (value == shouldNotifyTrackerFilling) return;
                shouldNotifyTrackerFilling = value;
                OnPropertyChanged();
                if (initialized)
                {
                    _settings.ShouldNotifyTrackerFilling = value;
                    AuthHelper.SaveNotificationSettings(_settings).ConfigureAwait(false);
                }
            }
        }

        public bool ShouldNotifyNewPosts
        {
            get => shouldNotifyNewPosts;
            set
            {
                if (value == shouldNotifyNewPosts) return;
                shouldNotifyNewPosts = value;
                OnPropertyChanged();
                if (initialized)
                {
                    _settings.ShouldNotifyNewPosts = value;
                    AuthHelper.SaveNotificationSettings(_settings).ConfigureAwait(false);
                }

            }
        }

        public bool ShouldNotifyNewExercises
        {
            get => shouldNotifyNewExercises;
            set
            {
                if (value == shouldNotifyNewExercises) return;
                shouldNotifyNewExercises = value;
                OnPropertyChanged();
                if (initialized)
                {
                    _settings.ShouldNotifyNewExercises = value;
                    AuthHelper.SaveNotificationSettings(_settings).ConfigureAwait(false);
                }
            }
        }

        public bool ShouldNotifyNewTests

        {
            get => shouldNotifyNewTests;
            set
            {
                if (value == shouldNotifyNewTests) return;
                shouldNotifyNewTests = value;
                OnPropertyChanged();
                if (initialized)
                {
                    _settings.ShouldNotifyNewTests = value;
                    AuthHelper.SaveNotificationSettings(_settings).ConfigureAwait(false);
                }

            }
        }

        public bool ShouldNotifyNewPractices
        {
            get => shouldNotifyNewPractices;
            set
            {
                if (value == shouldNotifyNewPractices) return;
                shouldNotifyNewPractices = value;
                OnPropertyChanged();
                if (initialized)
                {
                    _settings.ShouldNotifyNewPractices = value;
                    AuthHelper.SaveNotificationSettings(_settings).ConfigureAwait(false);
                }

            }
        }

        public bool ShouldNotifyBreathTime
        {
            get => shouldNotifyBreathTime;
            set
            {
                if (value == shouldNotifyBreathTime) return;
                shouldNotifyBreathTime = value;
                OnPropertyChanged();
                if (initialized)
                {
                    _settings.ShouldNotifyBreathTime = value;
                    AuthHelper.SaveNotificationSettings(_settings).ConfigureAwait(false);
                }

            }
        }

        #endregion

        private void RefreshTime()
        {
            LabelNotifyBreathTimeLabel.Text = _settings.NotifyBreathTime.ToString(@"hh\:mm");
            LabelNotifyTrackerFillingTimeLabel.Text = _settings.NotifyTrackerFillingTime.ToString(@"hh\:mm");
        }

        //private NotificationSettings settings;
        //public NotificationSettings Settings
        //{
        //    get { return settings; }
        //    set { settings = value; OnPropertyChanged(nameof(Settings)); }
        //}


        private ICommand saveAndClose;
        public ICommand SaveAndClose
        {
            get => saveAndClose ??= new RelayCommand(async obj =>
            {
                App.GoBack();
                //if (wereEdited)
                //{
                //    _settings.BaseUtcOffset = TimeZoneInfo.Local.BaseUtcOffset;
                //    await AuthHelper.SaveNotificationSettings(_settings);
                //}
                //await App.Current.MainPage.Navigation.PopAsync();
            });
        }

        #region SelectDate

        private string dateFieldName = "";

        private ICommand selectDate;
        private bool shouldNotifyNewPosts;
        private bool shouldNotifyNewExercises;
        private bool shouldNotifyNewTests;
        private bool shouldNotifyNewPractices;
        private bool shouldNotifyTrackerFilling;
        private bool shouldNotifyBreathTime;

        public ICommand SelectDate
        {
            get => selectDate ??= new RelayCommand(async obj =>
            {
                dateFieldName = obj as string;
                SetNotificationDatePage popup = null;

                switch (dateFieldName)
                {
                    case "NotifyTrackerFillingTime":
                    popup = new SetNotificationDatePage(_settings.NotifyTrackerFillingTime);
                    break;

                    case "NotifyBreathTime":
                    popup = new SetNotificationDatePage(_settings.NotifyBreathTime);
                    break;
                }

                popup.Selected += PopupDateSelected;
                App.OpenPage(popup);
            });
        }

        private void PopupDateSelected(object sender, TimeSpan e)
        {
            //wereEdited = true;

            switch (dateFieldName)
            {

                case "NotifyTrackerFillingTime":
                _settings.NotifyTrackerFillingTime = e;
                break;

                case "NotifyBreathTime":
                _settings.NotifyBreathTime = e;
                break;

            }

            RefreshTime();

            AuthHelper.SaveNotificationSettings(_settings).ConfigureAwait(false);
        }
        #endregion


        private void onTolggled(object? sender, SwitchStateChangedEventArgs switchStateChangedEventArgs)
        {
            //wereEdited = true;
        }
    }
}