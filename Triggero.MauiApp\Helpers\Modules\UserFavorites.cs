﻿using MobileAPIWrapper;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Practices;
using Triggero.Models.Tests;

namespace Triggero.MauiMobileApp.Extensions.Helpers.Modules
{
    public class UserFavorites
    {
        public static string FilePath = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + @"/userFavorites.json";

        public UserFavorites()
        {

        }

        List<Exercise> FavoriteExercises { get; set; } = new List<Exercise>();
        private List<Topic> FavoriteTopics { get; set; } = new List<Topic>();
        private List<Practice> FavoritePractices { get; set; } = new List<Practice>();
        private List<Test> FavoriteTests { get; set; } = new List<Test>();


        public async Task LoadFavorites()
        {
            if (Connectivity.NetworkAccess == NetworkAccess.Internet)
            {
                FavoriteExercises = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.GetUserFavoriteExercises(AuthHelper.UserId);
                FavoriteTopics = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.GetUserFavoriteTopics(AuthHelper.UserId);
                FavoritePractices = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.GetUserFavoritePractices(AuthHelper.UserId);
                FavoriteTests = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.GetUserFavoriteTests(AuthHelper.UserId);
            }
            else
            {
                if (File.Exists(FilePath))
                {
                    using (StreamReader file = File.OpenText(FilePath))
                    {
                        var json = file.ReadToEnd();
                        var obj = JsonConvert.DeserializeObject<UserFavorites>(json);

                        FavoriteExercises = obj.FavoriteExercises;
                        FavoriteTopics = obj.FavoriteTopics;
                        FavoritePractices = obj.FavoritePractices;
                        FavoriteTests = obj.FavoriteTests;
                    }
                }
            }
        }

        public async Task<bool> Set(int id, AppContentType content, bool value, object model = null)
        {
            try
            {
                bool result = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.SetFavorite(
                    AuthHelper.UserId,
                    content,
                    id,
                    value);

                switch (content)
                {
                    case AppContentType.Exercise:
                        if (result)
                        {
                            var item = model as Exercise;
                            if (item == null)
                            {
                                item = new Exercise() { Id = id };
                            }
                            if (FavoriteExercises.All(x => x.Id != id))
                            {
                                FavoriteExercises.Add(item);
                            }
                        }
                        else
                        {
                            var existing = FavoriteExercises.FirstOrDefault(o => o.Id == id);
                            if (existing != null)
                            {
                                FavoriteExercises.Remove(existing);
                            }
                        }
                        break;

                    case AppContentType.Topic:
                        if (result)
                        {
                            var item = model as Topic;
                            if (item == null)
                            {
                                item = new Topic() { Id = id };
                            }
                            if (FavoriteTopics.All(x => x.Id != id))
                            {
                                FavoriteTopics.Add(item);
                            }
                        }
                        else
                        {
                            var existing = FavoriteTopics.FirstOrDefault(o => o.Id == id);
                            if (existing != null)
                            {
                                FavoriteTopics.Remove(existing);
                            }
                        }
                        break;

                    case AppContentType.Practice:
                        if (result)
                        {
                            var item = model as Practice;
                            if (item == null)
                            {
                                item = new Practice() { Id = id };
                            }
                            if (FavoritePractices.All(x => x.Id != id))
                            {
                                FavoritePractices.Add(item);
                            }
                        }
                        else
                        {
                            var existing = FavoritePractices.FirstOrDefault(o => o.Id == id);
                            if (existing != null)
                            {
                                FavoritePractices.Remove(existing);
                            }
                        }
                        break;

                    case AppContentType.Test:
                        if (result)
                        {
                            var item = model as Test;
                            if (item == null)
                            {
                                item = new Test() { Id = id };
                            }
                            if (FavoriteTests.All(x => x.Id != id))
                            {
                                FavoriteTests.Add(item);
                            }
                        }
                        else
                        {
                            var existing = FavoriteTests.FirstOrDefault(o => o.Id == id);
                            if (existing != null)
                            {
                                FavoriteTests.Remove(existing);
                            }
                        }
                        break;
                }

                SaveChangesToMemory();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            return value;
        }

        //public async Task<bool> ToggleExercise(int id, Exercise model = null)
        //{
        //    bool result = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.ToggleFavoriteExercise(AuthHelper.UserId, id);

        //    if (model == null)
        //    {
        //        model = new Exercise() { Id = id };
        //    }

        //    if (result)
        //    {
        //        if (FavoriteExercises.All(x => x.Id != id))
        //        {
        //            FavoriteExercises.Add(model);
        //        }
        //        else
        //            FavoriteExercises.Remove(FavoriteExercises.FirstOrDefault(o => o.Id == id));
        //    }

        //    SaveChangesToMemory();
        //    return result;
        //}

        public async Task<bool> TogglePractice(int id, Practice model = null)
        {
            bool result = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.ToggleFavoritePractice(AuthHelper.UserId, id);

            if (model == null)
            {
                model = new Practice() { Id = id };
            }

            if (result) FavoritePractices.Add(model);
            else FavoritePractices.Remove(FavoritePractices.FirstOrDefault(o => o.Id == id));

            SaveChangesToMemory();
            return result;
        }

        public async Task<bool> ToggleTopic(int id, Topic model = null)
        {
            if (model == null)
            {
                model = new Topic() { Id = id };
            }

            bool result = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.ToggleFavoriteTopic(AuthHelper.UserId, id);

            if (result) FavoriteTopics.Add(model);
            else FavoriteTopics.Remove(FavoriteTopics.FirstOrDefault(o => o.Id == id));

            SaveChangesToMemory();
            return result;
        }

        public async Task<bool> ToggleTest(int id, Test model = null)
        {
            if (model == null)
            {
                model = new Test() { Id = id };
            }

            bool result = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.ToggleFavoriteTest(AuthHelper.UserId, id);

            if (result)
                FavoriteTests.Add(model);
            else
                FavoriteTests.Remove(FavoriteTests.FirstOrDefault(o => o.Id == id));

            SaveChangesToMemory();

            return result;
        }

        public bool HasFavoriteExercise(int id)
        {
            try
            {
                return FavoriteExercises.Any(o => o.Id == id);
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
                return false;
            }
        }

        public bool HasFavoritePractice(int id)
        {
            try
            {
                return FavoritePractices.Any(o => o.Id == id);
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
                return false;
            }
        }

        public bool HasFavoriteTopic(int id)
        {
            try
            {
                return FavoriteTopics.Any(o => o.Id == id);
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
                return false;
            }
        }

        public bool HasFavoriteTest(int id)
        {
            try
            {
                return FavoriteTests.Any(o => o.Id == id);
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
                return false;
            }
        }

        public void SetFavoriteExercises(List<Exercise> list)
        {
            FavoriteExercises = list;
        }

        public void SetFavoriteTopics(List<Topic> list)
        {
            FavoriteTopics = list;
        }

        public void SetFavoriteTests(List<Test> list)
        {
            FavoriteTests = list;
        }

        public void SetFavoritePractices(List<Practice> list)
        {
            FavoritePractices = list;
        }

        public List<Exercise> GetFavoriteExercises()
        {
            return FavoriteExercises.ToList();
        }

        public List<Topic> GetFavoriteTopics()
        {
            return FavoriteTopics.ToList();
        }

        public List<Practice> GetFavoritePractices()
        {
            return FavoritePractices.ToList();
        }

        public List<Test> GetFavoriteTests()
        {
            return FavoriteTests.ToList();
        }


        public void SaveChangesToMemory()
        {
            try
            {
                var json = JsonConvert.SerializeObject(this);
                File.WriteAllText(FilePath, json);
            }
            catch { }
        }

    }
}
