﻿<?xml version="1.0" encoding="UTF-8" ?>
<draw:SkiaLayout
    x:Class="Triggero.Controls.Cards.TasksForToday.CellTaskForToday"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:tasksForToday="clr-namespace:Triggero.Controls.Cards.TasksForToday;assembly=Triggero.MauiMobileApp"
    xmlns:tasksForToday1="clr-namespace:Triggero.MauiMobileApp.Controls.Cards.TasksForToday;assembly=Triggero.MauiMobileApp"
    xmlns:viewModels="clr-namespace:Triggero.MauiMobileApp.ViewModels"
    xmlns:tasksForToday2="clr-namespace:Triggero.MauiMobileApp.Controls.Cards.TasksForToday"
    x:Name="this"
    x:DataType="viewModels:TaskForTodayViewModel"
    ColumnSpacing="0"
    HorizontalOptions="Fill"
    VerticalOptions="Start"
    Type="Grid"
    RowDefinitions="Auto"
    UseCache="Image">

    <draw:SkiaLayout.ColumnDefinitions>
        <ColumnDefinition Width="54" />
        <ColumnDefinition Width="50" />
        <ColumnDefinition Width="*" />
        <ColumnDefinition Width="50" />
    </draw:SkiaLayout.ColumnDefinitions>

    <!--  CHECK  -->
    <tasksForToday2:DrawnCheckBox
        HeightRequest="49"
        VerticalOptions="Center"
        CommandToggled="{Binding CommandToggleCheck}"
        x:Name="Checked" />

    <!--  BANNER  -->
    <draw:SkiaShape
        Tapped="SkiaControl_OnTapped"
        Grid.Column="1"
        Padding="0"
        draw:AddGestures.CommandTapped="{Binding CommandOnTapped}"
        BackgroundColor="#20BBD5EA"
        CornerRadius="12"
        HeightRequest="49"
        HorizontalOptions="Center"
        IsClippedToBounds="True"
        VerticalOptions="Center"
        WidthRequest="49">

 

        <draw:SkiaImage
            Margin="2"
            Aspect="AspectCover"
            HorizontalOptions="Fill"
            Source="{Binding Image}"
            Tag="Problem"
            VerticalOptions="Fill" />

        <!--</draw:SkiaLayout>-->

    </draw:SkiaShape>


    <!--  TITLE + SUBTITLE  -->

    <draw:SkiaLayout
        Grid.Column="2"
        Margin="16,0,0,0"
        draw:AddGestures.CommandTapped="{Binding CommandOnTapped}"
        HorizontalOptions="Fill"
        Type="Column"
        UseCache="Operations"
        VerticalOptions="Center">

        <draw:SkiaLabel
            FontFamily="FontTextSemiBold"
            FontSize="13"
            MaxLines="3"
            Text="{Binding Title}"
            TextColor="{x:StaticResource ColorText}" />

        <draw:SkiaLabel
            FontSize="10"
            IsVisible="{Binding SubTitle, Converter={x:StaticResource IsNotNullOrEmptyConverter}}"
            MaxLines="1"
            TextColor="{x:StaticResource ColorTextGray}">
            <draw:SkiaLabel.Spans>

                <draw:TextSpan Text="{Binding SubTitle}" />

                <draw:TextSpan Text=" мин" />

            </draw:SkiaLabel.Spans>
        </draw:SkiaLabel>

    </draw:SkiaLayout>

    <draw:SkiaLayout
        Grid.Column="3"
        draw:AddGestures.CommandTapped="{Binding CommandOnTapped}"
        HorizontalOptions="Fill"
        IsVisible="{Binding HasLink}"
        VerticalOptions="Fill">

        <draw:SkiaImage
            HeightRequest="12"
            HorizontalOptions="Center"
            Source="arrowforwardlightblue.png"
            VerticalOptions="Center"
            WidthRequest="6" />

    </draw:SkiaLayout>



</draw:SkiaLayout>