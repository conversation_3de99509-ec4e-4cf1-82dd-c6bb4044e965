﻿using System.Collections.Generic;
using System.Linq;
using Triggero.Models.Localization;

namespace Triggero.MauiMobileApp.Extensions.Helpers
{
    public static class LanguageHelper
    {
        public static string LangCode { get; set; } = "RU";

        public static string GetLocalizedTitle(this IEnumerable<LocalizationWithTitle> list, string code, string fallback)
        {
            var found = list.FirstOrDefault(o => o.Language.Code.ToLower() == code.ToLower());
            if (found != null)
                return found.Title;
            return fallback;
        }

        public static string GetLocalizedDescription(this IEnumerable<LocalizationWithTitle> list, string code, string fallback)
        {
            var found = list.FirstOrDefault(o => o.Language.Code.ToLower() == code.ToLower());
            if (found != null)
                return found.Description;
            return fallback;
        }

    }
}
