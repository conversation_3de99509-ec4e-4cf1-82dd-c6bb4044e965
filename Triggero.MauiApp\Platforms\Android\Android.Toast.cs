﻿using Android.App;
using Android.Views;
using Android.Widget;


using AppoMobi.Mobile.Import.Enums;
using Application = Android.App.Application;
using Toast = Android.Widget.Toast;

namespace AppoMobi.Mobile
{
    public partial class Toast : IToastMessage
    {
        public void ShortAlert(string message)
        {
            var mess = Android.Widget.Toast.MakeText(Application.Context, message, ToastLength.Short);
            mess.SetGravity(GravityFlags.Center | GravityFlags.CenterHorizontal, 0, 0);
            //mess.SetGravity(GravityFlags.Bottom | GravityFlags.CenterHorizontal, 0, 0);
            mess.Show();
        }

        public void LongAlert(string message, ToastPosition position)

        {
            var gravity = GravityFlags.Bottom;
            switch (position)
            {
            case ToastPosition.Top:
            gravity = GravityFlags.Top;
            break;
            case ToastPosition.Center:
            gravity = GravityFlags.CenterVertical;
            break;
            case ToastPosition.Bottom:
            gravity = GravityFlags.Bottom;
            break;
            }
            var mess = Android.Widget.Toast.MakeText(Application.Context, message, ToastLength.Long);
            mess.SetGravity(gravity | GravityFlags.CenterHorizontal, 0, 0);
            mess.SetMargin(0, 0.1f);

            mess.Show();
        }
    }
}