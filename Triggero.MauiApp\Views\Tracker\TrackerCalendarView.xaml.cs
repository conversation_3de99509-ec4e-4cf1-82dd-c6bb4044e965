﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions;
using Triggero.MauiMobileApp.Views.Pages.MoodTracker;
using Triggero.Models.MoodTracker.User;
using Syncfusion.Maui.Calendar;

namespace Triggero.MauiMobileApp.Views.MoodTracker
{
    // Converter to get mood image based on date
    public class DateToMoodImageConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is DateTime date && TrackerCalendarView.CurrentInstance != null)
            {
                var dayItems = TrackerCalendarView.CurrentInstance.monthItems.Where(o => o.Date.Date == date.Date).ToList();

                if (!dayItems.Any())
                    return null;

                double avg = dayItems.Average(o => o.Mood);
                int mood = (int)Math.Round(avg, 0);

                switch (mood)
                {
                    case 5: return "daymood1.png";
                    case 4: return "daymood2.png";
                    case 3: return "daymood3.png";
                    case 2: return "daymood4.png";
                    case 1: return "daymood5.png";
                    case 0: return "daymood6.png";
                    default: return null;
                }
            }
            return null;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // Converter to determine border color based on date (today gets special color)
    public class DateToBorderColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is DateTime date)
            {
                return date.Date == DateTime.Now.Date ? Color.FromHex("#10ABB8") : Colors.Transparent;
            }
            return Colors.Transparent;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public partial class TrackerCalendarView : ContentView, INotifyPropertyChanged
    {
        public List<MoodtrackerItem> monthItems = new();
        public static TrackerCalendarView CurrentInstance { get; private set; }

        public TrackerCalendarView()
        {
            InitializeComponent();
            CurrentInstance = this;

            // TODO: FirstDayOfWeek is now in MonthView, set via XAML or code
            calendar.MonthView.FirstDayOfWeek = DayOfWeek.Monday;

            calendar.SelectedDate = null;
        }

        public List<CustomDayView> RecycledCells = new();

        private bool _IsBusy;
        public bool IsBusy
        {
            get
            {
                return _IsBusy;
            }
            set
            {
                if (_IsBusy != value)
                {
                    _IsBusy = value;
                    OnPropertyChanged();

                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        if (value)
                        {
                            calendar.Opacity = 0.5;
                        }
                        else
                        {
                            calendar.Opacity = 1.0;
                        }
                    });

                }
            }
        }


        #region Render

        public void SetMonthItems(int year, int month)
        {
            Task.Run(async () =>
            {
                try
                {
                    IsBusy = true;
                    await Task.Delay(10);

                    var monthStart = new DateTime(year, month, 1);
                    var monthEnd = new DateTime(year, month, 1).AddMonths(1).AddDays(-1);
                    var loaded = await ApplicationState.Data.GetMoodtrackerItemsAtPeriod(monthStart, monthEnd);

                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        monthItems = loaded;

                        calendar.DisplayDate = monthStart; // MoveToDate becomes DisplayDate in MAUI
                        monthDateLabel.Text = monthStart.ToString("MMMM yyyy").Capitilize();
                        calendar.SelectedDate = null;

                        IsBusy = false;
                    });

                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    IsBusy = false;
                }
            });

        }

        // TODO: OnMonthCellLoaded event is not available in MAUI Calendar
        // Need to use CellTemplate in XAML or handle ViewChanged event
        // This functionality needs to be reimplemented using MAUI Calendar's CellTemplate
        /*
        private async void Calendar_OnMonthCellLoaded(object sender, Syncfusion.SfCalendar.XForms.MonthCellLoadedEventArgs e)
        {
            var dayItems = monthItems.Where(o => o.Date.Date == e.Date.Date).ToList();

            var cell = new CustomDayView();

            int? mood = null;

            if (dayItems.Any())
            {
                double avg = dayItems.Average(o => o.Mood);
                mood = (int)Math.Round(avg, 0);
            }

            cell.SetContext(e.Date, mood);

            e.View = cell;

        }
        */
        #endregion


        #region Selections

        private void selectionChanged(object sender, CalendarSelectionChangedEventArgs e)
        {
            if (e.NewValue != null && e.NewValue is DateTime selectedDate)
            {
                var found = monthItems.Where(o => o.Date.Date == selectedDate.Date).ToList();
                if (found.Any())
                {
                    App.OpenPage(new TrackerDay(selectedDate.Date, found));
                }
                this.calendar.SelectedDate = null;
            }
        }

        private async void monthChanged(object sender, CalendarViewChangedEventArgs e)
        {
            // In MAUI, ViewChanged event provides NewVisibleDates instead of CurrentValue
            if (e.NewVisibleDates?.Count > 0)
            {
                var firstDate = e.NewVisibleDates[0];
                SetMonthItems(firstDate.Year, firstDate.Month);
            }
        }
        private async void goForwardMonth(object sender, EventArgs e)
        {
            if (IsBusy)
                return;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var date = calendar.DisplayDate.AddMonths(1); // MoveToDate becomes DisplayDate in MAUI
                SetMonthItems(date.Year, date.Month);
            });

        }

        private async void goBackMonth(object sender, EventArgs e)
        {
            if (IsBusy)
                return;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var date = calendar.DisplayDate.AddMonths(-1); // MoveToDate becomes DisplayDate in MAUI
                SetMonthItems(date.Year, date.Month);
            });


        }

        #endregion

        #region INotifyPropertyChanged
        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion

    }
}