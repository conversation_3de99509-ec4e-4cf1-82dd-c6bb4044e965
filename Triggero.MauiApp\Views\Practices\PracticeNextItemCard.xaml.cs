﻿using Triggero.MauiMobileApp.Extensions;
using Triggero.Models.Practices;
using Triggero.MauiMobileApp.Views.Pages.Library;

using Triggero.MauiMobileApp.Extensions.Helpers;

namespace Triggero.Controls.Cards.NextItemCard
{

    public partial class PracticeNextItemCard : ContentView
    {
        private Practice _practice;

        public PracticeNextItemCard()
        {
            InitializeComponent();
        }
        public PracticeNextItemCard(Practice practice)
        {
            InitializeComponent();
            Build(practice);
        }
        public async void Build(Practice practice)
        {
            _practice = practice;

            titleLabel.Text = _practice.GetLocalizedTitle(LanguageHelper.LangCode);
            var desc = _practice.GetLocalizedDescription(LanguageHelper.LangCode);
            desc = StringExtensions.ExtractText(desc);
            if (desc.Length > 27 * 2)
            {
                desc = desc.Substring(0, 27 * 2) + "...";
            }
            descLabel.Text = desc;

            img.Source = await ResorcesHelper.GetImageSource(_practice.ImgPath);

        }

        private async void tapped(object sender, EventArgs e)
        {
            if (_practice != null)
            {
                App.OpenPage(new PracticePage(_practice));
            }
        }
    }
}