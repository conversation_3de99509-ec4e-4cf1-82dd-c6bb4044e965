﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;




namespace Triggero.Custom
{
    
    public partial class ScrollableCalendarV2DayButton : ContentView
    {
        public DateTime Day { get; private set; }
        public ScrollableCalendarV2DayButton(DateTime day)
        {
            Day = day;
            InitializeComponent();

            dayLabel.Text = day.Day.ToString();
        }



        public void SetButtonSelection(bool selected)
        {
            if (selected)
            {
                //btnFrame.Background = Color.FromHex("#FDCE72");
                btnFrame.BackgroundColor = Color.FromHex("#FDCE72");
                btnFrame.CornerRadius = 18;
            }
            else
            {
                //btnFrame.Background = Colors.Transparent;
                btnFrame.BackgroundColor = Colors.Transparent;
                btnFrame.CornerRadius = 0;
            }
        }




        public event EventHandler<DateTime> Tapped;
        private void onTapped(object sender, EventArgs e)
        {
            Tapped?.Invoke(this, Day);
        }
    }
}