﻿using System.Windows.Input;
using Triggero.MauiMobileApp.Enums;
using Triggero.Models;
using Triggero.Models.Practices;
using Triggero.Models.Tests;
using Triggero.MauiMobileApp.Views.Pages.Library;
using Triggero.MauiMobileApp.Views.Pages.Tests;

using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Extensions.Helpers.Modules;

namespace Triggero.MauiMobileApp.ViewModels;

public class TaskForTodayViewModel : BaseViewModel
{
    public TaskForToday Model { get; }

    public TaskForTodayViewModel(TaskForToday model)
    {
        Model = model;
    }


    public void Initialize()
    {
        var model = Model.GetTaskModel();

        if (model is Test test)
        {
            Model.Title = test.Title;
            Model.ImgPath = test.ImgPath;
        }
        else
        if (model is Practice practice)
        {
            Model.Title = practice.Title;
            Model.ImgPath = practice.ImgPath;
        }
        else
        if (model is Exercise exercise)
        {
            Model.Title = exercise.Title;
            Model.ImgPath = exercise.ImgPath;

        }
        else
        if (model is Topic topic)
        {
            Model.Title = topic.Title;
            Model.ImgPath = topic.ImgPath;
        }

        Title = Model.Title;

        Image = App.GetFullImageUrl(Model.ImgPath, ThumbnailSize.Small, ThumbnailType.Png);

        if (Model.PassingTimeInMinutes > 0)
        {
            SubTitle = Model.PassingTimeInMinutes.ToString();
        }

        switch (Model.GetTaskType())
        {
        case RecomendationType.Test:
        HasLink = true;
        break;

        case RecomendationType.Exercise:
        HasLink = true;
        break;

        case RecomendationType.Practice:
        HasLink = true;
        break;

        case RecomendationType.Topic:
        HasLink = true;
        break;

        case RecomendationType.Unknown:
        HasLink = false;
        break;
        }
    }

    public ICommand CommandToggleCheck => new Command((ctx) =>
    {
        Model.IsCompleted = !Model.IsCompleted;

        CheckedChanged?.Invoke(this, Model);

        ApplicationState.SaveToFile(ApplicationState.TodayData, TodayData.FilePath);
    });

    public ICommand CommandOnTapped => new Command((ctx) =>
    {
        Tapped?.Invoke(this, Model);

        switch (Model.GetTaskType())
        {
        case RecomendationType.Test:
        App.OpenPage(new TestPage(Model.Test));
        break;

        case RecomendationType.Practice:
        App.OpenPage(new PracticePage(Model.Practice));
        break;

        case RecomendationType.Exercise:
        App.OpenPage(new ExercisePage(Model.Exercise));
        break;

        case RecomendationType.Topic:
        App.OpenPage(new TopicPage(Model.Topic));
        break;
        }
    });

    public event EventHandler<TaskForToday> CheckedChanged;

    public event EventHandler<TaskForToday> Tapped;

    private string _Title;
    public string Title
    {
        get
        {
            return _Title;
        }
        set
        {
            if (_Title != value)
            {
                _Title = value;
                OnPropertyChanged();
            }
        }
    }

    private string _SubTitle;
    public string SubTitle
    {
        get
        {
            return _SubTitle;
        }
        set
        {
            if (_SubTitle != value)
            {
                _SubTitle = value;
                OnPropertyChanged();
            }
        }
    }

    private string _Image;
    public string Image
    {
        get
        {
            return _Image;
        }
        set
        {
            if (_Image != value)
            {
                _Image = value;
                OnPropertyChanged();
            }
        }
    }


    private bool _HasLink;
    public bool HasLink
    {
        get
        {
            return _HasLink;
        }
        set
        {
            if (_HasLink != value)
            {
                _HasLink = value;
                OnPropertyChanged();
            }
        }
    }

}