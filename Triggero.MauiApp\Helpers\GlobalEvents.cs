﻿using Triggero.Models.General;
using Triggero.Models.MoodTracker.User;

namespace Triggero.MauiMobileApp.Extensions.Helpers
{
    public static class GlobalEvents
    {
        public static event EventHandler TodayDataChanged;
        public static void OnTodayDataChanged()
        {
            TodayDataChanged?.Invoke(null, EventArgs.Empty);
        }


        public static event EventHandler<User> UserPropertyChanged;
        public static void OnUserPropertyChanged(User user)
        {
            UserPropertyChanged?.Invoke(null, user);
        }

        public static event EventHandler<MoodtrackerNote> NoteChanged;
        public static void OnNoteChanged(MoodtrackerNote note)
        {
            NoteChanged?.Invoke(null, note);
        }
    }
}
