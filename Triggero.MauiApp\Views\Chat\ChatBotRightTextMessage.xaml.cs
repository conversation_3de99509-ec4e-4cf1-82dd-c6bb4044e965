﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models.Messengers.ChatBot;
using Triggero.Models.Messengers.Support;



namespace Triggero.Controls.Chat
{
    
    public partial class ChatBotRightTextMessage : ContentView
    {
        public ChatBotRightTextMessage(ChatBotMessage msg)
        {
            InitializeComponent();
            Message = msg;
            timeLabel.Text = msg.SentAt.ToString("HH:mm");


            //double suitableWidth = 0;

            //var words = msg.Text.Split(' ');
            //for(int i=0;i < words.Length; i++)
            //{
            //    var str = string.Join(" ", words.Take(i + 1));



            //    double width = 20 + 69 + ((double)str.Length) * 9.65;
            //    if (width < 360)
            //    {
            //        suitableWidth = width;
            //    }
            //    else
            //    {
            //        this.WidthRequest = suitableWidth;
            //        break;
            //    }

            //    if(i+1 == words.Length)
            //    {
            //        this.WidthRequest = suitableWidth;
            //        break;
            //    }
             
            //}
        }
        private ChatBotMessage message;
        public ChatBotMessage Message
        {
            get { return message; }
            set { message = value; OnPropertyChanged(nameof(Message)); }
        }
    }
}