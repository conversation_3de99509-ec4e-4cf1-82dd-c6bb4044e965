﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;




namespace Triggero.Custom
{
	
	public partial class DotsView : ContentView
	{

		public DotsView()
		{
			InitializeComponent();
		}
		public Color DefaultColor { get; set; } = Color.FromHex("#EEF5FB");
		public double DefaultHeightRequest { get; set; } = 8;
        public double DefaultWidthRequest { get; set; } = 8;
        public float DefaultCornerRadius { get; set; } = 4;


        public Color SelectedColor { get; set; } = Color.FromHex("#10ABB8");
        public double SelectedHeightRequest { get; set; } = 8;
        public double SelectedWidthRequest { get; set; } = 8;
        public float SelectedCornerRadius { get; set; } = 4;




        public bool HandleDotTapping { get; set; }
        public void SetDots(int count,int index = 0)
        {
            layout.Children.Clear();

            for (int i=0;i<count;i++)
			{
				var dot = new BoxView()
				{
					BackgroundColor = DefaultColor,
					WidthRequest = DefaultWidthRequest,
					HeightRequest = DefaultHeightRequest,
					CornerRadius = DefaultCornerRadius,
					HorizontalOptions = LayoutOptions.Start,
					VerticalOptions = LayoutOptions.Center,
				};

				var tapRecognizer = new TapGestureRecognizer();
                tapRecognizer.Tapped += TapRecognizer_Tapped;
				dot.GestureRecognizers.Add(tapRecognizer);

				if (i == index)
				{
					dot.BackgroundColor = SelectedColor;
					dot.WidthRequest = SelectedWidthRequest;
					dot.HeightRequest = SelectedHeightRequest;
					dot.CornerRadius = SelectedCornerRadius;
                }
				layout.Children.Add(dot);
			}
		}


        public event EventHandler<int> DotTapped;
        private void TapRecognizer_Tapped(object sender, EventArgs e)
        {
			if (HandleDotTapping)
			{
                int index = layout.Children.IndexOf(sender as View);
                SetIndex(index);
                DotTapped?.Invoke(sender, index);
            }		
        }

        public void SetIndex(int index)
		{
			for(int i=0;i< layout.Children.Count; i++)
			{
				var dot = layout.Children[i] as BoxView;
				if(i == index)
				{
                    dot.BackgroundColor = SelectedColor;
                    dot.WidthRequest = SelectedWidthRequest;
                    dot.HeightRequest = SelectedHeightRequest;
                    dot.CornerRadius = SelectedCornerRadius;
                }
				else
				{
                    dot.BackgroundColor = DefaultColor;
                    dot.WidthRequest = DefaultWidthRequest;
                    dot.HeightRequest = DefaultHeightRequest;
                    dot.CornerRadius = DefaultCornerRadius;
                }
			}
		}

	

    }
}