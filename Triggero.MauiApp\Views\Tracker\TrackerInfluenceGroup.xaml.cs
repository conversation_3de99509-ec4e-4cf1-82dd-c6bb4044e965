﻿using System.Collections.Generic;
using Triggero.Controls.Cards.Tracker.Influence;
using Triggero.MauiMobileApp.Enums;
using Triggero.Models.MoodTracker;


namespace Triggero.Controls.Cards.Tracker
{

    public partial class TrackerInfluenceGroup : ContentView
    {
        public TrackerInfluenceGroup()
        {
            InitializeComponent();
        }

        public void ShowItems(InfluenceGroupSeverity severity, List<FactorDetail> details)
        {
            switch (severity)
            {
            case InfluenceGroupSeverity.Strong:
            infuenceGroupLabel.Text = App.This.Interface.MoodTracker.TrackerMainPage.MoodAffectsStrongAffect;
            break;
            case InfluenceGroupSeverity.Middle:
            infuenceGroupLabel.Text = App.This.Interface.MoodTracker.TrackerMainPage.MoodAffectsMiddleAffect;
            break;
            case InfluenceGroupSeverity.Weak:
            infuenceGroupLabel.Text = App.This.Interface.MoodTracker.TrackerMainPage.MoodAffectsWeakAffect;
            break;
            }

            itemsLayout.Children.Clear();
            foreach (var detail in details)
            {
                var card = new InfluenceCardItem(detail)
                {
                    Margin = new Thickness(8, 8, 0, 0),
                    HeightRequest = 32,
                    VerticalOptions = LayoutOptions.Start
                };
                itemsLayout.Children.Add(card);
            }
        }
    }
}