﻿<?xml version="1.0" encoding="UTF-8"?>

<drawn:RecommendationDrawnBase
    x:Class="Triggero.MauiMobileApp.Views.Drawn.TopicRecommendationDrawn"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:drawn="clr-namespace:Triggero.MauiMobileApp.Views.Drawn"
    xmlns:models1="clr-namespace:Triggero.Models"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="this"
    x:DataType="models1:RecommendationModel"
    ColumnDefinitions="70, *, 20"
    HeightRequest="70"
    ColumnSpacing="8"
    HorizontalOptions="Fill"
    Type="Grid"
    UseCache="Image">

    <!--  banner  -->
    <draw:SkiaShape
        Padding="0"
        Background="#CDE9FF"
        CornerRadius="12"
        HeightRequest="70"
        HorizontalOptions="Center"
        IsClippedToBounds="True"
        VerticalOptions="Center"
        WidthRequest="70">

        <draw:SkiaImage
            x:Name="img"
            Aspect="AspectCover"
            HorizontalOptions="Fill"
            VerticalOptions="Fill" />

    </draw:SkiaShape>


    <draw:SkiaLayout
        UseCache="Operations"
        Grid.Column="1"
        Margin="5,0,0,0"
        Type="Column"
        BackgroundColor="White"
        VerticalOptions="Center">

        <!--title-->
        <draw:SkiaLabel
            x:Name="titleLabel"
            FontFamily="FontTextSemiBold"
            FontSize="13"
            TextColor="{x:StaticResource ColorText}" />

        <!--time-->
        <draw:SkiaLabel
            FontSize="10"
            Opacity="0.5"
            TextColor="{x:StaticResource ColorText}">

            <draw:SkiaLabel.Spans>

                <draw:TextSpan
                    x:Name="minutesSpan"
                    Text="" />

                <draw:TextSpan Text=" мин" />

            </draw:SkiaLabel.Spans>

        </draw:SkiaLabel>

    </draw:SkiaLayout>

    <draw:SkiaImage
        Grid.Column="2"
        HeightRequest="12"
        HorizontalOptions="Center"
        Source="arrowforwardlightblue.png"
        VerticalOptions="Center"
        WidthRequest="6" />


</drawn:RecommendationDrawnBase>