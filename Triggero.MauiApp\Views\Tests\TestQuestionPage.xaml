﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Tests.TestQuestionPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this"
    BackgroundColor="White">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition Height="115" />
                <RowDefinition Height="*" />
                <RowDefinition Height="120" />
            </Grid.RowDefinitions>

            <Image
                Grid.RowSpan="4"
                Aspect="Fill"
                HorizontalOptions="Fill"
                Source="lightbluegradientbg.png"
                VerticalOptions="Fill" />

            <!--PROGRESS-->
            <Grid Grid.Row="1">

                <StackLayout
                    x:Name="progressStackLayout"
                    Margin="20,0,20,0"
                    HorizontalOptions="Fill"
                    Spacing="0"
                    SizeChanged="ProgressStackLayout_OnSizeChanged"
                    VerticalOptions="Center">

                    <Grid
                        HeightRequest="40"
                        VerticalOptions="Start">

                        <Border
                            x:Name="totalProgressFrame"
                            Margin="0,12,0,0"
                            Padding="0"
                            Background="{x:StaticResource ColorPrimaryLight}"
                            HeightRequest="4"
                            HorizontalOptions="Fill"
                            StrokeShape="RoundRectangle 2" 
                            VerticalOptions="Center" />

                        <Border
                            x:Name="progressFrame"
                            Margin="0,12,0,0"
                            Padding="0"
                            Background="{x:StaticResource ColorAccent}"
                            HeightRequest="4"
                            StrokeShape="RoundRectangle 2" 
                            HorizontalOptions="Start"
                            VerticalOptions="Center" />

                        <StackLayout
                            x:Name="thumbStackLayout"
                            Margin="0,0,0,0"
                            HorizontalOptions="Start"
                            IsVisible="False"
                            Spacing="1"
                            VerticalOptions="Start"
                            WidthRequest="40">

                            <Label
                                x:Name="percentLabel"
                                FontSize="14"
                                HorizontalOptions="Center"
                                HorizontalTextAlignment="Center"
                                Text="20%"
                                TextColor="Black"
                                VerticalOptions="Start" />
                            
                            <BoxView
                                TranslationY="-2"
                                x:Name="progressThumb"
                                BackgroundColor="{x:StaticResource ColorAccent}"
                                CornerRadius="7"
                                HeightRequest="14"
                                HorizontalOptions="Center"
                                VerticalOptions="Start"
                                WidthRequest="14" />
                            
                        </StackLayout>




                    </Grid>

                    <!--<xforms:SfRangeSlider
                        Margin="-14,0,-14,0"
                        HorizontalOptions="FillAndExpand"
                        x:Name="rangeSlider"
                        HeightRequest="15"
                        VerticalOptions="Start"
                        Minimum="0"
                        Maximum="100"
                        Value="50"
                        StepFrequency="0.1"
                        ShowRange="False"
                        ThumbBorderColor="White"
                        TickPlacement="None"
                        TrackSelectionColor="#FDCE72"
                        TrackSelectionThickness="2"
                        TrackThickness="2"
                        KnobColor="#FDCE72"
                        TrackColor="#CEE3F4"/>-->
                </StackLayout>
            </Grid>


            <ScrollView
                Grid.Row="2"
                Margin="20,0,20,0"
                VerticalScrollBarVisibility="Never">
                <StackLayout>

                    <StackLayout
                        x:Name="testQuestionViewLayout"
                        Margin="0,25,0,0"
                        MinimumHeightRequest="300">

                    </StackLayout>

                </StackLayout>
            </ScrollView>

            <Grid Grid.Row="3">
                <Grid
                    Margin="20,0,20,0"
                    ColumnSpacing="16"
                    HeightRequest="56">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="56" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <ImageButton
                        Grid.Column="0"
                        BackgroundColor="Transparent"
                        Command="{Binding Source={x:Reference this}, Path=GoBack}"
                        CornerRadius="0"
                        HeightRequest="56"
                        HorizontalOptions="Center"
                        Source="buttonbackbordered.png"
                        VerticalOptions="Center"
                        WidthRequest="56" />


                    <Button
                        x:Name="goNextBtn"
                        Grid.Column="1"
                        Command="{Binding Source={x:Reference this}, Path=GoNext}"
                        Style="{x:StaticResource yellow_btn}"
                        Text="Далее 3/15"
                        VerticalOptions="Center" />

                </Grid>
            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>