﻿using Triggero.Models.MoodTracker.User;


namespace Triggero.Controls.Templates
{

    public partial class TrackerDayItemCard : ContentView
    {
        private MoodtrackerItem _item;
        public TrackerDayItemCard(MoodtrackerItem item)
        {
            _item = item;
            InitializeComponent();
            Load();
        }
        private void Load()
        {
            switch (_item.Mood)
            {
            case 5:
            moodImg.Source = ImageSource.FromFile("daymood1.png");
            moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood5;
            break;
            case 4:
            moodImg.Source = ImageSource.FromFile("daymood2.png");
            moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood4;
            break;
            case 3:
            moodImg.Source = ImageSource.FromFile("daymood3.png");
            moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood3;
            break;
            case 2:
            moodImg.Source = ImageSource.FromFile("daymood4.png");
            moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood2;
            break;
            case 1:
            moodImg.Source = ImageSource.FromFile("daymood5.png");
            moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood1;
            break;
            case 0:
            moodImg.Source = ImageSource.FromFile("daymood6.png");
            moodTitleLabel.Text = App.This.Interface.MoodTracker.TrackerGeneral.Mood0;
            break;
            }


            dateLabel.Text = _item.Date.ToString("dddd, dd MMM");
            timeLabel.Text = _item.Date.ToString("HH:mm");
        }


        public event EventHandler<MoodtrackerItem> Tapped;
        private void onTapped(object sender, EventArgs e)
        {
            Tapped?.Invoke(this, _item);
        }
    }
}