﻿<?xml version="1.0" encoding="UTF-8"?>

<RadioButton xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"

             HorizontalOptions="Center"
             VerticalOptions="Center"
             x:Class="Triggero.MauiMobileApp.Controls.Other.ExerciseEmojiRadioButton">
    <RadioButton.ControlTemplate>
        <ControlTemplate>

            <Frame
                BackgroundColor="Transparent"
                HasShadow="False"
                CornerRadius="12"
                Padding="0">

                <Frame.Style>
                    <Style TargetType="Frame">
                        <Style.Triggers>
                            <DataTrigger TargetType="Frame"
                                         Binding="{Binding Source={x:Reference this},Path=IsChecked}" Value="True">
                                <Setter Property="BackgroundColor" Value="{x:StaticResource ColorPrimaryLight}" />
                                <Setter Property="BorderColor" Value="#8D98A0" />
                            </DataTrigger>
                            <DataTrigger TargetType="Frame"
                                         Binding="{Binding Source={x:Reference this},Path=IsChecked}" Value="False">
                                <Setter Property="BackgroundColor" Value="Transparent" />
                                <Setter Property="BorderColor" Value="Transparent" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Frame.Style>


                <Frame.GestureRecognizers>
                    <TapGestureRecognizer Tapped="onTapped" />
                </Frame.GestureRecognizers>


                <StackLayout
                    HorizontalOptions="Center"
                    Spacing="11">

                    <ImageButton
                        InputTransparent="True"
                        BackgroundColor="Transparent"
                        CornerRadius="0"
                        Source="{Binding Source={x:Reference this},Path=ImgPath}"
                        HeightRequest="68"
                        WidthRequest="68"
                        HorizontalOptions="Center" />

                    <Label
                        TextColor="#000000"
                        FontSize="12"
                        MaxLines="1"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Reference this},Path=Text}" />

                </StackLayout>


            </Frame>

        </ControlTemplate>
    </RadioButton.ControlTemplate>
</RadioButton>