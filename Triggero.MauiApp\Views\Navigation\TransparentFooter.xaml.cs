﻿using System.Windows.Input;
using DrawnUi.Views;
using Triggero.MauiMobileApp.Views.Pages.MoodTracker;

namespace Triggero.Controls.Parts
{
    public partial class TransparentFooter : Canvas
    {
        public TransparentFooter()
        {
            InitializeComponent();
        }

        public void UnselectFooter()
        {
            IsMainPageSelected = false;
            IsLibraryPageSelected = false;
            IsTestsPageSelected = false;
            IsChatBotPageSelected = false;
        }


        #region Выбор радиобаттонов
            
        private bool isMainPageSelected;

        public bool IsMainPageSelected
        {
            get => isMainPageSelected;
            set
            {
                isMainPageSelected = value;
                OnPropertyChanged(nameof(IsMainPageSelected));

            }
        }

        private bool isLibraryPageSelected;

        public bool IsLibraryPageSelected
        {
            get => isLibraryPageSelected;
            set
            {
                isLibraryPageSelected = value;
                OnPropertyChanged(nameof(IsLibraryPageSelected));

            }
        }


        private bool isTestsPageSelected;

        public bool IsTestsPageSelected
        {
            get => isTestsPageSelected;
            set
            {
                isTestsPageSelected = value;
                OnPropertyChanged(nameof(IsTestsPageSelected));

            }
        }

        private bool isChatBotPageSelected;

        public bool IsChatBotPageSelected
        {
            get => isChatBotPageSelected;
            set
            {
                isChatBotPageSelected = value;
                OnPropertyChanged(nameof(IsChatBotPageSelected));

            
            }
        }

        #endregion

        private ICommand goToMoodTracker;

        public ICommand GoToMoodTracker
        {
            get => goToMoodTracker ??= new RelayCommand(async obj =>
            {
                if (AuthHelper.IsLibraryActivated)
                {
                    App.OpenPage(new TrackerStart());
                }
                else
                {
                    App.OpenNeedToPayNow();
                }
            });
        }


        private async Task PopToMainPage()
        {
            MainThread.BeginInvokeOnMainThread(() => { App.Current.MainPage.Navigation.PopToRootAsync(false); });

            //for(int i= App.Current.MainPage.Navigation.NavigationStack.Count-1; i>=0; i--)
            //{
            //    if (App.Current.MainPage.Navigation.NavigationStack[i] is MainPage)
            //    {
            //        break;
            //    }
            //    else
            //    {
            //       await App.Current.MainPage.Navigation.PopAsync();
            //    }
            //}
        }
    }
}