﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="Triggero.MauiMobileApp.Views.MoodTracker.TrackerCalendarView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:syncfusion="clr-namespace:Syncfusion.Maui.Calendar;assembly=Syncfusion.Maui.Calendar"
    xmlns:local="clr-namespace:Triggero.MauiMobileApp.Views.MoodTracker">
    <ContentView.Content>
        <Grid>



            <syncfusion:SfCalendar
                x:Name="calendar"
                Margin="0"
                BackgroundColor="White"
                ViewChanged="monthChanged"
                SelectionChanged="selectionChanged"
                SelectionMode="Single"
                ShowTrailingAndLeadingDates="False"
                AllowViewNavigation="False"
                View="Month">

                <syncfusion:SfCalendar.MonthView>
                    <syncfusion:CalendarMonthView
                        Background="White"
                        TodayBackground="#10ABB8"
                        TrailingLeadingDatesBackground="Transparent"
                        DisabledDatesBackground="Transparent"
                        WeekendDatesBackground="Transparent">

                        <!-- TODO: Map remaining properties from Xamarin to MAUI equivalents -->
                        <!-- AgendaSelectedDateColor, InlineBackgroundColor not supported in MAUI -->
                        <!-- DateSelectionColor, SelectionRadius handled by SfCalendar.SelectionBackground -->

                        <!-- Custom cell template to match Xamarin CustomDayView -->
                        <syncfusion:CalendarMonthView.CellTemplate>
                            <DataTemplate>
                                <StackLayout HeightRequest="64" Spacing="0">
                                    <!-- Circular frame for mood indicator -->
                                    <Frame x:Name="moodFrame"
                                           HeightRequest="36"
                                           WidthRequest="36"
                                           CornerRadius="18"
                                           HorizontalOptions="Center"
                                           VerticalOptions="Start"
                                           HasShadow="False"
                                           Padding="0"
                                           BackgroundColor="#E2EEF8"
                                           BorderColor="{Binding Date, Converter={local:DateToBorderColorConverter}}">
                                        <!-- Mood image will be set via converter -->
                                        <Image x:Name="moodImage">
                                            <Image.Source>
                                                <Binding Path="Date">
                                                    <Binding.Converter>
                                                        <local:DateToMoodImageConverter />
                                                    </Binding.Converter>
                                                </Binding>
                                            </Image.Source>
                                        </Image>
                                    </Frame>
                                    <!-- Day number label -->
                                    <Label Margin="0,8,0,0"
                                           FontSize="12"
                                           TextColor="#363B40"
                                           HorizontalOptions="Center"
                                           Text="{Binding Date.Day}" />
                                </StackLayout>
                            </DataTemplate>
                        </syncfusion:CalendarMonthView.CellTemplate>

                        <!-- Hide default text since we're using custom template -->
                        <syncfusion:CalendarMonthView.TextStyle>
                            <syncfusion:CalendarTextStyle TextColor="Transparent" />
                        </syncfusion:CalendarMonthView.TextStyle>

                        <syncfusion:CalendarMonthView.SelectionTextStyle>
                            <syncfusion:CalendarTextStyle TextColor="Transparent" />
                        </syncfusion:CalendarMonthView.SelectionTextStyle>

                        <syncfusion:CalendarMonthView.HeaderView>
                            <syncfusion:CalendarMonthHeaderView Background="Transparent">
                                <syncfusion:CalendarMonthHeaderView.TextStyle>
                                    <syncfusion:CalendarTextStyle TextColor="White" />
                                </syncfusion:CalendarMonthHeaderView.TextStyle>
                            </syncfusion:CalendarMonthHeaderView>
                        </syncfusion:CalendarMonthView.HeaderView>

                    </syncfusion:CalendarMonthView>
                </syncfusion:SfCalendar.MonthView>
            </syncfusion:SfCalendar>

            <Grid
                BackgroundColor="White"
                HeightRequest="45"
                HorizontalOptions="Fill"
                VerticalOptions="Start">

                <Label
                    x:Name="monthDateLabel"
                    Margin="12,20,0,0"
                    FontAttributes="Bold"
                    FontSize="17"
                    Text="dfsdsf"
                    TextColor="#0F2552" />

                <Grid
                    Margin="0,20,27,0"
                    HeightRequest="25"
                    HorizontalOptions="End"
                    VerticalOptions="Start"
                    WidthRequest="90">

                    <ImageButton
                        Padding="0,6"
                        BackgroundColor="Transparent"
                        Clicked="goBackMonth"
                        CornerRadius="0"
                        HeightRequest="30"
                        HorizontalOptions="Start"
                        Source="arrowbackblack.png"
                        VerticalOptions="Center"
                        WidthRequest="30" />

                    <ImageButton
                        Padding="0,6"
                        BackgroundColor="Transparent"
                        Clicked="goForwardMonth"
                        CornerRadius="0"
                        HeightRequest="30"
                        HorizontalOptions="End"
                        Source="arrowforwardblack.png"
                        VerticalOptions="Center"
                        WidthRequest="30" />

                </Grid>

            </Grid>

        </Grid>
    </ContentView.Content>
</ContentView>