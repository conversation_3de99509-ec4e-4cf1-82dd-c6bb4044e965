﻿using System.Collections.Generic;
using System.Linq;
using Triggero.Controls.Templates;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Abstractions;
using Triggero.Models.Tests;


namespace Triggero.MauiMobileApp.Views
{

    public partial class NewTutorialPage10 : ContentPage
    {
        private List<Test> tests = new List<Test>();
        public NewTutorialPage10()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            tests.Add(new Test
            {
                Title = App.This.Interface.TutorialNew.TutorialNewPage10.Task1Text,
                IconImgPath = "built_in/images/test1.png",
                Questions = new List<Question> { null, null, null, null, null, null, null, null, null, null }
            });
            tests.Add(new Test
            {
                Title = App.This.Interface.TutorialNew.TutorialNewPage10.Task2Text,
                IconImgPath = "built_in/images/test2.png",
                Questions = new List<Question> { null, null, null, null, null, null, null, null, null, null }
            });
            tests.Add(new Test
            {
                Title = App.This.Interface.TutorialNew.TutorialNewPage10.Task3Text,
                IconImgPath = "built_in/images/test3.png",
                Questions = new List<Question> { null, null, null, null, null, null, null, null, null, null }
            });
            Render();
        }
        private void Render()
        {
            testsLayout.Children.Clear();
            foreach (var test in tests)
            {
                var control = new TestCard(test)
                {
                    HeightRequest = 104,
                    VerticalOptions = LayoutOptions.Start,
                    HorizontalOptions = LayoutOptions.Fill,
                    InputTransparent = true,
                };
                testsLayout.Children.Add(control);
            }
        }




        private RelayCommand goNext;
        public RelayCommand GoNext
        {
            get => goNext ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new NewTutorialPage11());

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    if (App.Current.MainPage.Navigation.NavigationStack.Contains(this))
                    {
                        try
                        {
                            App.ClosePage(this);
                        }
                        catch { }
                    }

                });

            });
        }
    }
}