﻿using MobileAPIWrapper;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Triggero.ChatBot;
using Triggero.ChatBot.ChatBot;
using Triggero.ChatBot.Enums;
using Triggero.ChatBot.Helpers.Responses;
using Triggero.Controls.Chat;
using Triggero.Controls.ChatBot;
using Triggero.Domain.Models;
using Triggero.Domain.Models.Enums;
using Triggero.MauiMobileApp.Extensions;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Extensions.Helpers.Modules;
using Triggero.MauiMobileApp.Views.Pages;
using Triggero.MauiMobileApp.Views.Pages.MoodTracker;
using Triggero.MauiMobileApp.Views;
using Triggero.Models.ChatBot;
using Triggero.Models.Messengers.ChatBot;
using Triggero.Models.Messengers.ChatBot.Attachments;

namespace Triggero.MauiMobileApp.Views
{
    public partial class ChatBotView : ContentView
    {
        public class DateLabel : Label
        {
            public DateLabel(string text)
            {
                Text = text;
                HeightRequest = 25;
                TextColor = Colors.Grey;
                FontSize = 14;
                HorizontalOptions = LayoutOptions.Center;
            }
        }

        private TriggeroChatBot bot;
        private List<ChatBotMessage> _messages;

        private List<ChatBotMessage> renderedMessages = new List<ChatBotMessage>();
        private List<View> renderedMessageViews = new List<View>();
        private DateTime? lastEarliestRenderedMessageDate;

        public ChatBotView()
        {
            InitializeComponent();

            bot = ApplicationState.ChatBotData.GetChatBot();
            bot.SpecialEvent += Bot_SpecialEvent;
        }

        public async Task ScrollToEnd()
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await Task.Delay(10); //update UI

                await messagesScrollView.ScrollToAsync(0, messagesScrollView.ContentSize.Height, false);

                //var last = messagesLayout.Children.LastOrDefault();
                //if (last != null)
                //{
                //    if (last is ChatBotLeftTextMessage message)
                //    {
                //        Debug.WriteLine($"[SCROLL] To: {message.Message.Text}");
                //    }
                //    var scrollTo = last as Element;
                //    messagesScrollView.ScrollToAsync(scrollTo, ScrollToPosition.End, false);
                //}
            });
        }

        private async Task Prepare()
        {
            if (ApplicationState.ChatBotData == null)
            {
                ApplicationState.ChatBotData = new ChatBotData();
            }

            _messages = await bot.GetMessages();
            foreach (var msg in _messages)
            {
                msg.SentAt = msg.SentAt.ToLocalTime();
            }

            _messages = _messages.OrderBy(o => o.SentAt).ToList();

            foreach (var attachment in _messages.SelectMany(o => o.Attachments))
            {
                if (attachment is ChatBotContentAttachment contentAttachment)
                {
                    contentAttachment.Test =
                        ApplicationState.Data.Tests.FirstOrDefault(o => o.Id == contentAttachment.TestId);
                    contentAttachment.Exercise =
                        ApplicationState.Data.Exercises.FirstOrDefault(o => o.Id == contentAttachment.ExerciseId);
                    contentAttachment.Practice =
                        ApplicationState.Data.Practices.FirstOrDefault(o => o.Id == contentAttachment.PracticeId);
                    contentAttachment.Topic =
                        ApplicationState.Data.Topics.FirstOrDefault(o => o.Id == contentAttachment.TopicId);
                }
            }
        }

        #region Рендер футера(кнопок-вариантов и поля для ввода запроса)

        private void RenderResponseControls(ChatBotResponse response)
        {
            interactionsRowDef.Height = new GridLength(1);
            interactionsGrid.Children.Clear();
            padding = new RowDefinition(new GridLength(Globals.BottomOffsetForTabs));

            var layout = new StackLayout()
            {
                Spacing = 8
            };

            if (response.IsActiveTextBox)
            {
                var enterBtn = response.Buttons.FirstOrDefault(o => o.IsEnterBtn);
                var otherBtns = response.Buttons.Where(o => !o.IsEnterBtn).ToList();

                BuildTextField(enterBtn, layout);
                ArrangeButtons(otherBtns, layout);
            }
            else
            {
                ArrangeButtons(response.Buttons, layout);
            }

            interactionsGrid.Children.Add(layout);
            actualInteractionsGridHeight = interactionsRowDef.Height.Value;
        }

        private void ArrangeButtons(List<ChatOptionButton> buttons, StackLayout layout)
        {
            for (int i = 0; i < buttons.Count; i += 3)
            {
                var btns = buttons.Skip(i).Take(3).ToList();


                var btnsGrid = new Grid()
                {
                    ColumnSpacing = 8,
                    RowSpacing = 8,
                    HeightRequest = 50,
                    VerticalOptions = LayoutOptions.Start
                };

                for (int j = 0; j < btns.Count; j++)
                {
                    btnsGrid.ColumnDefinitions.Add(new ColumnDefinition()
                        { Width = new GridLength(1, GridUnitType.Star) });
                }

                for (int j = 0; j < btns.Count; j++)
                {
                    var ctrl = new ChatBotButton(btns[j]);
                    Grid.SetColumn(ctrl, j);
                    ctrl.Clicked += ChatButtonClicked;

                    btnsGrid.Children.Add(ctrl);
                }

                layout.Children.Add(btnsGrid);
                interactionsRowDef.Height = new GridLength(interactionsRowDef.Height.Value + 65);
            }
        }

        private void BuildTextField(Triggero.Models.ChatBot.ChatOptionButton sendBtn, StackLayout layout)
        {
            var ctrl = new ChatBotTextBotWithButton(sendBtn)
            {
                HeightRequest = 45,
                VerticalOptions = LayoutOptions.Start
            };
            ctrl.Clicked += TextFieldEnterClicked;
            ctrl.Focused += TextFieldFocused;
            ctrl.Unfocused += TextFieldUnfocused;
            layout.Children.Add(ctrl);
            interactionsRowDef.Height = new GridLength(interactionsRowDef.Height.Value + 50);
        }


        private double actualInteractionsGridHeight = 0;

        private void TextFieldUnfocused(object sender, EventArgs e)
        {
            if (Device.RuntimePlatform == Device.iOS)
            {
                interactionsRowDef.Height = actualInteractionsGridHeight;
            }
        }

        private void TextFieldFocused(object sender, EventArgs e)
        {
            if (Device.RuntimePlatform == Device.iOS)
            {
                interactionsRowDef.Height = actualInteractionsGridHeight + 180;
            }
        }

        #endregion

        #region Render

        public bool IsRendered { get; private set; }

        public async Task LoadChat()
        {
            await Task.Run(async () =>
            {
                await Prepare();

                var lastMessage = _messages.LastOrDefault();
                if (lastMessage != null && lastMessage.ChatSide == ChatSide.Admin)
                {
                    //todo change API to add MessageType to message model
                    //instead of this hack
                    if (lastMessage.Text.Contains(BaseChatResponseHelper.StringHowCanIHelp))
                        ApplicationState.ChatBotData.WasStartMsg = true;
                }

                if (ApplicationState.ChatBotData.Bot.User == null)
                {
                    ApplicationState.ChatBotData.Bot.User = AuthHelper.User;
                }

                if (ApplicationState.ChatBotData.IsUserAFK)
                {
                    var resp = await bot.ResetState(
                        !ApplicationState.ChatBotData.WasStartMsg, ApplicationState.ChatBotData.ShouldSendHelpMessage);
                    ApplicationState.ChatBotData.WasStartMsg = true;
                    ApplicationState.ChatBotData.IsHelpMsgSent = true;
                    ApplicationState.ChatBotData.SaveResponse(resp);
                }

                ;

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    if (ApplicationState.ChatBotData.LastResponse != null)
                    {
                        RenderResponseControls(ApplicationState.ChatBotData.LastResponse);
                    }

                    RenderChat();
                });

                IsRendered = true;
            });
        }

        private void RenderChat()
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                messagesLayout.Children.Clear();
                RenderMessages(50, 0);
                areRenderedStartMessages = true;

                await Task.Delay(500);
                await ScrollToEnd();
            });
        }

        private View RenderMessage(ChatBotMessage message, bool withTypingAnimation = false, bool renderToEnd = true)
        {
            renderedMessages.Add(message);

            View view = null;
            if (message.ChatSide == ChatSide.User)
            {
                view = new ChatBotRightTextMessage(message);
            }
            else if (message.ChatSide == ChatSide.Admin)
            {
                view = new ChatBotLeftTextMessage(message);
            }

            renderedMessageViews.Add(view);


            int currentMessageCtrlIndex = -1;


            if (currentMessageCtrlIndex != -1)
            {
                messagesLayout.Children.Insert(currentMessageCtrlIndex, view);
            }
            else
            {
                if (renderToEnd)
                {
                    messagesLayout.Children.Add(view);
                }
                else
                {
                    messagesLayout.Children.Insert(0, view);
                }
            }

            return view;
        }


        private void RenderMessages(int count, int offsetFromLast)
        {
            var views = new List<View>();

            var msgs = System.Linq.Enumerable
                .TakeLast(System.Linq.Enumerable.SkipLast(_messages, offsetFromLast), count).Reverse().ToList();

            foreach (var msg in msgs)
            {
                if (lastEarliestRenderedMessageDate > msg.SentAt.Date)
                {
                    var label = new DateLabel(lastEarliestRenderedMessageDate?.Date.ToString("dd MMMM"));

                    views.Add(label);
                    messagesLayout.Children.Insert(0, label);
                }

                var msgView = RenderMessage(msg, false, false);
                views.Add(msgView);

                lastEarliestRenderedMessageDate = msg.SentAt.Date;
            }
        }

        #endregion

        #region Long polling

        private async Task StartLongPolling()
        {
            var messages = await bot.GetMessages();

            ApplicationState.Messengers.ChatBotLongPollingGotResponse += Messengers_SupportLongPollingGotResponse;
            ApplicationState.Messengers.StartChatBotLongPolling(messages.Count);
        }

        private void Messengers_SupportLongPollingGotResponse(object sender, ChatBotLongPollItem e)
        {
            if (e.Type == ChatBotLongPollType.NewMessage)
            {
                foreach (var msg in e.Messages)
                {
                    //Избегаем повторного рендеринга сообщения
                    if (!renderedMessages.Any(o => o.GlobalId == msg.GlobalId) && msg.ChatSide == ChatSide.Admin)
                    {
                        RenderMessage(msg);
                    }
                }
            }
        }

        private void StopLongPolling()
        {
            ApplicationState.Messengers.StopSupportLongPolling();
            ApplicationState.Messengers.ChatBotLongPollingGotResponse -= Messengers_SupportLongPollingGotResponse;
        }

        #endregion


        #region Events

        private void Bot_SpecialEvent(object sender, ChatBot.Models.SpecialEventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                switch (e.SpecialEventType)
                {
                    case SpecialEventType.OpenTutorial:
                        App.OpenPage(new NewTutorialPage1());
                        break;

                    case SpecialEventType.OpenLibrary:
                        var mainPage =
                            (MainPage)App.Current.MainPage.Navigation.NavigationStack
                                .FirstOrDefault(o => o is MainPage);
                        mainPage.Footer.IsLibraryPageSelected = true;
                        break;

                    case SpecialEventType.OpenMain:
                        var mainPage2 =
                            (MainPage)App.Current.MainPage.Navigation.NavigationStack
                                .FirstOrDefault(o => o is MainPage);
                        mainPage2.Footer.IsMainPageSelected = true;
                        break;

                    case SpecialEventType.OpenMoodTracker:
                        App.OpenPage(new TrackerStart());
                        break;

                    case SpecialEventType.InviteSupport:
                        TriggeroMobileAPI.MessengersMethods.ChatBotMessengerMethods.SetSeverity(AuthHelper.UserId,
                            ChatBotChatSeverity.NeedHelp);
                        StartLongPolling();
                        break;

                    case SpecialEventType.QuitSupport:
                        TriggeroMobileAPI.MessengersMethods.ChatBotMessengerMethods.SetSeverity(AuthHelper.UserId,
                            ChatBotChatSeverity.None);
                        StopLongPolling();
                        break;
                }
            });
        }

        private void ChatButtonClicked(object sender, Triggero.Models.ChatBot.ChatOptionButton e)
        {
            if (string.IsNullOrEmpty(e.Text))
                return;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                interactionsGrid.InputTransparent = true;
                await interactionsGrid.FadeTo(0.5, 400);

                RenderMessage(new ChatBotMessage { Text = e.Text, ChatSide = ChatSide.User });
                await Task.Delay(200);
                await ScrollToEnd();
                await Task.Delay(500);


                var resp = await bot.SendButton(e);
                foreach (var msg in resp.Messages)
                {
                    RenderMessage(msg, false);
                }

                ApplicationState.ChatBotData.LastUserActionDate = DateTime.Now;
                ApplicationState.ChatBotData.IsHelpMsgSent = false;
                ApplicationState.ChatBotData.SaveResponse(resp);

                RenderResponseControls(resp);
                await ScrollToEnd();

                interactionsGrid.InputTransparent = false;
                await interactionsGrid.FadeTo(1, 400);
            });
        }

        private void TextFieldEnterClicked(object sender, ChatBotEnterTextEventArgs e)
        {
            if (string.IsNullOrEmpty(e.Text))
                return;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                interactionsGrid.InputTransparent = true;
                await interactionsGrid.FadeTo(0.5, 400);

                RenderMessage(new ChatBotMessage { Text = e.Text, ChatSide = ChatSide.User });
                await Task.Delay(200);
                await ScrollToEnd();
                await Task.Delay(500);

                var resp = await bot.SendText(e.Text, e.Button);
                foreach (var msg in resp.Messages)
                {
                    RenderMessage(msg, false);
                }

                ApplicationState.ChatBotData.LastUserActionDate = DateTime.Now;
                ApplicationState.ChatBotData.IsHelpMsgSent = false;
                ApplicationState.ChatBotData.SaveResponse(resp);

                RenderResponseControls(resp);
                await ScrollToEnd();

                interactionsGrid.InputTransparent = false;
                await interactionsGrid.FadeTo(1, 400);
            });
        }

        #endregion

        bool gotNewChunk = false;
        bool areRenderedStartMessages = false;

        private void onMessagesScrollViewScrolled(object sender, ScrolledEventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                if (e.ScrollY < 10 && !gotNewChunk && areRenderedStartMessages)
                {
                    gotNewChunk = true;
                    RenderMessages(10, renderedMessages.Count);


                    await Task.Delay(1);

                    int counter = 0;
                    double height = e.ScrollY;
                    foreach (var child in messagesLayout.Children)
                    {
                        if (counter == 10) break;

                        if (child is ChatBotLeftTextMessage || child is ChatBotRightTextMessage)
                        {
                            counter++;
                        }

                        height += child.Height;
                        height += messagesLayout.Spacing;
                    }

                    if (!double.IsNaN(height))
                    {
                        await messagesScrollView.ScrollToAsync(0, height, false);
                    }
                }

                if (e.ScrollY > 10 && gotNewChunk)
                {
                    gotNewChunk = false;
                }
            });
        }
    }
}