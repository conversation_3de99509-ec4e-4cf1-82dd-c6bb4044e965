﻿<?xml version="1.0" encoding="UTF-8"?>

<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Chat.ChatLeftTextMessage"
             HorizontalOptions="Start"
             MaximumWidthRequest="250">

    <ContentView.Content>
        <Frame
            MaximumWidthRequest="250"
            HorizontalOptions="Start"
            CornerRadius="12"
            BackgroundColor="#EEF5FB"
            HasShadow="False"
            Padding="0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="50" />
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">
                    <Label
                        TextColor="{x:StaticResource ColorText}"
                        FontSize="14"
                        Margin="20,8,0,0"
                        VerticalOptions="Start"
                        HorizontalOptions="Start"
                        Text="Привет, Кристина!" />
                </Grid>

                <Grid Grid.Column="1">
                    <Label
                        TextColor="{x:StaticResource ColorText}"
                        Opacity="0.5"
                        FontSize="12"
                        Margin="0,12,0,0"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        Text="8:38" />
                </Grid>

            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>