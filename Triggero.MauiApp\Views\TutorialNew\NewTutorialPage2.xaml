﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage2"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <Grid VerticalOptions="Fill">

        <Image
            VerticalOptions="Fill"
            HorizontalOptions="Fill"
            Aspect="Fill"
            Source="tutorialblur2.png" />

        <ScrollView VerticalOptions="Fill">        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="170"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="140"/>
            </Grid.RowDefinitions>
            
 

            <Grid Grid.Row="0" Padding="0,0">

                <Grid
                    VerticalOptions="End"
                    HeightRequest="125">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>


                    <Grid Grid.Column="0">
                        <StackLayout
                            Spacing="12"
                            HeightRequest="66"
                            VerticalOptions="Center"
                            Orientation="Horizontal">

                            <Frame 
                                Margin="20,0,0,0"
                                CornerRadius="33"
                                Padding="0"
                                IsClippedToBounds="True"
                                BackgroundColor="#DEEDF9"
                                BorderColor="{x:StaticResource ColorPrimary}"
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                HeightRequest="66"
                                WidthRequest="66"
                                HasShadow="False">
                                <Image 
                                    x:Name="avatar"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    HeightRequest="66"
                                    WidthRequest="66"/>
                            </Frame>

                            <StackLayout
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                Spacing="8">
                                <Label 
                                    x:Name="hiName"
                                    TextColor="{x:StaticResource ColorText}"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    HorizontalOptions="Start"
                                    Text="Привет, Кристина"/>
                                <Label 
                                    TextColor="{x:StaticResource ColorText}"
                                    HorizontalOptions="Start"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.WhatWillDo}"/>
                            </StackLayout>

                        </StackLayout>
                    </Grid>

                    <Grid Grid.Column="1">
                        <StackLayout
                            Margin="0,0,20,0"
                            HorizontalOptions="End"
                            VerticalOptions="Center"
                            Spacing="20"
                            Orientation="Horizontal">
                            <ImageButton
                                BackgroundColor="Transparent"
                                WidthRequest="20"
                                HeightRequest="20"
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                Source="search.png"/>
                            <ImageButton
                                BackgroundColor="Transparent"
                                WidthRequest="20"
                                HeightRequest="20"
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                Source="likeset.png"/>
                        </StackLayout>
                    </Grid>

                </Grid>
                
            </Grid>

            <Grid Grid.Row="1" Padding="10,0">

                <StackLayout
                    Margin="20,-10,20,0"
                    Spacing="12">


                    <Frame
                        BackgroundColor="White"
                        CornerRadius="15"
                        Padding="0"
                        HasShadow="False"
                        HeightRequest="267">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="45"/>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0">
                                <Label 
                                    TextColor="{x:StaticResource ColorText}"
                                    FontSize="14"
                                    VerticalOptions="Center"
                                    Margin="16,6,0,0"
                                    HorizontalOptions="Start"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.Completed}"/>

                                <Frame
                                    VerticalOptions="Center"
                                    HorizontalOptions="End"
                                    Margin="0,6,16,0"
                                    BackgroundColor="White"
                                    BorderColor="{x:StaticResource ColorTextSecondary}"
                                    WidthRequest="36"
                                    HeightRequest="22"
                                    CornerRadius="9"
                                    HasShadow="False"
                                    Padding="0">
                                    <Label 
                                        TextColor="{x:StaticResource ColorTextSecondary}"
                                        FontSize="14"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"
                                        Text="1/3"/>
                                </Frame>

                            </Grid>

                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="40"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="50"/>
                                </Grid.ColumnDefinitions>

                                <Grid Grid.Column="0">
                                    <RadioButton 
                                        IsChecked="True"
                                        InputTransparent="True"
                                        Style="{x:StaticResource yellow_checkMark_rb}"
                                        VerticalOptions="Center"
                                        HorizontalOptions="End"
                                        HeightRequest="24"
                                        WidthRequest="24"/>
                                </Grid>

                                <Grid Grid.Column="1">
                                    <Image 
                                        Source="tutorialtodo1.png"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"
                                        HeightRequest="55"
                                        WidthRequest="55">
                                    </Image>
                                </Grid>

                                <Grid Grid.Column="2">
                                    <StackLayout
                                        Spacing="3"
                                        VerticalOptions="Center">
                                        <Label 
                                            TextColor="{x:StaticResource ColorText}"
                                            FontSize="16"
                                            FontFamily="FontTextLight"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start"
                                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.Task1Text}"/>
                                        <Label 
                                            TextColor="{x:StaticResource ColorText}"
                                            Opacity="0.5"
                                            FontSize="12"
                                            FontFamily="FontTextLight"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="1 "/>
                                                        <Span Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.MinutesAbbrevated}"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                    </StackLayout>
                                </Grid>

                                <Grid Grid.Column="3">
                                    <Image 
                                        Source="arrowforwardlightblue.png"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        WidthRequest="6"
                                        HeightRequest="12" />
                                </Grid>

                                <BoxView
                                    Opacity="0.5"
                                    Margin="20,0,20,0"
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"
                                    VerticalOptions="End"
                                    HorizontalOptions="Fill"
                                    HeightRequest="1"
                                    Grid.ColumnSpan="4"/>

                            </Grid>

                            <Grid Grid.Row="2">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="40"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="50"/>
                                </Grid.ColumnDefinitions>

                                <Grid Grid.Column="0">
                                    <RadioButton 
                                        IsChecked="False"
                                        InputTransparent="True"
                                        Style="{x:StaticResource yellow_checkMark_rb}"
                                        VerticalOptions="Center"
                                        HorizontalOptions="End"
                                        HeightRequest="24"
                                        WidthRequest="24"/>
                                </Grid>

                                <Grid Grid.Column="1">
                                    <Image 
                                        Source="tutorialtodo2.png"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"
                                        HeightRequest="55"
                                        WidthRequest="55">
                                    </Image>
                                </Grid>

                                <Grid Grid.Column="2">
                                    <StackLayout
                                        Spacing="3"
                                        VerticalOptions="Center">
                                        <Label 
                                            TextColor="{x:StaticResource ColorText}"
                                            FontSize="16"
                                            FontFamily="FontTextLight"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start"
                                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.Task2Text}"/>
                                        <Label 
                                            TextColor="{x:StaticResource ColorText}"
                                            Opacity="0.5"
                                            FontSize="12"
                                            FontFamily="FontTextLight"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="3 "/>
                                                        <Span Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.MinutesAbbrevated}"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                    </StackLayout>
                                </Grid>

                                <Grid Grid.Column="3">
                                    <Image 
                                        Source="arrowforwardlightblue.png"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        WidthRequest="6"
                                        HeightRequest="12" />
                                </Grid>

                                <BoxView
                                    Opacity="0.5"
                                    Margin="20,0,20,0"
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"
                                    VerticalOptions="End"
                                    HorizontalOptions="Fill"
                                    HeightRequest="1"
                                    Grid.ColumnSpan="4"/>
                                
                            </Grid>

                            <Grid Grid.Row="3">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="40"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="50"/>
                                </Grid.ColumnDefinitions>

                                <Grid Grid.Column="0">
                                    <RadioButton 
                                        IsChecked="False"
                                        InputTransparent="True"
                                        Style="{x:StaticResource yellow_checkMark_rb}"
                                        VerticalOptions="Center"
                                        HorizontalOptions="End"
                                        HeightRequest="24"
                                        WidthRequest="24"/>
                                </Grid>

                                <Grid Grid.Column="1">
                                    <Image 
                                        Source="tutorialtodo3.png"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"
                                        HeightRequest="55"
                                        WidthRequest="55">
                                    </Image>
                                </Grid>

                                <Grid Grid.Column="2">
                                    <StackLayout
                                        Spacing="3"
                                        VerticalOptions="Center">
                                        <Label 
                                            TextColor="{x:StaticResource ColorText}"
                                            FontSize="16"
                                            FontFamily="FontTextLight"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start"
                                            Text='{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.Task3Text}'/>
                                        <Label 
                                            TextColor="{x:StaticResource ColorText}"
                                            Opacity="0.5"
                                            FontSize="12"
                                            FontFamily="FontTextLight"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="10 "/>
                                                        <Span Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.MinutesAbbrevated}"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                    </StackLayout>
                                </Grid>

                                <Grid Grid.Column="3">
                                    <Image 
                                        Source="arrowforwardlightblue.png"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        WidthRequest="6"
                                        HeightRequest="12" />
                                </Grid>
                            </Grid>

                        </Grid>
                    </Frame>
                    
                    
                    <Label 
                        Margin="0,60,0,0"
                        TextColor="#000000"
                        FontAttributes="Bold"
                        FontSize="19"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="317"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.Header}"/>
                    <Label 
                        Margin="0,0,0,0"
                        TextColor="{x:StaticResource ColorText}"
                        FontSize="16"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="307"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.Description}"/>


                </StackLayout>
                
            </Grid>

            <Grid Grid.Row="2">
                <Button 
                    Command="{Binding Source={x:Reference this},Path=GoNext}"
                    VerticalOptions="Start"
                    HorizontalOptions="Fill"
                    Margin="63,0,63,0"
                    Style="{x:StaticResource yellow_btn}"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.GoNext}"/>

            </Grid>

        </Grid>
        </ScrollView>

    </Grid>
</ContentPage>