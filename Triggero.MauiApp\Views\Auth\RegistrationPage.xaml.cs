﻿using AppoMobi.Maui.Gestures;
using MobileAPIWrapper;
using System.Windows.Input;
using Triggero.Common.Helpers;
using Triggero.Domain.Models;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Views.Browse;

namespace Triggero.MauiMobileApp.Views.Pages.Auth
{

    public partial class RegistrationPage : BasePage
    {
        public RegistrationPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        public ICommand CommandPrivacy
        {
            get
            {
                return new Command(async (object context) =>
                {
                    var lang = "ru";
                    //if (App.Instance.SelectedLang != lang)
                    //    lang = "en";

                    var pagePath = $"Resources.Html.Privacy.{lang}.html";
                    var content = ReadStringFromAssembly(pagePath);
                    App.OpenPage(new Webpage("Политика конфиденциальности", content, false));
                });
            }
        }

        private bool privacyAggreed;
        public bool PrivacyAggreed
        {
            get { return privacyAggreed; }
            set { privacyAggreed = value; OnPropertyChanged(nameof(PrivacyAggreed)); }
        }

        private RegisterUserDto userModel = new();
        public RegisterUserDto UserModel
        {
            get { return userModel; }
            set { userModel = value; OnPropertyChanged(nameof(UserModel)); }
        }

        public ICommand CommandRegisterAccount
        {
            get
            {
                return new Command((ctx) =>
                {
                    if (TouchEffect.CheckLockAndSet())
                        return;

                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        try
                        {
                            if (!PrivacyAggreed)
                            {
                                errorLabel.IsVisible = true;
                                errorLabel.Text = App.This.Interface.Auth.CreateAccount.MessageNeedAcceptPrivacy;
                                App.ShowToast(App.This.Interface.Auth.CreateAccount.MessageNeedAcceptPrivacy);
                                return;
                            }

                            if (string.IsNullOrEmpty(UserModel.Name)
                                || string.IsNullOrEmpty(UserModel.Email)
                                || string.IsNullOrEmpty(UserModel.Phone)
                                || string.IsNullOrEmpty(UserModel.Password))
                            {
                                errorLabel.Text = "Заполните все обязательные поля";
                                App.ShowToast(errorLabel.Text);
                                return;
                            }


                            //быстрая валидация ввиду полного ее отсутствия до этого

                            var email = EmailValidator.NormalizedEmail(userModel.Email);
                            if (string.IsNullOrEmpty(email))
                            {
                                errorLabel.Text = "Некорректный E-mail";
                                App.ShowToast(errorLabel.Text);
                                return;
                            }
                            UserModel.Email = email;

                            if (UserModel.Password.Length < 4)
                            {
                                errorLabel.Text = "Слишком короткий пароль";
                                App.ShowToast(errorLabel.Text);
                                return;
                            }

                            if (UserModel.Phone.Length < 11)
                            {
                                errorLabel.Text = "Не корректный номер телефона";
                                App.ShowToast(errorLabel.Text);
                                return;
                            }

                            var login = await TriggeroMobileAPI.Account.Register(UserModel);
                            if (login != null)
                            {
                                if (!string.IsNullOrEmpty(login.Error))
                                {
                                    errorLabel.IsVisible = true;
                                    errorLabel.Text = $"Ошибка. {login.Error}";
                                    App.ShowToast(errorLabel.Text);
                                }
                                else
                                {
                                    AuthHelper.SetupAuthorization(login.Token);
                                    var user = await TriggeroMobileAPI.Account.MyProfileFull();
                                    if (user != null)
                                    {
                                        AuthHelper.Login(user, login.Token);
                                        App.SetMainPage(new RegistrationSuccessPage());
                                    }
                                    else
                                    {
                                        App.ShowToast("Не удалось загрузить профиль пользователя");
                                    }
                                }
                            }
                            else
                            {
                                errorLabel.IsVisible = true;
                                errorLabel.Text = "Ошибка. Проверьте правильность заполнения полей.";
                                App.ShowToast(errorLabel.Text);
                            }
                        }
                        catch (Exception e)
                        {
                            Super.Log(e);
                            App.ShowToast($"Ошибка: {e}");
                        }
                    });

                });
            }
        }

        private RelayCommand goToLogin;
        public RelayCommand GoToLogin
        {
            get => goToLogin ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new LoginStartPage());
            });
        }
    }
}