﻿using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using Triggero.Models.Practices.Categories;


namespace Triggero.MauiMobileApp.ViewModels;


public abstract class BaseCategoriesViewModel : BaseItemsViewModel<AbstractCategory>
{
    public abstract Task InitializeAsyc();

    public bool IsInitialized { get; set; }

    public abstract Color ThemeColor { get; }

    protected abstract void OnViewTapped(SkiaControl control);

    public ICommand CommandChildTapped
    {
        get
        {
            return new Command((child) =>
            {
                if (child is SkiaControlWithRect wrapper)
                {
                    if (wrapper.Control != null)
                    {
                        OnViewTapped(wrapper.Control);
                    }
                }
                else
                if (child is SkiaControl control)
                {
                    OnViewTapped(control);
                }
            });
        }
    }

    private DataTemplate _itemTemplate;
    public DataTemplate ItemTemplate
    {
        get
        {
            return _itemTemplate;
        }
        set
        {
            if (_itemTemplate != value)
            {
                _itemTemplate = value;
                OnPropertyChanged();
            }
        }
    }

    public CancellationTokenSource CancelPreload { get; set; }
}