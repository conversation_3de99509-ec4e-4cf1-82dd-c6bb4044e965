﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models;
using Triggero.Models.MoodTracker.User;


namespace Triggero.Controls.MoodTracker
{
    public partial class TrackerMoodChart : ContentView
    {
        public TrackerMoodChart()
        {
            InitializeComponent();
 
        }


        private ObservableCollection<MoodTrackerChartDay> data = new ObservableCollection<MoodTrackerChartDay>();

        public ObservableCollection<MoodTrackerChartDay> Data
        {
            get { return data; }
            set
            {
                data = value;
                OnPropertyChanged(nameof(Data));
            }
        }

        #region Точки по настроению от 0 до 5

        private ObservableCollection<MoodTrackerChartDay> data5 = new ObservableCollection<MoodTrackerChartDay>();

        public ObservableCollection<MoodTrackerChartDay> Data5
        {
            get { return data5; }
            set
            {
                data5 = value;
                OnPropertyChanged(nameof(Data5));
            }
        }

        private ObservableCollection<MoodTrackerChartDay> data4 = new ObservableCollection<MoodTrackerChartDay>();

        public ObservableCollection<MoodTrackerChartDay> Data4
        {
            get { return data4; }
            set
            {
                data4 = value;
                OnPropertyChanged(nameof(Data4));
            }
        }

        private ObservableCollection<MoodTrackerChartDay> data3 = new ObservableCollection<MoodTrackerChartDay>();

        public ObservableCollection<MoodTrackerChartDay> Data3
        {
            get { return data3; }
            set
            {
                data3 = value;
                OnPropertyChanged(nameof(Data3));
            }
        }

        private ObservableCollection<MoodTrackerChartDay> data2 = new ObservableCollection<MoodTrackerChartDay>();

        public ObservableCollection<MoodTrackerChartDay> Data2
        {
            get { return data2; }
            set
            {
                data2 = value;
                OnPropertyChanged(nameof(Data2));
            }
        }

        private ObservableCollection<MoodTrackerChartDay> data1 = new ObservableCollection<MoodTrackerChartDay>();

        public ObservableCollection<MoodTrackerChartDay> Data1
        {
            get { return data1; }
            set
            {
                data1 = value;
                OnPropertyChanged(nameof(Data1));
            }
        }

        private ObservableCollection<MoodTrackerChartDay> data0 = new ObservableCollection<MoodTrackerChartDay>();

        public ObservableCollection<MoodTrackerChartDay> Data0
        {
            get { return data0; }
            set
            {
                data0 = value;
                OnPropertyChanged(nameof(Data0));
            }
        }

        #endregion

        public async void BuildChart(DateTime from, DateTime to)
        {
            var items = await ApplicationState.Data.GetMoodtrackerItemsAtPeriod(from, to);
            BuildChart(items);
        }

        public async void BuildChart(List<MoodtrackerItem> items)
        {
            Data.Clear();

            var dayGroupings = items.GroupBy(o => o.Date.Date);

            foreach (var group in dayGroupings.OrderBy(o => o.Key))
            {
                double avg = group.Average(o => o.Mood);
                Data.Add(new MoodTrackerChartDay
                {
                    Date = group.Key.ToLocalTime(),
                    Mood = avg
                });
            }

            Data5 = new ObservableCollection<MoodTrackerChartDay>(Data.Where(o => o.Mood >= 5));
            Data4 = new ObservableCollection<MoodTrackerChartDay>(Data.Where(o => o.Mood >= 4 && o.Mood < 5));
            Data3 = new ObservableCollection<MoodTrackerChartDay>(Data.Where(o => o.Mood >= 3 && o.Mood < 4));
            Data2 = new ObservableCollection<MoodTrackerChartDay>(Data.Where(o => o.Mood >= 2 && o.Mood < 3));
            Data1 = new ObservableCollection<MoodTrackerChartDay>(Data.Where(o => o.Mood >= 1 && o.Mood < 2));
            Data0 = new ObservableCollection<MoodTrackerChartDay>(Data.Where(o => o.Mood >= 0 && o.Mood < 1));

            if (items.Any())
            {
                dateAxis.Minimum = items.Min(o => o.Date.Date).AddDays(-1);
                dateAxis.Maximum = items.Max(o => o.Date.Date).AddDays(1);
            }
        }

 
    }
}