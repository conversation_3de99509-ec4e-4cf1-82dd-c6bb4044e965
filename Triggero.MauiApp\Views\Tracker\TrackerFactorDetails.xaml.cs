﻿
using System.Windows.Input;
using Triggero.Controls.Cards.Tracker.Factors;
using Triggero.Models.MoodTracker.User;
using Triggero.MauiMobileApp.Views.Pages.MoodTracker;


namespace Triggero.MauiMobileApp.Views.MoodTracker
{

    public partial class TrackerFactorDetails : ContentPage
    {
        public TrackerFactorDetails(MoodtrackerItem item)
        {
            Tracker = item;

            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            goNextBtn.Text = $"{App.This.Interface.MoodTracker.TrackerGeneral.GoNext} 4/5";
        }

        private MoodtrackerItem tracker;
        public MoodtrackerItem Tracker
        {
            get { return tracker; }
            set { tracker = value; OnPropertyChanged(nameof(Tracker)); }
        }

        #region Init

        protected override void OnAppearing()
        {
            layout.Children.Clear();

            foreach (var item in Tracker.Influences)
            {
                layout.Children.Add(new FactorDetailsGroupCard(item));
            }

        }

        #endregion


        #region Navigation commands
        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }

        private ICommand goNext;
        public ICommand GoNext
        {
            get => goNext ??= new RelayCommand(async obj =>
            {
                try
                {
                    foreach (FactorDetailsGroupCard factorView in layout.Children)
                    {
                        if (!factorView.ValidateFactor())
                        {
                            await scrollView.ScrollToAsync(factorView, ScrollToPosition.Center, true);
                            return;
                        }
                    }
                    foreach (FactorDetailsGroupCard factorView in layout.Children)
                    {
                        factorView.BuildFactor();
                    }

                    App.OpenPage(new TrackerFinalPage(Tracker));
                }
                catch (Exception ex)
                {

                }
            });
        }
        #endregion
    }
}