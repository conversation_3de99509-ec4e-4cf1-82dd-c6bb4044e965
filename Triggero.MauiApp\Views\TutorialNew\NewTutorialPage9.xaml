﻿<?xml version="1.0" encoding="utf-8"?>

<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:parts="clr-namespace:Triggero.Controls.Parts"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage9"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <Grid VerticalOptions="Fill">

        <Image
            VerticalOptions="Fill"
            HorizontalOptions="Fill"
            Aspect="Fill"
            Source="tutorialblur3.png" />

        <ScrollView VerticalOptions="Fill">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="340" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="140" />
                </Grid.RowDefinitions>


                <Grid Grid.Row="0" Padding="10,0">

                    <Grid
                        VerticalOptions="End"
                        HeightRequest="217">

                        <Image
                            Aspect="Fill"
                            Source="tutoriallibrarycontainer.png" />

                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="70" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*" />
                                    <ColumnDefinition Width="1*" />
                                </Grid.ColumnDefinitions>


                                <Grid Grid.Column="0">
                                    <Label
                                        TextColor="{x:StaticResource ColorText}"
                                        FontSize="22"
                                        FontAttributes="Bold"
                                        Margin="20,0,0,0"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Start"
                                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Library.Library.Library}" />
                                </Grid>

                                <Grid Grid.Column="1">

                                    <StackLayout
                                        Margin="0,0,30,0"
                                        HorizontalOptions="End"
                                        VerticalOptions="Center"
                                        Spacing="20"
                                        Orientation="Horizontal">
                                        <ImageButton
                                            BackgroundColor="Transparent"
                                            WidthRequest="20"
                                            HeightRequest="20"
                                            HorizontalOptions="Center"
                                            VerticalOptions="Center"
                                            Source="search.png" />
                                        <ImageButton
                                            BackgroundColor="Transparent"
                                            WidthRequest="20"
                                            HeightRequest="20"
                                            HorizontalOptions="Center"
                                            VerticalOptions="Center"
                                            Source="likeset.png" />
                                    </StackLayout>

                                </Grid>

                            </Grid>

                            <Grid
                                Grid.Row="1"
                                Margin="20,0,20,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*" />
                                    <ColumnDefinition Width="1*" />
                                    <ColumnDefinition Width="1*" />
                                    <ColumnDefinition Width="1*" />
                                </Grid.ColumnDefinitions>

                                <Frame Grid.Column="0"
                                       IsClippedToBounds="True"
                                       HorizontalOptions="Center"
                                       VerticalOptions="Center"
                                       HasShadow="False"
                                       BackgroundColor="White"
                                       Padding="0"
                                       CornerRadius="16"
                                       HeightRequest="90"
                                       WidthRequest="75">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="60" />
                                            <RowDefinition Height="30" />
                                        </Grid.RowDefinitions>

                                        <Grid
                                            Opacity="0.4"
                                            Grid.RowSpan="2">
                                            <Grid.Background>
                                                <LinearGradientBrush>
                                                    <LinearGradientBrush.GradientStops>
                                                        <GradientStop Color="#F9EDC7" Offset="0.1" />
                                                        <GradientStop Color="#ECA069" Offset="1.0" />
                                                    </LinearGradientBrush.GradientStops>
                                                </LinearGradientBrush>
                                            </Grid.Background>
                                        </Grid>


                                        <Grid Grid.Row="0">
                                            <Image
                                                Source="menuexercises.png"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Center"
                                                Aspect="Fill"
                                                HeightRequest="46"
                                                WidthRequest="60" />
                                        </Grid>

                                        <Grid Grid.Row="1">
                                            <Label
                                                TextColor="{x:StaticResource ColorText}"
                                                FontSize="10"
                                                VerticalOptions="Start"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Library.Library.Exercises}" />
                                        </Grid>

                                    </Grid>
                                </Frame>

                                <Frame Grid.Column="1"
                                       IsClippedToBounds="True"
                                       HorizontalOptions="Center"
                                       VerticalOptions="Center"
                                       HasShadow="False"
                                       Padding="0"
                                       BackgroundColor="White"
                                       CornerRadius="16"
                                       HeightRequest="90"
                                       WidthRequest="75">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="60" />
                                            <RowDefinition Height="30" />
                                        </Grid.RowDefinitions>

                                        <Grid
                                            Opacity="0.4"
                                            Grid.RowSpan="2">
                                            <Grid.Background>
                                                <LinearGradientBrush>
                                                    <LinearGradientBrush.GradientStops>
                                                        <GradientStop Color="#B3B3E6" Offset="0.1" />
                                                        <GradientStop Color="#F9ACC0" Offset="1.0" />
                                                    </LinearGradientBrush.GradientStops>
                                                </LinearGradientBrush>
                                            </Grid.Background>
                                        </Grid>

                                        <Grid Grid.Row="0">
                                            <Image
                                                Source="menupractices.png"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Center"
                                                Aspect="Fill"
                                                HeightRequest="45"
                                                WidthRequest="52" />
                                        </Grid>

                                        <Grid Grid.Row="1">
                                            <Label
                                                TextColor="{x:StaticResource ColorText}"
                                                FontSize="10"
                                                VerticalOptions="Start"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Library.Library.Practices}" />
                                        </Grid>

                                    </Grid>
                                </Frame>

                                <Frame Grid.Column="2"
                                       IsClippedToBounds="True"
                                       HorizontalOptions="Center"
                                       VerticalOptions="Center"
                                       HasShadow="False"
                                       Padding="0"
                                       BackgroundColor="White"
                                       CornerRadius="16"
                                       HeightRequest="90"
                                       WidthRequest="75">

                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="60" />
                                            <RowDefinition Height="30" />
                                        </Grid.RowDefinitions>

                                        <Grid
                                            Opacity="1"
                                            Grid.RowSpan="2">
                                            <Grid.Background>
                                                <LinearGradientBrush>
                                                    <LinearGradientBrush.GradientStops>
                                                        <GradientStop Color="#F7FFF3" Offset="0.1" />
                                                        <GradientStop Color="#F0FFEA" Offset="0.5" />
                                                        <GradientStop Color="#EDF8FF" Offset="1.0" />
                                                    </LinearGradientBrush.GradientStops>
                                                </LinearGradientBrush>
                                            </Grid.Background>
                                        </Grid>

                                        <Grid Grid.Row="0">
                                            <Image
                                                Source="menutopics.png"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Center"
                                                Aspect="Fill"
                                                HeightRequest="50"
                                                WidthRequest="59" />
                                        </Grid>

                                        <Grid Grid.Row="1">
                                            <Label
                                                TextColor="{x:StaticResource ColorText}"
                                                FontSize="10"
                                                VerticalOptions="Start"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Library.Library.Topics}" />
                                        </Grid>

                                    </Grid>
                                </Frame>

                                <Frame Grid.Column="3"
                                       IsClippedToBounds="True"
                                       HorizontalOptions="Center"
                                       VerticalOptions="Center"
                                       BackgroundColor="White"
                                       HasShadow="False"
                                       Padding="0"
                                       CornerRadius="16"
                                       HeightRequest="90"
                                       WidthRequest="75">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="60" />
                                            <RowDefinition Height="30" />
                                        </Grid.RowDefinitions>

                                        <Grid
                                            Opacity="0.4"
                                            Grid.RowSpan="2">
                                            <Grid.Background>
                                                <LinearGradientBrush>
                                                    <LinearGradientBrush.GradientStops>
                                                        <GradientStop Color="#BFFFFB" Offset="0.1" />
                                                        <GradientStop Color="#2448DC" Offset="1.0" />
                                                    </LinearGradientBrush.GradientStops>
                                                </LinearGradientBrush>
                                            </Grid.Background>
                                        </Grid>


                                        <Grid Grid.Row="0">
                                            <Image
                                                Aspect="Fill"
                                                Source="menubreath2.png"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Center"
                                                HeightRequest="38"
                                                WidthRequest="51" />
                                        </Grid>

                                        <Grid Grid.Row="1">
                                            <Label
                                                TextColor="{x:StaticResource ColorText}"
                                                FontSize="10"
                                                VerticalOptions="Start"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Library.Library.Breath}" />
                                        </Grid>

                                    </Grid>
                                </Frame>

                            </Grid>

                        </Grid>

                    </Grid>


                </Grid>

                <Grid Grid.Row="1" Padding="10,0">

                    <StackLayout
                        Margin="20,0,20,0">

                        <parts:TransparentFooter
                            IsLibraryPageSelected="True"
                            Margin="0,10,0,0"
                            HorizontalOptions="Fill"
                            InputTransparent="False" />


                        <Label
                            Margin="0,35,0,0"
                            TextColor="#000000"
                            FontAttributes="Bold"
                            FontSize="19"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="317"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage9.Header}" />
                        <Label
                            Margin="0,0,0,0"
                            TextColor="{x:StaticResource ColorText}"
                            FontSize="16"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="{x:OnPlatform Android=280,iOS=320}"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage9.Description}" />

                    </StackLayout>

                </Grid>


                <Grid Grid.Row="2">
                    <Button
                        Command="{Binding Source={x:Reference this},Path=GoNext}"
                        VerticalOptions="Start"
                        HorizontalOptions="Fill"
                        Margin="63,0,63,0"
                        Style="{x:StaticResource yellow_btn}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage9.GoNext}" />

                </Grid>

            </Grid>
        </ScrollView>
    </Grid>
</ContentPage>