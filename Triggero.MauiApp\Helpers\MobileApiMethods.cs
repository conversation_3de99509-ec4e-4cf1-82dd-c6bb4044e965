using System.Net;
using System.Net.Http;
using System.Text.Json;
using Triggero.Domain.Models;
using Triggero.Domain.Models.Dto;
using Triggero.Models.General;
using MobileAPIWrapper;
using MobileAPIWrapper.Helpers;

namespace Triggero.MauiMobileApp.Helpers
{
    /// <summary>
    /// Mobile-specific API methods that use pure HttpClient (no RestSharp dependency)
    /// This replaces the RestSharp-based API methods for mobile applications
    /// </summary>
    public static class MobileApiMethods
    {
        /// <summary>
        /// Account-related API methods
        /// </summary>
        public static class Account
        {
            public static async Task<LoginResponseDto?> Register(RegisterUserDto dto)
            {
                string url = TriggeroMobileAPI.AddBaseUrl($"/Account/Register");
                return await RequestHelper.ExecuteRequestReceiveModelAsync<LoginResponseDto>(url, Method.Post, dto);
            }

            public static async Task<bool> RequestSms(string phone)
            {
                string url = TriggeroMobileAPI.AddBaseUrl($"/Account/RequestSms?phone={phone}");
                var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);
                return response.StatusCode == HttpStatusCode.OK;
            }

            public static async Task<LoginResponseDto?> LoginWithPhone(string phone, string code)
            {
                string url = TriggeroMobileAPI.AddBaseUrl($"/Account/LoginWithPhone");

                var dto = new LoginWithPhoneNumberDto
                {
                    Code = code,
                    PhoneNumber = phone
                };

                return await RequestHelper.ExecuteRequestReceiveModelAsync<LoginResponseDto>(url, Method.Post, dto);
            }

            public static async Task<LoginResponseDto?> LoginWithEmail(string email, string password)
            {
                string url = TriggeroMobileAPI.AddBaseUrl($"/Account/LoginWithEmail");

                var dto = new LoginWithEmailDto
                {
                    Email = email,
                    Password = password
                };

                return await RequestHelper.ExecuteRequestReceiveModelAsync<LoginResponseDto>(url, Method.Post, dto);
            }
        }

        /// <summary>
        /// User-related API methods
        /// </summary>
        public static class Users
        {
            private static string BASE_HOST => TriggeroMobileAPI.AddBaseUrl("Users/");

            public static async Task<User?> GetUserById(int id, OSType OS)
            {
                string url = BASE_HOST + $"GetUserById?id={id}&OS={(int)OS}";
                return await RequestHelper.ExecuteRequestReceiveModelAsync<User>(url, Method.Get);
            }

            public static async Task<User?> GetUserByPhone(string phone, OSType OS)
            {
                string url = BASE_HOST + $"GetUserByPhone?phone={phone.Replace("+", "")}&OS={(int)OS}";
                return await RequestHelper.ExecuteRequestReceiveModelAsync<User>(url, Method.Get);
            }
        }

        /// <summary>
        /// Common API methods
        /// </summary>
        public static class Common
        {
            private static string BASE_HOST => TriggeroMobileAPI.AddBaseUrl("Common/");

            public static string GetDownloadUrl(string filename, ThumbnailSize thumbnailSize, string ext = ".png")
            {
                return BASE_HOST + $"GetImage/{thumbnailSize}/{ext}/{filename}";
            }

            public static async Task<bool> CheckHui()
            {
                string url = BASE_HOST + "CheckHui";
                var result = await RequestHelper.ExecuteRequestReceiveModelAsync<bool?>(url, Method.Get);
                return result ?? false;
            }
        }

        /// <summary>
        /// Payment-related API methods
        /// </summary>
        public static class Payment
        {
            private static string BASE_HOST => TriggeroMobileAPI.AddBaseUrl("Payments/");

            public static async Task<bool> CheckPayment(string paymentId)
            {
                string url = BASE_HOST + $"CheckPayment?paymentId={paymentId}";
                var result = await RequestHelper.ExecuteRequestReceiveModelAsync<bool?>(url, Method.Get);
                return result ?? false;
            }

            public static async Task UnbindCard(int userId)
            {
                string url = BASE_HOST + $"UnbindCard?userId={userId}";
                await RequestHelper.ExecuteRequestAsync(url, Method.Get);
            }
        }
    }
}
