﻿using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Triggero.Database;
using Triggero.Domain.Controllers;
using Triggero.Domain.Controllers.Rest;
using Triggero.Domain.Models;
using Triggero.Models.Enums;
using Triggero.Models.General;
using Yandex.Checkout.V3;

namespace Triggero.Domain.Jobs
{

    public class AutoPaymentJob
    {

        public static async Task StartJob(IServiceProvider services)
        {
            await Task.Run(async () =>
            {
                await Task.Delay(TimeSpan.FromSeconds(15));

                Console.WriteLine($"Started AutoPaymentJob");

                while (true)
                {
                    try
                    {
                        // Create a new scope for each iteration to get fresh services
                        using (var scope = services.CreateScope())
                        {
                            var config = scope.ServiceProvider.GetRequiredService<IConfiguration>();
                            var db = scope.ServiceProvider.GetRequiredService<DatabaseContext>();

                            var client = new Yandex.Checkout.V3.Client(
                                shopId: config["Secrets:UKassa:ShopId"],
                                secretKey: config["Secrets:UKassa:Key"]);

                            var users = db.Users.Include(o => o.UserSubscription).ThenInclude(o => o.UserSubsctiptionOptions).Where(o => !o.IsDeleted).ToList();

                            foreach (var user in users)
                            {


                                if (user.UserSubscription == null) continue;

                                if (!user.UserSubscription.IsSubscriptionEnded
                                    || !user.UserSubscription.AllowRecurrent
                                    || string.IsNullOrEmpty(user.UserSubscription.SavedPaymentId))
                                {
                                    continue;
                                }


                                try
                                {
                                    var planOptionIds = new List<int>();
                                    foreach (var option in user.UserSubscription.UserSubsctiptionOptions)
                                    {
                                        planOptionIds.Add(option.PlanOptionId);
                                    }

                                    var settings = new SubscriptionPaymentSettings
                                    {
                                        Duration = user.UserSubscription.SubscriptionDuration,
                                        IsBindingPayment = false,
                                        SubType = user.UserSubscription.SubscriptionType,
                                        planOptionIds = planOptionIds
                                    };

                                    var paymentInfo = await PaymentsController.PaySubscriptionYookassaAuto(db, client, user.Id, settings);
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"AutoPaymentJob error for user {user.Id}: {ex.Message}");
                                }


                                await Task.Delay(2000);

                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"AutoPaymentJob error: {ex.Message}");
                    }


                    await Task.Delay(TimeSpan.FromHours(1));
                }
            });
        }

    }
}
