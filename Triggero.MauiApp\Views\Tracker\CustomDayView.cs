﻿namespace Triggero.MauiMobileApp.Views.MoodTracker;

public class CustomDayView : StackLayout
{
    private readonly Label _label;
    private readonly Image _moodImg;
    private readonly Frame _frame;
    public DateTime Date { get; set; }

    public void SetContext(DateTime date, int? mood)
    {
        Date = date;

        if (mood == null)
        {
            _frame.Content = null;
        }
        else
        {
            switch (mood.Value)
            {
                case 5:
                    _moodImg.Source = "daymood1.png";
                    break;
                case 4:
                    _moodImg.Source = "daymood2.png";
                    break;
                case 3:
                    _moodImg.Source = "daymood3.png";
                    break;
                case 2:
                    _moodImg.Source = "daymood4.png";
                    break;
                case 1:
                    _moodImg.Source = "daymood5.png";
                    break;
                case 0:
                    _moodImg.Source = "daymood6.png";
                    break;
            }
            _frame.Content = _moodImg;
        }

        if (Date == DateTime.Now.Date)
        {
            _frame.BorderColor = Color.FromHex("#10ABB8");
        }
        else
        {
            _frame.BorderColor = Colors.Transparent;
        }

        _label.Text = Date.Day.ToString();
    }

    public CustomDayView()
    {
        HeightRequest = 50;
        Spacing = 0;

        _frame = new Frame()
        {
            HeightRequest = 36,
            WidthRequest = 36,
            CornerRadius = 18,
            HorizontalOptions = LayoutOptions.Center,
            VerticalOptions = LayoutOptions.Start,
            HasShadow = false,
            Padding = 0,
            BackgroundColor = Color.FromHex("#E2EEF8"),
            //Background = Color.FromHex("#E2EEF8"),
        };
        Children.Add(_frame);


        _label = new Label()
        {
            Margin = new Thickness(0, 8, 0, 0),
            FontSize = 12,
            TextColor = Color.FromHex("#363B40"),
            HorizontalOptions = LayoutOptions.Center,
        };
        Children.Add(_label);

        //_frame.Content = new Grid();

        _moodImg = new Image();



    }
}