using Foundation;
using Triggero.MauiMobileApp.Abstractions;
using UIKit;

namespace Triggero.MauiMobileApp.Platforms.iOS
{
    public class ToastMessage : IToastMessage
    {
        public class ToastPopup
        {
            public string UID { get; set; } = string.Empty;
            public UIAlertController? Popup { get; set; }
            public NSTimer? Timer { get; set; }
            public string Message { get; set; } = string.Empty;
            public double Seconds { get; set; }
        }

        const double LONG_DELAY = 4.0;
        const double SHORT_DELAY = 2.5;

        private readonly List<ToastPopup> _popups = new();

        public void LongAlert(string message, ToastPosition position)
        {
            ShowAlert(message, LONG_DELAY);
        }

        public void ShortAlert(string message)
        {
            ShowAlert(message, SHORT_DELAY);
        }

        void ShowAlert(string message, double seconds)
        {
            try
            {
                // TODO: Xamarin code used Device.BeginInvokeOnMainThread
                // MAUI equivalent is MainThread.BeginInvokeOnMainThread
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    var uid = Guid.NewGuid().ToString();
                    var alert = UIAlertController.Create(null, message, UIAlertControllerStyle.Alert);
                    var popup = new ToastPopup
                    {
                        Seconds = seconds,
                        Timer = null,
                        Popup = alert,
                        UID = uid,
                        Message = message
                    };
                    _popups.Add(popup);
                    if (_popups.Count == 1)
                        Show(popup);
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ToastMessage] ShowAlert failed: {ex.Message}");
            }
        }

        void Show(ToastPopup popup)
        {
            try
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    if (popup.Popup == null) return;

                    popup.Timer = NSTimer.CreateScheduledTimer(popup.Seconds, (obj) =>
                    {
                        DismissMessage(popup.UID);
                    });

                    // TODO: Xamarin code used UIApplication.SharedApplication.KeyWindow.RootViewController
                    // MAUI might need different approach to get current view controller
                    var rootViewController = GetCurrentViewController();
                    rootViewController?.PresentViewController(popup.Popup, true, null);
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ToastMessage] Show failed: {ex.Message}");
            }
        }

        private UIViewController? GetCurrentViewController()
        {
            try
            {
                // TODO: Xamarin code - needs adaptation for MAUI
                // Original used UIApplication.SharedApplication.KeyWindow.RootViewController
                var window = UIApplication.SharedApplication.ConnectedScenes
                    .OfType<UIWindowScene>()
                    .SelectMany(scene => scene.Windows)
                    .FirstOrDefault(w => w.IsKeyWindow);

                return window?.RootViewController;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ToastMessage] GetCurrentViewController failed: {ex.Message}");
                return null;
            }
        }

        void DismissMessage(string uid)
        {
            try
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    var popup = _popups.FirstOrDefault(x => x.UID == uid);
                    if (popup != null)
                    {
                        popup.Timer?.Invalidate();
                        popup.Popup?.DismissViewController(true, null);
                        _popups.Remove(popup);

                        if (_popups.Count > 0)
                        {
                            Show(_popups[0]);
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ToastMessage] DismissMessage failed: {ex.Message}");
            }
        }
    }
}
