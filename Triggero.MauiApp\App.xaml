﻿<?xml version = "1.0" encoding = "UTF-8" ?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:Triggero.MauiMobileApp"
             xmlns:converters="clr-namespace:Triggero.MauiMobileApp.Converters"
             xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             xmlns:converters1="clr-namespace:MarkupCreator.Converters"
             xmlns:popups="clr-namespace:AppoMobi.Maui.Popups;assembly=AppoMobi.Maui.Popups"
             xmlns:converters2="clr-namespace:Syncfusion.Maui.Core.Converters;assembly=Syncfusion.Maui.Core"
             x:Class="Triggero.MauiMobileApp.App">
    <Application.Resources>
        <ResourceDictionary>

            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Svg.xaml" />
                <ResourceDictionary Source="Resources/Styles/DefineColors.xaml" />
                <ResourceDictionary Source="Resources/Styles/SfSwitchStyles.xaml" />
                <ResourceDictionary Source="Resources/Styles/TextEditStyles.xaml" />
                <ResourceDictionary Source="Resources/Styles/RadioButtonStyles.xaml" />
                <ResourceDictionary Source="Resources/Styles/CheckBoxStyles.xaml" />
                <ResourceDictionary Source="Resources/Styles/ButtonStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <converters:BoolToColorConverter x:Key="BoolToColorConverter" />
            <converters1:TimeSpanToStringConverter x:Key="TimeSpanToStringConverter" />
            <converters1:IntegerNotZeroConverter x:Key="IntegerNotZeroConverter" />
            <converters1:IntegerIsZeroConverter x:Key="IntegerIsZeroConverter" />
            <converters2:InvertedBoolConverter x:Key="NotConverter" />
            <converters:IsStringNullOrEmptyConverter x:Key="IsNotNullOrEmptyConverter" />
            <converters1:CompareIntegersConverter x:Key="CompareIntegersConverter" />


            <!--<Style
                ApplyToDerivedTypes="True"
                TargetType="popups:Popup">
                <Setter Property="VerticalOptions" Value="Center" />
                <Setter Property="HorizontalOptions" Value="Center" />
            </Style>-->

            <Style
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontFamily" Value="FontText" />
                <Setter Property="FontSize" Value="12" />
                <Setter Property="TextColor" Value="{x:StaticResource ColorText}" />
            </Style>

            <!--<Style
                ApplyToDerivedTypes="True"
                TargetType="Grid">
                <Setter Property="ColumnSpacing" Value="8" />
                <Setter Property="RowSpacing" Value="8" />
            </Style>-->

            <Style
                ApplyToDerivedTypes="True"
                TargetType="Button">
                <Setter Property="FontFamily" Value="FontText" />
                <Setter Property="FontSize" Value="14" />
            </Style>

            <Style
                x:Key="StyleBtnText"
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontFamily" Value="FontTextBold" />
                <Setter Property="FontSize" Value="14" />
            </Style>

            <Style
                x:Key="StyleHeaderNavigation"
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontFamily" Value="FontTextBold" />
                <Setter Property="FontSize" Value="16" />
            </Style>

            <Style
                x:Key="StyleHeaderText"
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontFamily" Value="FontTextBold" />
                <Setter Property="FontSize" Value="24" />
            </Style>


            <Style
                x:Key="StyleArticleTitle"
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontFamily" Value="FontTextSemiBold" />
                <Setter Property="FontSize" Value="19" />
                <Setter Property="TextColor" Value="Black" />
            </Style>

            <Style
                x:Key="StyleArticleSubTitle"
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontSize" Value="12" />
                <Setter Property="TextColor" Value="#7F000000" />
            </Style>

            <Style
                x:Key="StyleArticleText"
                ApplyToDerivedTypes="True"
                TargetType="Label">
                <Setter Property="FontSize" Value="15" />
            </Style>

            <Style
                x:Key="StyleHeaderTextDrawn"
                ApplyToDerivedTypes="True"
                TargetType="draw:SkiaLabel">
                <Setter Property="ParagraphSpacing" Value="0.1" />
                <Setter Property="FontFamily" Value="FontTextBold" />
                <Setter Property="FontSize" Value="24" />
            </Style>

            <Style
                ApplyToDerivedTypes="True"
                TargetType="draw:SkiaLabel">
                <Setter Property="FontFamily" Value="FontText" />
                <Setter Property="FontSize" Value="12" />
                <Setter Property="LineSpacing" Value="1.0" />
                <Setter Property="TextColor" Value="{x:StaticResource ColorText}" />
            </Style>

            <Style
                x:Key="StyleBtnTextDrawn"
                ApplyToDerivedTypes="True"
                TargetType="draw:SkiaLabel">
                <Setter Property="FontFamily" Value="FontTextBold" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="LineSpacing" Value="0.9" />
                <Setter Property="ParagraphSpacing" Value="-0.15" />
            </Style>

            <Style
                ApplyToDerivedTypes="True"
                TargetType="ContentPage">
                <Setter Property="BackgroundColor" Value="White" />
            </Style>

            <Style
                ApplyToDerivedTypes="True"
                TargetType="draw:SkiaShape">
                <Setter Property="CornerRadius" Value="15" />
            </Style>

            <Style
                ApplyToDerivedTypes="True"
                TargetType="NavigationPage">
                <Setter Property="BackgroundColor" Value="White" />
            </Style>

            <Style
                ApplyToDerivedTypes="True"
                TargetType="ActivityIndicator">
                <Setter Property="Color" Value="{x:StaticResource ColorPrimary}" />
            </Style>

        </ResourceDictionary>
    </Application.Resources>
</Application>
