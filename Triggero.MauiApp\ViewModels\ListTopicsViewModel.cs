﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;


namespace Triggero.MauiMobileApp.ViewModels;

public class ListTopicsViewModel : ElementsListViewModel
{
    private readonly TopicCategory _category;

    public ListTopicsViewModel(TopicCategory category)
    {
        _category = category;

        Title = _category.Localizations.GetLocalizedTitle(LanguageHelper.LangCode, _category.Title);
    }

    public override Color ThemeColorB
    {
        get
        {
            return Color.FromHex("#9FAEA8");
        }
    }

    public override Color ThemeColor
    {
        get
        {
            return Color.FromHex("#F4FFFB");
        }
    }

    protected override async Task<IEnumerable<IElementDetails>> LoadItemsAsync()
    {
        return await ApplicationState.Data.GetTopics(_category.Id); ;
    }


}