using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;

#if ANDROID
using Android.Graphics.Drawables;
using AndroidX.AppCompat.Widget;
using Microsoft.Maui.Platform;
#elif IOS
using UIKit;
using Foundation;
#elif WINDOWS
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml;
#endif

namespace Triggero.MauiMobileApp.Handlers
{
    public class CustomEditorHandler : EditorHandler
    {
#if ANDROID
        protected override void ConnectHandler(AppCompatEditText platformView)
        {
            base.ConnectHandler(platformView);

            ApplyCustomStyling(platformView);
        }

        private void ApplyCustomStyling(AppCompatEditText platformView)
        {
            // Create a completely transparent drawable
            var transparentDrawable = new ColorDrawable(Android.Graphics.Color.Transparent);

            // Remove background (underline) immediately and permanently
            platformView.Background = transparentDrawable;
            platformView.SetBackgroundDrawable(transparentDrawable);

            // Remove padding
            platformView.SetPadding(0, 0, 0, 0);

            // Remove any additional styling
            platformView.SetBackgroundColor(Android.Graphics.Color.Transparent);

            // Disable the background tint to prevent any native theming
            platformView.BackgroundTintList = null;
            platformView.BackgroundTintMode = null;

            // Use post to ensure this runs after any native focus handling
            platformView.Post(() =>
            {
                platformView.Background = transparentDrawable;
                platformView.SetBackgroundDrawable(transparentDrawable);
            });

            // Handle focus changes with immediate reapplication
            platformView.FocusChange += (sender, e) =>
            {
                if (sender is AppCompatEditText editText)
                {
                    // Use post to run after native focus handling
                    editText.Post(() =>
                    {
                        editText.Background = transparentDrawable;
                        editText.SetBackgroundDrawable(transparentDrawable);
                        editText.BackgroundTintList = null;
                    });
                }
            };
        }
#endif

#if IOS
        protected override void ConnectHandler(MauiTextView platformView)
        {
            base.ConnectHandler(platformView);
            
            // Remove background and border
            platformView.BackgroundColor = UIColor.Clear;
            platformView.Layer.BorderWidth = 0;
            
            // Remove padding/insets
            platformView.TextContainerInset = UIEdgeInsets.Zero;
            platformView.TextContainer.LineFragmentPadding = 0;
            
            // Remove scrolling indicators if desired
            platformView.ShowsVerticalScrollIndicator = false;
            platformView.ShowsHorizontalScrollIndicator = false;
        }
#endif

#if WINDOWS
        protected override void ConnectHandler(TextBox platformView)
        {
            base.ConnectHandler(platformView);
            
            // Remove border and background
            platformView.BorderThickness = new Microsoft.UI.Xaml.Thickness(0);
            platformView.Background = null;
            
            // Remove padding
            platformView.Padding = new Microsoft.UI.Xaml.Thickness(0);
            
            // Remove focus visual
            platformView.UseSystemFocusVisuals = false;
            platformView.IsTabStop = true;
            
            // Remove any additional styling
            platformView.Style = null;
            
            // For multiline text (Editor), ensure proper behavior
            platformView.AcceptsReturn = true;
            platformView.TextWrapping = Microsoft.UI.Xaml.TextWrapping.Wrap;
        }
#endif
    }
}
