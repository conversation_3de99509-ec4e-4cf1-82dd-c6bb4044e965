﻿using AppoMobi.Specials;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using Triggero.MauiMobileApp.Extensions;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.ViewModels;


namespace Triggero.MauiMobileApp.Views.Pages
{
    public partial class SearchPage : ContentPage//, IDisposable
    {

        private bool _rendererSet;
        protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            base.OnPropertyChanged(propertyName);

            if (propertyName == "Renderer")
            {
                if (_rendererSet)
                {
                    Dispose();
                }
                else
                {
                    _rendererSet = true;
                }
            }
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();

            PlatformUi.Instance.ApplyTheme();
        }

        public void Dispose()
        {
            DrawnCanvas.DisableUpdates();
            DrawnCanvas?.Dispose();
            if (BindingContext is IDisposable dispose)
            {
                dispose.Dispose();
            }
        }

        public SearchPage()
        {
            _vm = new SearchPageViewModel();

            InitializeComponent();

            BindingContext = _vm;

            RenderSearchQueries();
        }

        public SearchPage(string query)
        {
            _vm = new SearchPageViewModel();

            InitializeComponent();

            BindingContext = _vm;

            SearchQuery = query;

            _ = RenderSearchQueries();
        }


        #region Render

        private async Task RenderSearchQueries()
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                queriesLayout.Children.Clear();
                foreach (var query in ApplicationState.Data.GetOftenSearchPageQueries())
                {
                    var label = new Label
                    {
                        FontSize = 16,
                        TextColor = Color.FromHex("#10ABB8"),
                        TextDecorations = TextDecorations.Underline,
                        HorizontalOptions = LayoutOptions.Center,
                        Text = query
                    };
                    label.GestureRecognizers.Add(new TapGestureRecognizer
                    {
                        Command = SetSearchQuery,
                        CommandParameter = query
                    });
                    queriesLayout.Children.Add(label);
                }
            });
        }

        private async Task RenderSearchResult()
        {

            Tasks.StartDelayed(TimeSpan.FromMilliseconds(30), async () =>
            {
                await _vm.LoadDataAsync();
            });

            //MainThread.BeginInvokeOnMainThread(() =>
            //{
            //    var foundTests = tests.Where(o => o.Title.ToLower().Contains(SearchQuery.ToLower())
            //                            || o.MainTags.ToLower().Contains(SearchQuery.ToLower())
            //                            || o.SecondaryTags.ToLower().Contains(SearchQuery.ToLower())
            //                            || o.Localizations.Any(o => o.Title.ToLower().Contains(SearchQuery.ToLower()))).ToList();

            //    var foundExercises = exercises.Where(o => o.Title.ToLower().Contains(SearchQuery.ToLower())
            //                                    || o.MainTags.ToLower().Contains(SearchQuery.ToLower())
            //                                    || o.SecondaryTags.ToLower().Contains(SearchQuery.ToLower())
            //                                    || o.Localizations.Any(o => o.Title.ToLower().Contains(SearchQuery.ToLower()))).ToList();

            //    var foundTopics = topics.Where(o => o.Title.ToLower().Contains(SearchQuery.ToLower())
            //                                    || o.MainTags.ToLower().Contains(SearchQuery.ToLower())
            //                                    || o.SecondaryTags.ToLower().Contains(SearchQuery.ToLower())
            //                                    || o.Localizations.Any(o => o.Title.ToLower().Contains(SearchQuery.ToLower()))).ToList();

            //    var foundPractices = practices.Where(o => o.Title.ToLower().Contains(SearchQuery.ToLower())
            //                                   || o.MainTags.ToLower().Contains(SearchQuery.ToLower())
            //                                   || o.SecondaryTags.ToLower().Contains(SearchQuery.ToLower())
            //                                   || o.Localizations.Any(o => o.Title.ToLower().Contains(SearchQuery.ToLower()))).ToList();


            //    foundCount = foundTests.Count() + foundExercises.Count() + foundTopics.Count() + foundPractices.Count();

            //    var itemsSource = new List<AbsModel>();
            //    itemsSource.AddRange(foundTests);
            //    itemsSource.AddRange(foundExercises);
            //    itemsSource.AddRange(foundTopics);
            //    itemsSource.AddRange(foundPractices);


            //    collection.ItemsSource = itemsSource;
            //});
        }


        #endregion


        #region Search


        private string searchQuery = "";
        public string SearchQuery
        {
            get => searchQuery;
            set
            {
                searchQuery = value;
                OnPropertyChanged();
                _vm.SearchQuery = value;
            }
        }

        DateTime lastTextChangedTime;
        private async void textChanged(object sender, EventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                lastTextChangedTime = DateTime.Now;
                await Task.Delay(1000);

                if (lastTextChangedTime.AddSeconds(1) < DateTime.Now)
                {
                    await RenderSearchResult();
                }
            });
        }

        private RelayCommand setSearchQuery;
        public RelayCommand SetSearchQuery
        {
            get => setSearchQuery ??= new RelayCommand(async obj =>
            {
                SearchQuery = obj as string;
            });
        }
        private void onTextEditUnfocused(object sender, FocusEventArgs e)
        {
            if (_vm.FoundCount > 0 && _vm.FoundCount < 6 && SearchQuery.Length > 5)
            {
                ApplicationState.Data.AddSearchPageQuery(SearchQuery.Capitilize());
            }
        }
        #endregion


        private ICommand close;
        private SearchPageViewModel _vm;


        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }


    }
}