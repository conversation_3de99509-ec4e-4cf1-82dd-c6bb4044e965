﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="Triggero.MauiMobileApp.Views.TestsView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:library="clr-namespace:Triggero.MauiMobileApp.Views"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this">
    <ContentView.Content>

        <Grid
            Padding="0"
            RowSpacing="0">

            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <LinearGradientBrush.GradientStops>
                        <GradientStop Offset="0" Color="#FFFFFF" />
                        <GradientStop Offset="1.0" Color="#FDCE72" />
                    </LinearGradientBrush.GradientStops>
                </LinearGradientBrush>
            </Grid.Background>

            <Grid.RowDefinitions>
                <RowDefinition Height="150" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>


            <Grid Grid.Row="0">

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*" />
                    <ColumnDefinition Width="1*" />
                </Grid.ColumnDefinitions>


                <Label
                    Margin="20,0,0,30"
                    HorizontalOptions="Start"
                    Style="{x:StaticResource StyleHeaderText}"
                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Tests.KnowYourself}"
                    VerticalOptions="End" />


                <Grid Grid.Column="1">

                    <StackLayout
                        Margin="0,0,20,39"
                        HorizontalOptions="End"
                        Orientation="Horizontal"
                        Spacing="24"
                        VerticalOptions="End">
                        
                        <ImageButton
                            BackgroundColor="Transparent"
                            Command="{Binding Source={x:Reference this}, Path=GoToSearch}"
                            HeightRequest="20"
                            HorizontalOptions="Center"
                            Source="search.png"
                            VerticalOptions="Center"
                            WidthRequest="20" />
                        
                        <ImageButton
                            BackgroundColor="Transparent"
                            Command="{Binding Source={x:Reference this}, Path=GoToFavorites}"
                            HeightRequest="20"
                            HorizontalOptions="Center"
                            Source="likeset.png"
                            VerticalOptions="Center"
                            WidthRequest="20" />
                    </StackLayout>

                </Grid>

            </Grid>


            <library:TestsCategoriesDrawn
                Grid.Row="1"
                HorizontalOptions="Fill"
                VerticalOptions="Fill" />

        </Grid>
    </ContentView.Content>
</ContentView>