﻿using DrawnUi.Views;

namespace Triggero.MauiMobileApp.Controls;

public class SvgCachedImage : Canvas
{
    public SkiaSvg Svg { get; protected set; }

    public static readonly BindableProperty SourceProperty = BindableProperty.Create(nameof(Source),
        typeof(string),
        typeof(SvgCachedImage),
        string.Empty,
        propertyChanged: ApplySourceProperty);

    private static void ApplySourceProperty(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is SvgCachedImage control)
        {
            control.Svg.Source = newValue as string;
        }
    }

    public string Source
    {
        get { return (string)GetValue(SourceProperty); }
        set { SetValue(SourceProperty, value); }
    }

    public SvgCachedImage()
    {
        Svg = new SkiaSvg();
        Content = Svg;
        Svg.Success += SvgOnSuccess;
        Svg.Error += SvgOnError;
    }

    public SvgCachedImage(string source)
    {
        Svg = new SkiaSvg();
        Content = Svg;
        Svg.Source = source;
        Svg.Success += SvgOnSuccess;
        Svg.Error += SvgOnError;
    }

    private void SvgOnError(object? sender, Exception e)
    {
        Error?.Invoke(this, e);
    }

    private void SvgOnSuccess(object? sender, string e)
    {
        Success?.Invoke(this, e);
    }

    /// <summary>
    /// Happens when loaded fine from `Source`. Will pass source as string.
    /// </summary>
    public event EventHandler<string> Success;

    /// <summary>
    /// Happens when loaded with error from `Source`. Will pass exception.
    /// </summary>
    public event EventHandler<Exception> Error;

}