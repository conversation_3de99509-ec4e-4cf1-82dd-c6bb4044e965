﻿<?xml version="1.0" encoding="utf-8"?>

<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:parts="clr-namespace:Triggero.Controls.Parts"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage10"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <Grid VerticalOptions="Fill">

        <Image
            VerticalOptions="Fill"
            HorizontalOptions="Fill"
            Aspect="Fill"
            Source="tutorialblur3.png" />

        <ScrollView VerticalOptions="Fill">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="340" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="140" />
                </Grid.RowDefinitions>


                <Grid Grid.Row="0" Padding="10,0">

                    <Grid
                        VerticalOptions="End"
                        HeightRequest="300">

                        <Image
                            Aspect="Fill"
                            Margin="-6,0,-13,0"
                            Source="tutorialtestscontainer.png" />

                        <ScrollView
                            Margin="20,10,20,13"
                            HorizontalScrollBarVisibility="Never"
                            VerticalScrollBarVisibility="Never"
                            InputTransparent="True"
                            IsEnabled="False">
                            <StackLayout
                                Spacing="12"
                                x:Name="testsLayout">

                            </StackLayout>
                        </ScrollView>

                    </Grid>


                </Grid>

                <Grid Grid.Row="1" Padding="10,0">

                    <StackLayout
                        Margin="20,0,20,0">

                        <parts:TransparentFooter
                            IsTestsPageSelected="True"
                            Margin="0,10,0,0"
                            HorizontalOptions="Fill"
                            InputTransparent="False" />


                        <Label
                            Margin="0,35,0,0"
                            TextColor="#000000"
                            FontAttributes="Bold"
                            FontSize="19"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="317"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage10.Header}" />
                        <Label
                            Margin="0,0,0,0"
                            TextColor="{x:StaticResource ColorText}"
                            FontSize="16"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="270"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage10.Description}" />

                    </StackLayout>

                </Grid>


                <Grid Grid.Row="2">
                    <Button
                        Command="{Binding Source={x:Reference this},Path=GoNext}"
                        VerticalOptions="Start"
                        HorizontalOptions="Fill"
                        Margin="63,0,63,0"
                        Style="{x:StaticResource yellow_btn}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage10.GoNext}" />

                </Grid>

            </Grid>
        </ScrollView>
    </Grid>
</ContentPage>