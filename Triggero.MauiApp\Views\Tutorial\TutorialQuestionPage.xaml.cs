﻿ 
using Triggero.Controls.Cards.Tests.Questions;
 
using Triggero.Models.Tests;
using Triggero.Models.Tests.Questions;
using Triggero.MauiMobileApp.Views.Pages.Auth;

using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiPort.Models;

namespace Triggero.MauiMobileApp.Views.Pages.Tests
{

    public partial class TutorialQuestionPage : ContentPage
    {
        private Test _test;
        private int _questionNumber;


        public static SortedSet<string> NeedToHandleItems = new SortedSet<string>();
        public TutorialQuestionPage(Test test, int questionNumber)
        {
            _test = test;
            _questionNumber = questionNumber;

            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            numberLabel.Text = $"{_questionNumber}/{test.Questions.Count}";

            skipLabel.IsVisible = _questionNumber == 4;
            if (_questionNumber == 4)
            {
                _test.Questions[_questionNumber - 1].MultipleAnswers = true;
            }

            Render();


        }


        private BaseQuestionView questionView = null;

        private void Render()
        {
            var question = _test.Questions[_questionNumber - 1];

            if (question is SimpleQuestion)
            {
                questionView = new SimpleQuestionView(question);
            }
            else if (question is PictureQuestion)
            {
                questionView = new PicturesQuestionView(question);
            }
            else if (question is SimplePicturedQuestion)
            {
                questionView = new QuestionWithPictureView(question);
            }

            testQuestionViewLayout.Children.Add(questionView);
        }


        private RelayCommand goNext;
        public RelayCommand GoNext
        {
            get => goNext ??= new RelayCommand(async obj =>
            {
                if (!ValidateQuestion()) return;


                var selectedOptions = questionView.GetSelectedOptions();
                foreach (var option in selectedOptions)
                {
                    var tags = option.Tags.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                    for (int i = 0; i < tags.Length; i++)
                    {
                        tags[i] = tags[i].Trim();
                        NeedToHandleItems.Add(tags[i]);
                    }
                }




                if (_questionNumber + 1 <= _test.Questions.Count)
                {
                    App.OpenPage(new TutorialQuestionPage(_test, _questionNumber + 1));
                }
                else
                {

                    App.OpenPage(new TutorialCompletedPage());
                }
            });
        }

        private RelayCommand skip;
        public RelayCommand Skip
        {
            get => skip ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new TutorialCompletedPage());
            });
        }


        private bool ValidateQuestion()
        {
            var selectedOptions = questionView.GetSelectedOptions();
            return selectedOptions.Any();
        }
    }
}