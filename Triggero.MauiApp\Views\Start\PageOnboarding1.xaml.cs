﻿
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Controls;
using Triggero.MauiMobileApp.Extensions.Helpers;
using RelayCommand = Triggero.MauiMobileApp.Extensions.Helpers.RelayCommand;


namespace Triggero.MauiMobileApp.Views.Pages.Start
{

    public partial class PageOnboarding1 : ContentPage
    {
        public PageOnboarding1()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        protected override void OnAppearing()
        {
            var animatedImage = new GifCachedImage
            {
                HeightRequest = 269,
                WidthRequest = 242,
                Opacity = 0,

                VerticalOptions = LayoutOptions.Start,
                HorizontalOptions = LayoutOptions.Center,
                //Aspect = Aspect.AspectFit,
                Source = "animationstrenght.gif"
            };
            animatedLayout.Children.Insert(0, animatedImage);

            animatedImage.Success += async (sender, e) =>
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await Task.Delay(500);
                    animatedImage.Opacity = 1;
                    notAnimatedImg.Opacity = 0;
                });
            };

        }

        private RelayCommand goNext;
        public RelayCommand GoNext
        {
            get => goNext ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new PageOnboarding2());
            });
        }
    }
}