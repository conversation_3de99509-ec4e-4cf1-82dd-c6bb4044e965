﻿using Triggero.Models.Practices;
using Triggero.Models.Tests;

namespace Triggero.Models
{
    /// <summary>
    /// Рекомендация на сегодня. Будет что-то одно. Остальное будет == null
    /// </summary>
    public class RecommendationModel
    {
        public RecommendationModel()
        {

        }
        public RecommendationModel(object obj)
        {
            if (obj is Test test) Test = test;
            else if (obj is Practice practice) Practice = practice;
            else if (obj is Topic topic) Topic = topic;
            else if (obj is Exercise exercise) Exercise = exercise;
        }

        public Test Test { get; set; }
        public Practice Practice { get; set; }
        public Topic Topic { get; set; }
        public Exercise Exercise { get; set; }

        public RecomendationType GetRecommendationType()
        {
            if (Test != null) return RecomendationType.Test;
            if (Practice != null) return RecomendationType.Practice;
            if (Topic != null) return RecomendationType.Topic;
            if (Exercise != null) return RecomendationType.Exercise;
            return RecomendationType.Unknown;
        }

        public IElementDetails GetModel()
        {
            if (Test != null) return Test;
            if (Practice != null) return Practice;
            if (Topic != null) return Topic;
            if (Exercise != null) return Exercise;
            return null;
        }

    }
}
