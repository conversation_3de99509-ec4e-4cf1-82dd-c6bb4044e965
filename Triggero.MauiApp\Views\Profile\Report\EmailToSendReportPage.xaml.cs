﻿
using System.Windows.Input;


namespace Triggero.MauiMobileApp.Views.Pages.Profile.Report
{

    public partial class EmailToSendReportPage : ContentPage
    {
        public EmailToSendReportPage(string email)
        {
            Email = email;

            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            Disappearing += EmailToSendReportPage_Disappearing;
        }


        private string email = "";
        public string Email
        {
            get { return email; }
            set { email = value; OnPropertyChanged(nameof(Email)); }
        }
        private ICommand setEmail;
        public ICommand SetEmail
        {
            get => setEmail ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }



        public event EventHandler<string> EmailSelected;
        private void EmailToSendReportPage_Disappearing(object sender, EventArgs e)
        {
            EmailSelected?.Invoke(this, Email);
        }
    }
}