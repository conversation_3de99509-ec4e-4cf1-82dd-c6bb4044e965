﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
             x:Class="Triggero.SplashPage">
    <ContentPage.Content>
        <Grid>
            <Image
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightbluegradientbg.png"/>


            <StackLayout
                Spacing="30"
                x:Name="xxxLayout"
                VerticalOptions="Center"
                HorizontalOptions="Center">
                
               
                <Grid 
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    HeightRequest="130"
                    WidthRequest="130">

                    <controls:GifCachedImage
                        Source="logoanimated.gif"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        HeightRequest="130"
                        WidthRequest="130"/>

                </Grid>


                <Image 
                    Source="logotitleyellow.png"
                    Aspect="Fill"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    HeightRequest="30"
                    WidthRequest="220"/>




            </StackLayout>

            <!--WARNING VPN-->
            <Frame
                CornerRadius="12"
                BackgroundColor="#FDCE72"
                HeightRequest="48"
                Margin="20,0,20,66"
                HorizontalOptions="Fill"
                VerticalOptions="End"
                Padding="0"
                HasShadow="False">

                <Label
                    WidthRequest="220"
                    TextColor="{x:StaticResource ColorText}"
                    HorizontalTextAlignment="Center"
                    VerticalOptions="Center"
                    HorizontalOptions="Center"
                    FontSize="12"
                    Text="Не забудьте отключить VPN для корректной работы приложения"/>

            </Frame>
            
        </Grid>
    </ContentPage.Content>
</ContentPage>