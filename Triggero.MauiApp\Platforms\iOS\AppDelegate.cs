﻿using Foundation;
using UIKit;
using UserNotifications;

namespace Triggero.MauiMobileApp
{
    [Register("AppDelegate")]
    public class AppDelegate : MauiUIApplicationDelegate
    {
        protected override MauiApp CreateMauiApp() => MauiProgram.CreateMauiApp();

        public override bool FinishedLaunching(UIApplication application, NSDictionary launchOptions)
        {
            var result = base.FinishedLaunching(application, launchOptions);

            // Check if App was opened by Push Notification
            if (launchOptions != null)
            {
                var keyName = new NSString("UIApplicationLaunchOptionsRemoteNotificationKey");
                if (launchOptions.ContainsKey(keyName))
                {
                    var pushOptions = launchOptions.ObjectForKey(keyName) as NSDictionary;
                    if (pushOptions != null)
                    {
                        NSError error;
                        var json = NSJsonSerialization.Serialize(pushOptions, 0, out error);
                        Console.WriteLine($"[PUSH] App launched from notification: {json}");

                        // TODO: Handle app launch from notification
                        // You can parse the notification data and navigate to specific screens
                    }
                }
            }

            return result;
        }

    }
}
