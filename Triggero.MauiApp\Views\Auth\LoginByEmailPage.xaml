﻿<?xml version="1.0" encoding="utf-8"?>

<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Auth.LoginByEmailPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="this">
    <ContentPage.Content>
        <Grid>


            <Image
                Aspect="Fill"
                HorizontalOptions="Fill"
                Source="lightbluegradientbg.png"
                VerticalOptions="Fill" />

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="200" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>


                <Grid Grid.Row="0">

                    <ImageButton
                        Margin="20"
                        BackgroundColor="Transparent"
                        Command="{Binding Source={x:Reference this}, Path=Close}"
                        CornerRadius="0"
                        HeightRequest="56"
                        HorizontalOptions="Start"
                        Source="buttonbackbordered.png"
                        VerticalOptions="Center"
                        WidthRequest="56" />

                    <Label
                        Margin="20,0,0,20"
                        FontAttributes="Bold"
                        FontSize="22"
                        HorizontalOptions="Start"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.LoginByEmail}"
                        TextColor="{x:StaticResource ColorText}"
                        VerticalOptions="End" />

                </Grid>

                <pancakeview:PancakeView
                    Grid.Row="1"
                    Padding="0"
                    BackgroundColor="#FFFFFF"
                    StrokeShape="RoundRectangle 15,15,0,0">
                    <Grid>
                        <StackLayout
                            Margin="20,20,20,0"
                            Spacing="0">

                            <Label
                                FontSize="14"
                                HorizontalOptions="Start"
                                Opacity="0.5"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.Email}"
                                TextColor="{x:StaticResource ColorText}"
                                VerticalOptions="Start" />

                            <!--todo-->
                            <!--
                                CharacterCasing="Lower"
                            -->
                            <Grid
                                VerticalOptions="Center"
                                Margin="0,10,0,0"
                                HeightRequest="50">
                                <draw:Canvas HorizontalOptions="Fill" VerticalOptions="Fill">

                                    <draw:SkiaShape
                                        HorizontalOptions="Fill" VerticalOptions="Fill"
                                        UseCache="Operations"
                                        StrokeColor="#DEEAF6"
                                        StrokeWidth="1.5"
                                        CornerRadius="16" />

                                </draw:Canvas>

                                <Entry
                                    Margin="10,0"
                                    Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.EnterEmail}"
                                    HorizontalOptions="Fill"
                                    Style="{x:StaticResource grayTextEdit}"
                                    Text="{Binding Source={x:Reference this}, Path=Email, Mode=TwoWay}"
                                    VerticalOptions="Fill" />


                            </Grid>

                            <Label
                                Margin="0,20,0,0"
                                FontSize="14"

                                HorizontalOptions="Start"
                                Opacity="0.5"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.Password}"
                                TextColor="{x:StaticResource ColorText}"
                                VerticalOptions="Start" />

                            <Grid
                                Margin="0,10,0,0"
                                HeightRequest="50">
                                <draw:Canvas HorizontalOptions="Fill" VerticalOptions="Fill">

                                    <draw:SkiaShape
                                        HorizontalOptions="Fill" VerticalOptions="Fill"
                                        UseCache="Operations"
                                        StrokeColor="#DEEAF6"
                                        StrokeWidth="1.5"
                                        CornerRadius="16" />

                                </draw:Canvas>

                                <Entry
                                    IsPassword="True"
                                    Margin="10,0"
                                    HorizontalOptions="Fill"
                                    Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.EnterPassword}"
                                    Style="{x:StaticResource grayTextEdit}"
                                    Text="{Binding Source={x:Reference this}, Path=Password, Mode=TwoWay}"
                                    VerticalOptions="Fill" />

                            </Grid>

                            <Grid
                                Margin="0,32,0,0"
                                HeightRequest="60">

                                <Button
                                    Command="{Binding Source={x:Reference this}, Path=CommandLoginByEmail}"
                                    IsEnabled="{Binding Source={x:Reference this}, Path=IsBusy, Converter={x:StaticResource NotConverter}}"
                                    Style="{x:StaticResource yellow_btn}"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.SignIn}"
                                    VerticalOptions="Center" />

                                <ActivityIndicator
                                    HorizontalOptions="Center"
                                    IsRunning="{Binding Source={x:Reference this}, Path=IsBusy}"
                                    IsVisible="{Binding Source={x:Reference this}, Path=IsBusy}"
                                    VerticalOptions="Center" />

                            </Grid>

                            <StackLayout
                                Margin="0,28,0,0"
                                HorizontalOptions="Center"
                                Orientation="Horizontal"
                                Spacing="12">
                                <Label
                                    FontSize="14"

                                    Opacity="0.5"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.ForgotPassword}"
                                    TextColor="{x:StaticResource ColorText}" />

                                <Label
                                    FontAttributes="Bold"
                                    FontSize="14"

                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginByEmail.Restore}"
                                    TextColor="{x:StaticResource ColorText}"
                                    TextDecorations="Underline">
                                    <Label.GestureRecognizers>
                                        <TapGestureRecognizer
                                            Command="{Binding Source={x:Reference this}, Path=GoToRestorePage}" />
                                    </Label.GestureRecognizers>
                                </Label>
                            </StackLayout>

                            <Label
                                x:Name="errorLabel"
                                Margin="0,24,0,0"
                                FontSize="14"

                                HorizontalOptions="Center"
                                IsVisible="False"
                                Opacity="0.5"
                                Text=""
                                TextColor="{x:StaticResource ColorPrimary}"
                                VerticalOptions="Start" />

                        </StackLayout>
                    </Grid>
                </pancakeview:PancakeView>


            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>