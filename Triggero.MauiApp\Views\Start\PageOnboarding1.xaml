﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Start.PageOnboarding1"
             xmlns:app="clr-namespace:Triggero"
             
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        <Grid>


            <Image
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightbluegradientbg.png"/>

            <StackLayout
                Spacing="0"
                VerticalOptions="Center">

                <!--<Image
                    WidthRequest="242"
                    HeightRequest="269"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    Source="startperson.png"/>-->

                <!--IMAGE-->
                <Grid
                    x:Name="animatedLayout"
                    WidthRequest="242"
                    HeightRequest="269"
                    VerticalOptions="Start"
                    HorizontalOptions="Center">
                    
                    <Image
                        x:Name="notAnimatedImg"
                        WidthRequest="242"
                        HeightRequest="269"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        Source="animationstrenght.png"/>
                    
                </Grid>

                <!--Знакомьтесь, это Triggero!-->
                <Label 
                    Margin="0,40,0,0"
                    TextColor="{x:StaticResource ColorText}"
                    FontSize="22"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Start.HelloPage.Header}"/>


                <StackLayout
                    Spacing="1"
                    HorizontalOptions="Center"
                    Margin="0,20,0,0">

                    <Label 
                        TextColor="{x:StaticResource ColorText}"
                        Opacity="0.5"
                        FontSize="17"
                        VerticalOptions="Start"
                        Margin="50,0"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Start.HelloPage.Text}"/>

                </StackLayout>

                <ImageButton 
                    Command="{Binding Source={x:Reference this},Path=GoNext}"
                    Margin="0,90,0,0"
                    WidthRequest="92"
                    HeightRequest="64"
                    HorizontalOptions="Center"
                    VerticalOptions="Start"
                    CornerRadius="0"
                    BackgroundColor="Transparent"
                    Source="yellowcontinuebtn.png"/>


            </StackLayout>
            
            
            
            

        </Grid>
    </ContentPage.Content>
</ContentPage>