﻿using Triggero.Controls.Cards.Tests.Questions.Answers.Single;
using Triggero.MauiPort.Models;
using Triggero.Models.Abstractions;
using Triggero.Models.Tests.Questions;

namespace Triggero.Controls.Cards.Tests.Questions
{

    public partial class QuestionWithPictureView : BaseQuestionView
    {
        public QuestionWithPictureView(Question question) : base(question)
        {
            InitializeComponent();


            if (question.MultipleAnswers)
            {
                questiontypeLabel.Text = App.This.Interface.Tests.MaySelectMultiple;
            }
            else
            {
                questiontypeLabel.Text = App.This.Interface.Tests.MaySelectSingle;
            }

            Render();
        }


        private async void Render()
        {
            img.Source = await ResorcesHelper.GetImageSource((Question as SimplePicturedQuestion).ImgPath);
            titleLabel.Text = Question.GetLocalizedText(LanguageHelper.LangCode);

            optionsLayout.Children.Clear();
            if (Question.MultipleAnswers)
            {
                Question.Options.ForEach(o =>
                {
                    var control = new MultipleTextAnswerOption(o)
                    {
                        HeightRequest = 64
                    };
                    control.OnCheckedChanged += OnOptionCheckedChanged;
                    optionsLayout.Children.Add(control);
                });
            }
            else
            {
                Question.Options.ForEach(o =>
                {
                    var control = new TextAnswerOption(o)
                    {
                        HeightRequest = 64
                    };
                    optionsLayout.Children.Add(control);
                });
            }
        }

        int selectedCount = 0;
        private void OnOptionCheckedChanged(object sender, EventArgs e)
        {
            var option = sender as BaseQuestionOptionView;

            if (option.IsChecked)
            {
                if (selectedCount < 3)
                {
                    selectedCount++;
                }
                else
                {
                    option.IsChecked = false;
                }
            }
            else
            {
                selectedCount--;
            }
        }


        public override List<QuestionOption> GetSelectedOptions()
        {
            var options = new List<QuestionOption>();

            var selectedCards = optionsLayout.Children.Cast<BaseQuestionOptionView>().Where(o => o.IsChecked).ToList();
            selectedCards.ForEach(o => options.Add(o.Option));

            return options;
        }
    }
}