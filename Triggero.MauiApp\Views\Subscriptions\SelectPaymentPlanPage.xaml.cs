﻿using MobileAPIWrapper;

using System.Linq;
using System.Windows.Input;
using Triggero.Controls;
using Triggero.Models.General;
using Triggero.Models.General.UserData;

namespace Triggero.MauiMobileApp.Views.Pages.Subscriptions
{

    //SPPage = SelectPlanPage

    public partial class SelectPaymentPlanPage : ContentPage
    {


        public SelectPaymentPlanPage()
        {
            InitializeComponent();

            NavigationPage.SetHasNavigationBar(this, false);

            Load();
        }


        #region Render

        private async void Load()
        {
            var plans = await ApplicationState.Data.GetPlans();
            var monthPlan = plans.FirstOrDefault(o => o.BuiltInPlanType == BuiltInPlanType.Month);
            var priceWithDiscount = monthPlan.Price - (monthPlan.Price / 100 * monthPlan.Discount);

            fullAccessBtn.Text = string.Format(App.This.Interface.Subscriptions.SelectSubscription.FullAccessFrom, priceWithDiscount);
            partialAccessBtn.Text = string.Format(App.This.Interface.Subscriptions.SelectSubscription.PartialAccessFrom, 99);

            //if(Device.RuntimePlatform == Device.iOS)
            //{
            //    yourChoisePlanContainer.IsVisible = !(await TriggeroMobileAPI.Common.CheckHui());
            //}

            //if (
            //    !AuthHelper.User.UserSubscription.IsSubscriptionEnded && AuthHelper.User.UserSubscription.SubscriptionType != SubscriptionType.Full || AuthHelper.CheatFreeVersion)
            //{
            //    fullPlanContainer.IsVisible = false;
            //}
            
            RenderPaymentData();

            GlobalEvents.UserPropertyChanged += GlobalEvents_UserPropertyChanged;
        }
        private async void GlobalEvents_UserPropertyChanged(object sender, User e)
        {
            RenderPaymentData();
        }

        public ICommand CommandExternalPayment
        {
            get
            {
                return new Command((context) =>
                {
                    PlatformUi.Instance.Command("ExternalPaymentLink");

                    App.GoBack();
                });
            }
        }

        void RenderPaymentOptions(bool canPay, bool showCard)
        {

            if (AuthHelper.User == null || AuthHelper.User.UserSubscription == null)
                return;

            if (Device.RuntimePlatform == Device.iOS)
            {
                fullAccessBtn.IsVisible = false;
                partialAccessBtn.IsVisible = false;
                cardNumberLabel.IsVisible = false;
                unbindCardLabel.IsVisible = false;

                if (canPay && !Constants.IsFreeVersion)
                {
                    LabelChooseYourPlan.IsVisible = false;
                    fullPlanContainer.IsVisible = true;
                    yourChoisePlanContainer.IsVisible = false;
                    LabelExternalLink.IsVisible = true;
                }
            }
            else
            {
                RenderPlanOptions();

                if (canPay && !Constants.IsFreeVersion)
                {
                    fullAccessBtn.IsVisible = true;
                    partialAccessBtn.IsVisible = true;

                    LabelChooseYourPlan.IsVisible = true;
                    fullPlanContainer.IsVisible = true;
                    yourChoisePlanContainer.IsVisible = true;
                }
                else
                {
                    fullAccessBtn.IsVisible = false;
                    partialAccessBtn.IsVisible = false;
                }

                cardNumberSpan.Text = AuthHelper.User.UserSubscription.BindedCardNumber;
                cardNumberLabel.IsVisible = showCard && AuthHelper.User.UserSubscription.AllowRecurrent && !string.IsNullOrEmpty(AuthHelper.User.UserSubscription.BindedCardNumber);
                unbindCardLabel.IsVisible = showCard && AuthHelper.User.UserSubscription.AllowRecurrent && !string.IsNullOrEmpty(AuthHelper.User.UserSubscription.BindedCardNumber);
            }

        }

        private async void RenderPaymentData()
        {
            if (AuthHelper.User == null || AuthHelper.User.UserSubscription == null)
                return;

            subscriptionDataFrame.IsVisible = true;
            bool canPay = false;

            bool showCreditCardInfo = Device.RuntimePlatform == Device.Android;

            if (AuthHelper.UserSubscription == SubscriptionType.Trial)
            {
                tarifNameLabel.Text = "Пробный период";
                activeBeforeLabel.IsVisible = false;
                cardNumberLabel.IsVisible = false;
                unbindCardLabel.IsVisible = false;
                RenderPaymentOptions(true, false);
                return;
            }

            activeBeforeLabel.IsVisible = true;
            switch (AuthHelper.UserSubscription)
            {
                case SubscriptionType.None:
                tarifNameLabel.Text = "Не активирована";
                fullPlanContainer.IsVisible = true;
                canPay = true;
                break;

                //case SubscriptionType.Trial:
                //tarifNameLabel.Text = "Пробный";
                //break;

                case SubscriptionType.Full:
                tarifNameLabel.Text = "Полный доступ";
                fullPlanContainer.IsVisible = true;
                yourChoisePlanContainer.IsVisible = AuthHelper.User.UserSubscription.IsSubscriptionEnded;
                break;

                case SubscriptionType.Custom:
                tarifNameLabel.Text = "Выбранные опции";

                if (AuthHelper.User.UserSubscription.UserSubsctiptionOptions.Any())
                {
                    tarifNameLabel.Text += ": ";
                    var index = 0;
                    foreach (UserSubsctiptionOption option in AuthHelper.User.UserSubscription.UserSubsctiptionOptions)
                    {
                        if (index > 0)
                            tarifNameLabel.Text += ", ";
                        tarifNameLabel.Text += option.PlanOption.Title;
                        index++;
                    }
                }

                fullPlanContainer.IsVisible = AuthHelper.User.UserSubscription.IsSubscriptionEnded;
                yourChoisePlanContainer.IsVisible = AuthHelper.User.UserSubscription.IsSubscriptionEnded;
                break;
            }

            if (AuthHelper.User.UserSubscription.IsSubscriptionEnded)
            {
                canPay = true;
                if (AuthHelper.User.UserSubscription.SubscriptionType != SubscriptionType.None)
                {
                    activeBeforeLabel.Text = "Подписка закончилась";
                }
                else
                {
                    activeBeforeLabel.IsVisible = false;
                }
            }
            else
            {
                activeBeforeLabel.Text = "Активна до: " + AuthHelper.User.UserSubscription.SubscriptionBefore.ToLocalTime().ToString("dd.MM.yyyy HH:mm");

                if (AuthHelper.User.UserSubscription.SubscriptionType != SubscriptionType.Full)
                {
                    //upgrade?
                    canPay = true;
                    fullPlanContainer.IsVisible = true;
                    yourChoisePlanContainer.IsVisible = true;
                }
                else
                {
                    canPay = false;
                }
            }

            RenderPaymentOptions(canPay, showCreditCardInfo);
        }

        private RelayCommand unbindCard;
        public RelayCommand UnbindCard
        {
            get => unbindCard ??= new RelayCommand(async obj =>
            {

                await TriggeroMobileAPI.Payment.UnbindCard(AuthHelper.User.Id);

                unbindCardLabel.IsVisible = false;
                cardNumberSpan.Text = "Не привязана";
            });
        }



        private async void RenderPlanOptions()
        {
            if (AuthHelper.User == null || AuthHelper.User.UserSubscription == null)
                return;

            planOptionsLayout.Children.Clear();

            var options = await ApplicationState.Data.GetPlansOptions();
            foreach (var option in options)
            {
                var card = new PlanOptionCard(option, false)
                {
                    HeightRequest = 83,
                    VerticalOptions = LayoutOptions.Start,
                    InputTransparent = true,
                };
                card.SelectionChanged += Card_SelectionChanged;
                planOptionsLayout.Children.Add(card);
            }

        }

        private void Card_SelectionChanged(object sender, bool e)
        {
            double price = 0;
            foreach (PlanOptionCard card in planOptionsLayout.Children)
            {
                if (card.IsSelected)
                {
                    price += card.Option.Price;
                }
            }

            partialAccessBtn.Text = string.Format(App.This.Interface.Subscriptions.SelectSubscription.PartialAccessFrom, (int)price);

        }
        #endregion

        private RelayCommand goToCustomPlan;
        public RelayCommand GoToCustomPlan
        {
            get => goToCustomPlan ??= new RelayCommand(async obj =>
            {
                var selectionsModel = new CustomPlanSelectionsModel();

                bool anySelected = false;

                foreach (PlanOptionCard card in planOptionsLayout.Children)
                {
                    if (card.IsSelected)
                    {
                        anySelected = true;
                        switch (card.Option.Type)
                        {
                            case PlanOptionType.Library:
                            selectionsModel.IsLibrarySelected = true;
                            break;
                            case PlanOptionType.ChatBot:
                            selectionsModel.IsChatBotSelected = true;
                            break;
                            case PlanOptionType.MoodTracker:
                            selectionsModel.IsMoodTrackerSelected = true;
                            break;
                            case PlanOptionType.Tests:
                            selectionsModel.IsTestsSelected = true;
                            break;
                        }
                    }
                }

                App.OpenPage(new BuyCustomPlanPage(selectionsModel));
            });
        }
        private RelayCommand goToFullPlan;
        public RelayCommand GoToFullPlan
        {
            get => goToFullPlan ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new BuyPlanPage());


                //if (Device.RuntimePlatform == Device.iOS)
                //{
                //    //App.OpenPage(new BuyPlanPageAppstore());
                //}
                //else
                //{

                //}

            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
    }
}