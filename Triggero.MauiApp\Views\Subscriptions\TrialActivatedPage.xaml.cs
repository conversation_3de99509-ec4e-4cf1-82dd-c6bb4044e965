﻿
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Controls;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Views.Pages.Start;
using RelayCommand = Triggero.MauiMobileApp.Extensions.Helpers.RelayCommand;


namespace Triggero.MauiMobileApp.Views.Pages.Subscriptions
{
    //TAPage = TrialActivatedPage

    public partial class TrialActivatedPage : ContentPage
    {
        public TrialActivatedPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        protected override async void OnAppearing()
        {

            MainThread.BeginInvokeOnMainThread(() =>
            {

                var animatedImage = new GifCachedImage
                {
                    HeightRequest = 200,
                    WidthRequest = 180,
                    Opacity = 0,

                    VerticalOptions = LayoutOptions.Start,
                    HorizontalOptions = LayoutOptions.Center,
                    //Aspect = Aspect.AspectFit,
                    Source = "animationgirlwithheartsanim.gif"
                };
                animatedLayout.Children.Insert(0, animatedImage);

                animatedImage.Success += async (sender, e) =>
                {
                    await Task.Delay(500);
                    animatedImage.Opacity = 1;
                    notAnimatedImg.Opacity = 0;
                };
            });

        }

        private RelayCommand goToTutorial;
        public RelayCommand GoToTutorial
        {
            get => goToTutorial ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new DisclaimerPage());
            });
        }


    }
}