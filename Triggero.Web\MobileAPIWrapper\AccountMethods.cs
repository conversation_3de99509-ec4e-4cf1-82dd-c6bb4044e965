﻿using Newtonsoft.Json;

using System.Net;
using MobileAPIWrapper.Helpers;
using Triggero.Domain.Models;
using Triggero.Domain.Models.Dto;
using Triggero.Models.General;


namespace MobileAPIWrapper.Methods;

public class AccountMethods
{
    public async Task<LoginResponseDto?> Register(RegisterUserDto dto)
    {
        string url = TriggeroMobileAPI.AddBaseUrl($"/Account/Register");

        var model = await RequestHelper.ExecuteRequestReceiveModelAsync<LoginResponseDto>(url, Method.Post, dto);

        return model;
    }

    public async Task<bool> RequestSms(string phone)
    {
        string url = TriggeroMobileAPI.AddBaseUrl($"/Account/RequestSms?phone={phone}");
        var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);
        return response.StatusCode == HttpStatusCode.OK;
    }

    public async Task<LoginResponseDto?> LoginWithPhone(string phone, string code)
    {
        string url = TriggeroMobileAPI.AddBaseUrl($"/Account/LoginWithPhone");

        var dto = new LoginWithPhoneNumberDto
        {
            Code = code,
            PhoneNumber = phone
        };

        var model = await RequestHelper.ExecuteRequestReceiveModelAsync<LoginResponseDto>(url, Method.Post, dto);

        return model;
    }

    public async Task<LoginResponseDto?> LoginWithEmail(string email, string password)
    {
        string url = TriggeroMobileAPI.AddBaseUrl($"/Account/LoginWithEmail");

        var dto = new LoginWithEmailDto
        {
            Email = email,
            Password = password
        };

        var model = await RequestHelper.ExecuteRequestReceiveModelAsync<LoginResponseDto>(url, Method.Post, dto);

        return model;
    }

    public async Task<UserDataDto?> MyProfile()
    {
        string url = TriggeroMobileAPI.AddBaseUrl($"/Account/MyProfile");

        var model = await RequestHelper.ExecuteRequestReceiveModelAsync<UserDataDto>(url, Method.Get);

        return model;
    }

    public async Task<User?> MyProfileFull()
    {
        string url = TriggeroMobileAPI.AddBaseUrl($"/Account/MyData");

        var model = await RequestHelper.ExecuteRequestReceiveModelAsync<User>(url, Method.Get);

        return model;
    }

}