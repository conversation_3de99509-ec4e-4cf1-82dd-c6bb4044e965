﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Triggero.Controls.Chat;
using Triggero.Domain.Models;
using Triggero.Domain.Models.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Messengers.Support;

namespace Triggero.MauiMobileApp.Views.Pages
{
    public partial class SupportPage : BasePage
    {
        public SupportPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
            Load();

            Disappearing += SupportPage_Disappearing;
        }

        public override double OnKeyboardResized(double size)
        {
            rowKeyboard.Height = size;

            return base.OnKeyboardResized(size);
        }

        #region Init

        private async void Load()
        {
            RenderChat();

            if (ConnectionHelper.HasInternet())
            {
                ApplicationState.Messengers.SupportLongPollingGotResponse += Messengers_SupportLongPollingGotResponse;
                ApplicationState.Messengers.StartSupportLongPolling(renderedMessages.Count);
            }
            else
            {
                await <PERSON>splay<PERSON><PERSON>("Ошибка", "Нет подключения к интернету", "Ок");
            }
        }

        private void Messengers_SupportLongPollingGotResponse(object sender, SupportChatLongPollItem e)
        {
            if (e.Type == SupportChatLongPollType.NewMessage)
            {
                foreach (var msg in e.Messages)
                {
                    //Избегаем повторного рендеринга сообщения
                    if (!renderedMessages.Any(o => o.GlobalId == msg.GlobalId))
                    {
                        RenderMessage(msg);
                    }
                }
            }
        }

        private void SupportPage_Disappearing(object sender, EventArgs e)
        {
            ApplicationState.Messengers.StopSupportLongPolling();
            ApplicationState.Messengers.SupportLongPollingGotResponse -= Messengers_SupportLongPollingGotResponse;
        }

        #endregion

        #region Render

        private List<SupportChatMessage> renderedMessages = new List<SupportChatMessage>();

        private async void RenderChat()
        {
            var chat = await ApplicationState.Messengers.GetSupportChat();
            messagesLayout.Children.Clear();

            foreach (var msgGroup in chat.Messages.GroupBy(o => o.CreatedAt.Date))
            {
                var label = new Label
                {
                    HeightRequest = 25,
                    FontSize = 14,
                    Text = msgGroup.Key.ToLocalTime().ToString("dd MMMM"),
                    HorizontalOptions = LayoutOptions.Center
                };
                messagesLayout.Children.Add(label);

                foreach (var msg in msgGroup)
                {
                    RenderMessage(msg);
                }
            }

            await Task.Delay(500);
            await ScrollToEnd();
        }

        private void RenderMessage(SupportChatMessage message)
        {
            renderedMessages.Add(message);

            View view = null;
            if (message.ChatSide == ChatSide.User)
            {
                view = new SupportChatRightTextMessage(message)
                {
                    HorizontalOptions = LayoutOptions.End,
                    Margin = new Thickness(150, 0, 0, 0)
                };
            }
            else if (message.ChatSide == ChatSide.Admin)
            {
                view = new SupportChatLeftTextMessage(message)
                {
                    HorizontalOptions = LayoutOptions.Start,
                    Margin = new Thickness(0, 0, 150, 0)
                };
            }

            messagesLayout.Children.Add(view);
        }

        public async Task ScrollToEnd()
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                var last = messagesLayout.Children.LastOrDefault() as Element;
                if (last != null)
                {
                    messagesScrollView.ScrollToAsync(last, ScrollToPosition.End, false);
                }
            });
        }

        #endregion

        #region Current message

        private string message;

        public string Message
        {
            get { return message; }
            set
            {
                message = value;
                OnPropertyChanged(nameof(Message));
            }
        }

        private ICommand sendMessage;

        public ICommand SendMessage
        {
            get => sendMessage ??= new RelayCommand(async obj =>
            {
                if (string.IsNullOrEmpty(message))
                    return;

                var msg = new SupportChatMessage
                {
                    Text = message,
                    CreatedAt = DateTime.UtcNow,
                    ChatSide = ChatSide.User,
                    SentAt = DateTime.UtcNow,
                };
                bool result = await ApplicationState.Messengers.SendSupportMessage(msg);
                if (!result)
                {
                    await DisplayAlert("Ошибка", "Нет подключения к интернету", "Ок");
                }
                else
                {
                    Message = "";
                    RenderMessage(msg);
                }
            });
        }

        #endregion

        private ICommand close;

        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj => { await App.Current.MainPage.Navigation.PopAsync(); });
        }


        private void textEditFocused(object sender, FocusEventArgs e)
        {
            //if (Device.RuntimePlatform == Device.iOS)
            //{
            //    sendMsgRowDefinition.Height = 400;
            //}
        }

        private void textEditUnfocused(object sender, FocusEventArgs e)
        {
            //if (Device.RuntimePlatform == Device.iOS)
            //{
            //    sendMsgRowDefinition.Height = 100;
            //}
        }
    }
}