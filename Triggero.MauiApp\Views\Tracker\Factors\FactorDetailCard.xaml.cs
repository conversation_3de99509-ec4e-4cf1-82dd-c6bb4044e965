﻿using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.MoodTracker;


namespace Triggero.Controls.Cards.Tracker.Factors
{

    public partial class FactorDetailCard : ContentView
    {
        public FactorDetailCard(FactorDetail factor)
        {
            FactorDetail = factor;
            InitializeComponent();

            Load();
        }
        private async void Load()
        {
            titleLabel.Text = FactorDetail.GetLocalizedTitle(LanguageHelper.LangCode);
            img.Source = await ResorcesHelper.GetImageSource(FactorDetail.ImgPath);
        }


        private FactorDetail factorDetail;
        public FactorDetail FactorDetail
        {
            get { return factorDetail; }
            set { factorDetail = value; OnPropertyChanged(nameof(FactorDetail)); }
        }


        #region Selection
        private bool isSelected;
        public bool IsSelected
        {
            get { return isSelected; }
            set { isSelected = value; OnPropertyChanged(nameof(IsSelected)); }
        }
        public event EventHandler<FactorDetail> Tapped;
        private void onTapped(object sender, EventArgs e)
        {
            Tapped?.Invoke(this, FactorDetail);
        }

        #endregion

    }
}