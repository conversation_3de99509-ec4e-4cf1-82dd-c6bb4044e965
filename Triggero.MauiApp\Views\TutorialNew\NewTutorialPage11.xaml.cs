﻿
using System.Linq;
using System.Windows.Input;


namespace Triggero.MauiMobileApp.Views
{

    public partial class NewTutorialPage11 : ContentPage
    {
        public NewTutorialPage11()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        private ICommand goNext;
        public ICommand GoNext
        {
            get => goNext ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new NewTutorialPage12());

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    if (App.Current.MainPage.Navigation.NavigationStack.Contains(this))
                    {
                        try
                        {
                            App.ClosePage(this);
                        }
                        catch { }
                    }

                });

            });
        }
    }
}