﻿using Triggero;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models;
using Triggero.Models.Practices;
using App = Triggero.MauiMobileApp.App;

namespace Triggero.MauiMobileApp.Views.Drawn
{

    public partial class TopicRecommendationDrawn : RecommendationDrawnBase
    {

        public TopicRecommendationDrawn(RecommendationModel recommendation, IRecommendation item)
            : base(recommendation, item)
        {
            InitializeComponent();

            titleLabel.Text = _item.GetLocalizedTitle(LanguageHelper.LangCode);
            minutesSpan.Text = $"{_item.PassingTimeInMinutes}";
            img.Source = App.GetFullImageUrl(item.IconImgPath, ThumbnailSize.Small, ThumbnailType.Jpg);
        }
    }
}