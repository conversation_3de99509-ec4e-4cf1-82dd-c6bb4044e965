﻿using Newtonsoft.Json;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Triggero.Models;

namespace Triggero.MauiMobileApp.Extensions.Helpers.Modules.Pools
{
    public class PostponedRequests
    {
        public static string FilePath = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + @"/postponedRequests.json";

        public List<PostponedRequestModel> Requests { get; set; } = new List<PostponedRequestModel>();

        public void AddRequest(PostponedRequestModel req)
        {
            Requests.Add(req);
        }

        public async Task<bool> ExecuteRequestes()
        {
            try
            {
                while (Requests.Count > 0)
                {
                    var item = Requests.LastOrDefault();
                    await item.ExecuteRequest();
                    Requests.Remove(item);
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }


        public void SaveChangesToMemory()
        {
            try
            {
                var json = JsonConvert.SerializeObject(this);
                File.WriteAllText(FilePath, json);
            }
            catch { }
        }
    }
}
