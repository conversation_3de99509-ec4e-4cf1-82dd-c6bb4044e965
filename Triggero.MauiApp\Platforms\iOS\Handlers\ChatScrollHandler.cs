#if IOS
using Microsoft.Maui.Handlers;
using UIKit;
using CoreGraphics;
using Triggero.MauiMobileApp.Controls;

namespace Triggero.MauiMobileApp.Platforms.iOS.Handlers
{
    /// <summary>
    /// Custom iOS handler for ChatScroll that prevents horizontal offset changes during vertical scrolling
    /// </summary>
    public class ChatScrollHandler : ScrollViewHandler
    {
        protected override void ConnectHandler(UIScrollView platformView)
        {
            base.ConnectHandler(platformView);
            
            if (platformView != null)
            {
                // Subscribe to scroll events to monitor and correct X offset
                platformView.Scrolled += OnScrolled;
                
                // Ensure horizontal scrolling is disabled
                platformView.ShowsHorizontalScrollIndicator = false;
                platformView.AlwaysBounceHorizontal = false;
                
                // Set content inset adjustment behavior to prevent automatic adjustments
                if (UIDevice.CurrentDevice.CheckSystemVersion(11, 0))
                {
                    platformView.ContentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentBehavior.Never;
                }
            }
        }

        protected override void DisconnectHandler(UIScrollView platformView)
        {
            if (platformView != null)
            {
                platformView.Scrolled -= OnScrolled;
            }
            
            base.DisconnectHandler(platformView);
        }

        private void OnScrolled(object sender, EventArgs e)
        {
            if (sender is UIScrollView scrollView)
            {
                // If X offset has changed from 0, reset it back to 0
                if (scrollView.ContentOffset.X != 0)
                {
                    var correctedOffset = new CGPoint(0, scrollView.ContentOffset.Y);
                    scrollView.SetContentOffset(correctedOffset, false);
                }
            }
        }

        /// <summary>
        /// Override SetContentOffset to ensure X is always 0
        /// </summary>
        public static void SetContentOffset(UIScrollView scrollView, CGPoint offset, bool animated)
        {
            // Force X offset to 0
            var correctedOffset = new CGPoint(0, offset.Y);
            scrollView.SetContentOffset(correctedOffset, animated);
        }

        /// <summary>
        /// Custom scroll to method that ensures X offset stays at 0
        /// </summary>
        public static void ScrollToPosition(UIScrollView scrollView, double x, double y, bool animated)
        {
            // Force X to 0 regardless of input
            var targetOffset = new CGPoint(0, y);
            scrollView.SetContentOffset(targetOffset, animated);
        }
    }
}
#endif
