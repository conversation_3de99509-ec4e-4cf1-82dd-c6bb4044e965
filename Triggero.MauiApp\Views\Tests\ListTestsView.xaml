﻿<?xml version="1.0" encoding="UTF-8"?>

<ContentView
    x:Class="Triggero.MauiMobileApp.Views.ListTestsView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
    xmlns:viewModels="clr-namespace:Triggero.MauiMobileApp.ViewModels"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this"
    x:DataType="viewModels:ElementsListViewModel">

    <ContentView.Content>

        <Grid
            Padding="0"
            BackgroundColor="#FFFFFF"
            HorizontalOptions="Fill"
            RowSpacing="0"
            VerticalOptions="Fill">

            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <LinearGradientBrush.GradientStops>
                        <GradientStop Offset="0" Color="#FFFFFF" />
                        <GradientStop Offset="1.0" Color="#FDCE72" />
                    </LinearGradientBrush.GradientStops>
                </LinearGradientBrush>
            </Grid.Background>

            <draw:Canvas
                Tag="ListTests"
                x:Name="DrawnCanvas"
                Gestures="Lock"
                RenderingMode="Accelerated"
                HorizontalOptions="Fill"
                VerticalOptions="Fill">

                <draw:SkiaGrid

                    x:Name="mainGrid"
                    VerticalOptions="Fill"
                    Padding="0"
                    BackgroundColor="#AEAA9F"
                    RowSpacing="0">

                    <draw:SkiaGrid.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                            <LinearGradientBrush.GradientStops>
                                <GradientStop Offset="0.0" Color="#FFFFFF" />
                                <GradientStop Offset="1.0" Color="#FDCE72" />
                            </LinearGradientBrush.GradientStops>
                        </LinearGradientBrush>
                    </draw:SkiaGrid.Background>

                    <draw:SkiaLayout.RowDefinitions>
                        <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                        <RowDefinition
                            x:Name="mainGridDef1"
                            Height="50" />
                        <RowDefinition Height="*" />
                    </draw:SkiaLayout.RowDefinitions>

                    <draw:SkiaLayer
                        VerticalOptions="Fill"
                        UseCache="Image"
                        Grid.Row="1">

                        <draw:SkiaImage
                            x:Name="arrowBackBtn"
                            Margin="16,0,0,0"
                            BackgroundColor="Transparent"
                            Tapped="GoBackPls"
                            HeightRequest="18"
                            Aspect="AspectFit"
                            HorizontalOptions="Start"
                            Source="arrowbackwhite.png"
                            VerticalOptions="Center"
                            WidthRequest="18" />


                        <draw:SkiaGrid
                            x:Name="pageBackNavigationGrid"
                            Margin="20,0,20,30"
                            Opacity="0"
                            VerticalOptions="End">

                            <draw:SkiaGrid.ColumnDefinitions>
                                <ColumnDefinition Width="44" />
                                <ColumnDefinition Width="*" />
                            </draw:SkiaGrid.ColumnDefinitions>

                            <draw:SkiaImage
                                Grid.Column="0"
                                BackgroundColor="Transparent"
                                Tapped="GoBackPls"
                                HeightRequest="44"
                                HorizontalOptions="Center"
                                Source="buttonbackbordered.png"
                                VerticalOptions="Center"
                                WidthRequest="44" />

                            <draw:SkiaLabel
                                x:Name="categoryLabel"
                                Grid.Column="1"
                                Margin="20,0,0,0"
                                IsVisible="False"
                                Opacity="0.001"
                                Style="{x:StaticResource StyleHeaderTextDrawn}"
                                Text="{Binding Title}"
                                VerticalOptions="Center" />

                        </draw:SkiaGrid>


                    </draw:SkiaLayer>

                    <!--CARD-->
                    <draw:SkiaShape
                        Grid.Row="2"
                        VerticalOptions="Fill"
                        HorizontalOptions="Fill"
                        Padding="0"
                        BackgroundColor="#FFFFFF"
                        CornerRadius="15,15,0,0">

                        <draw:SkiaGrid
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill">


                            <draw:SkiaGrid.RowDefinitions>
                                <RowDefinition Height="12" />
                                <RowDefinition Height="60" />
                                <RowDefinition Height="*" />
                            </draw:SkiaGrid.RowDefinitions>


                            <draw:SkiaLabel
                                UseCache="Operations"
                                x:Name="categoryLabelCollapsed"
                                Margin="20,15,0,0"
                                Style="{x:StaticResource StyleHeaderTextDrawn}"
                                Text="{Binding Title}"
                                VerticalOptions="Center" />


                            <!--  TOP TABS  -->
                            <draw:SkiaShape
                                HorizontalOptions="Fill"
                                UseCache="Image"
                                Grid.Row="1"
                                Margin="20,0,20,2"
                                Padding="0"
                                BackgroundColor="Transparent"
                                StrokeColor="{x:StaticResource ColorPrimaryLight}"
                                StrokeWidth="1"
                                CornerRadius="12"
                                HeightRequest="32"
                                VerticalOptions="Center">

                                <draw:SkiaGrid>
                                    <draw:SkiaGrid.ColumnDefinitions>
                                        <ColumnDefinition Width="1*" />
                                        <ColumnDefinition Width="1*" />
                                    </draw:SkiaGrid.ColumnDefinitions>

                                    <!--Style="{x:StaticResource yellow_rb}"-->
                                    <draw:SkiaRadioButton
                                        HorizontalOptions="Fill" VerticalOptions="Fill"
                                        Grid.Column="0"
                                        UseCache="Image"
                                        Toggled="filterByNewCheckedChanged"
                                        IsToggled="True">

                                        <draw:SkiaShape
                                            Tag="On"
                                            Background="#FDCE72"
                                            HorizontalOptions="Fill" VerticalOptions="Fill"
                                            CornerRadius="12">
                                            <draw:SkiaLabel
                                                FontSize="14"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static triggeroV2:App.This}, Path=Interface.Library.Library.FilterNew}"
                                                TextColor="#4D4D4D"
                                                VerticalOptions="Center" />
                                        </draw:SkiaShape>

                                        <draw:SkiaLabel
                                            Tag="Off"
                                            FontSize="14"
                                            HorizontalOptions="Center"
                                            Text="{Binding Source={x:Static triggeroV2:App.This}, Path=Interface.Library.Library.FilterNew}"
                                            TextColor="#4D4D4D"
                                            VerticalOptions="Center" />

                                    </draw:SkiaRadioButton>

                                    <!--Style="{x:StaticResource yellow_rb}"-->
                                    <draw:SkiaRadioButton
                                        HorizontalOptions="Fill" VerticalOptions="Fill"
                                        UseCache="Image"
                                        Grid.Column="1"
                                        Toggled="filterByWatchesCheckedChanged">

                                        <draw:SkiaShape
                                            Tag="On"
                                            Background="#FDCE72"
                                            HorizontalOptions="Fill" VerticalOptions="Fill"
                                            CornerRadius="12">
                                            <draw:SkiaLabel
                                                FontSize="14"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static triggeroV2:App.This}, Path=Interface.Library.Library.FilterPopular}"
                                                TextColor="#4D4D4D"
                                                VerticalOptions="Center" />
                                        </draw:SkiaShape>

                                        <draw:SkiaLabel
                                            Tag="Off"
                                            FontSize="14"
                                            HorizontalOptions="Center"
                                            Text="{Binding Source={x:Static triggeroV2:App.This}, Path=Interface.Library.Library.FilterPopular}"
                                            TextColor="#4D4D4D"
                                            VerticalOptions="Center" />

                                    </draw:SkiaRadioButton>

                                </draw:SkiaGrid>
                            </draw:SkiaShape>


                            <!--  LIST  -->


                            <!--  NATIVE  -->
                            <!--<CollectionView
                           Grid.Row="2
                            x:Name="collection"
                            Margin="20,25,20,0"
                            Scrolled="onScrolled">
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <templates:ExerciseCard
                                        Padding="0,6,0,0"
                                        HeightRequest="104"
                                        HorizontalOptions="Fill"
                                        VerticalOptions="Start" />

                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>-->


                            <draw:SkiaLayout
                                Grid.Row="2"
                                Margin="10,6,10,0"
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill">

                                <draw:SkiaScroll
                                    FrictionScrolled="0.35"
                                    HorizontalOptions="Fill"
                                    VerticalOptions="Fill">

                                    <draw:SkiaLayout
                                        Tag="TestsCells"
                                        x:Name="StackCells"
                                        HorizontalOptions="Fill"
                                        ItemTemplate="{Binding ItemTemplate}"
                                        ItemsSource="{Binding Items}"
                                        RecyclingTemplate="Enabled"
                                        MeasureItemsStrategy="MeasureVisible"
                                        Spacing="0"
                                        Type="Column"
                                        VirtualisationInflated="50" />

                                    <draw:SkiaScroll.Footer>
                                        <draw:SkiaLayout
                                            HeightRequest="{x:Static triggeroV2:Globals.BottomOffsetForTabs}"
                                            HorizontalOptions="Fill" />
                                    </draw:SkiaScroll.Footer>

                                </draw:SkiaScroll>

                                <draw:SkiaLabel
                                    IsVisible="{x:Static triggeroV2:Globals.ShowDebugInfo}"
                                    UseCache="Operations"
                                    Margin="8,0,8,100"
                                    BackgroundColor="Black"
                                    HorizontalOptions="Start"
                                    InputTransparent="True"
                                    Text="{Binding Source={x:Reference StackCells}, Path=DebugString}"
                                    TextColor="Lime"
                                    VerticalOptions="End" />

                                <!--  FPS  -->
                                <draw:SkiaLabelFps
                                    Margin="0,0,4,84"
                                    BackgroundColor="DarkRed"
                                    ForceRefresh="False"
                                    HorizontalOptions="End"
                                    IsVisible="{x:Static triggeroV2:Globals.ShowFPS}"
                                    Rotation="-45"
                                    TextColor="White"
                                    VerticalOptions="End" />

                            </draw:SkiaLayout>


                            <!--<ActivityIndicator
                        Grid.Row="2"
                        HorizontalOptions="Center"
                        IsRunning="{Binding IsBusy}"
                        IsVisible="{Binding IsBusy}"
                        VerticalOptions="Center" />-->

                        </draw:SkiaGrid>

                    </draw:SkiaShape>


                </draw:SkiaGrid>


            </draw:Canvas>

        </Grid>

    </ContentView.Content>
</ContentView>