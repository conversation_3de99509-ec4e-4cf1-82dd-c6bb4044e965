﻿
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Controls;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Views.Pages.Legal;
using RelayCommand = Triggero.MauiMobileApp.Extensions.Helpers.RelayCommand;


namespace Triggero.MauiMobileApp.Views.Pages.Start
{

    public partial class DisclaimerPage : ContentPage
    {
        public DisclaimerPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        protected override async void OnAppearing()
        {

            MainThread.BeginInvokeOnMainThread(() =>
            {

                var animatedImage = new GifCachedImage
                {
                    HeightRequest = 74,
                    WidthRequest = 100,
                    Opacity = 0,

                    VerticalOptions = LayoutOptions.Start,
                    HorizontalOptions = LayoutOptions.Center,
                    //Aspect = Aspect.AspectFit,
                    Source = "animationeyeinfoanim.gif"
                };
                animatedLayout.Children.Insert(0, animatedImage);

                animatedImage.Success += async (sender, e) =>
                {
                    await Task.Delay(500);
                    animatedImage.Opacity = 1;
                    notAnimatedImg.Opacity = 0;
                };
            });

        }



        private RelayCommand goBack;
        public RelayCommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }

        private RelayCommand goNext;
        public RelayCommand GoNext
        {
            get => goNext ??= new RelayCommand(async obj =>
            {
                App.SetMainPage(new MainPage());
            });
        }

        private RelayCommand goToTerms;
        public RelayCommand GoToTerms
        {
            get => goToTerms ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new TermsPage());
            });
        }
        private RelayCommand goToEULA;
        public RelayCommand GoToEULA
        {
            get => goToEULA ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new EULAPage());
            });
        }



    }
}