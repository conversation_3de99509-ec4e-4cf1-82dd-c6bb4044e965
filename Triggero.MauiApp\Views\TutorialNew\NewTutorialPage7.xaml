﻿<?xml version="1.0" encoding="utf-8"?>

<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage7"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <Grid VerticalOptions="Fill">

        <Image
            VerticalOptions="Fill"
            HorizontalOptions="Fill"
            Aspect="Fill"
            Source="tutorialblur3.png" />

        <ScrollView VerticalOptions="Fill">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="170" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="140" />
                </Grid.RowDefinitions>


                <Grid Grid.Row="0"
                      Padding="0,0"
                      Grid.RowSpan="2">

                    <StackLayout
                        Margin="0,0,0,0"
                        Spacing="12">


                        <Grid
                            Margin="-3,0,0,0"
                            HorizontalOptions="Fill"
                            VerticalOptions="Start"
                            HeightRequest="453">
                            <Image
                                Aspect="Fill"
                                Source="tutorialadvisecontainer.png" />


                            <Frame
                                HasShadow="False"
                                CornerRadius="20"
                                Padding="0"
                                BackgroundColor="White"
                                HeightRequest="365"
                                Margin="{x:OnPlatform Android='25',iOS='25,50,25,25'}">
                                <StackLayout>
                                    <Label
                                        Margin="10,0,0,0"
                                        TextColor="{x:StaticResource ColorText}"
                                        FontSize="17"
                                        FontAttributes="Bold"
                                        HorizontalOptions="Start"
                                        VerticalOptions="Start"
                                        Text="Рекомендации на сегодня" />

                                    <StackLayout
                                        Margin="0,12,0,0">

                                        <Grid HeightRequest="75">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="80" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="30" />
                                            </Grid.ColumnDefinitions>

                                            <Grid Grid.Column="0">
                                                <Image
                                                    Source="tutorialrecommendation1.png"
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="Center"
                                                    Aspect="Fill"
                                                    HeightRequest="70"
                                                    WidthRequest="70" />
                                            </Grid>

                                            <Grid Grid.Column="1">
                                                <StackLayout
                                                    Margin="10,0,0,0"
                                                    Spacing="3"
                                                    VerticalOptions="Center">
                                                    <Label
                                                        TextColor="{x:StaticResource ColorText}"
                                                        FontSize="14"
                                                        FontFamily="FontTextLight"
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Start"
                                                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage7.Task1Text}" />
                                                    <Label
                                                        TextColor="{x:StaticResource ColorText}"
                                                        Opacity="0.5"
                                                        FontSize="12"
                                                        FontFamily="FontTextLight"
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Start">
                                                        <Label.FormattedText>
                                                            <FormattedString>
                                                                <FormattedString.Spans>
                                                                    <Span Text="3 " />
                                                                    <Span
                                                                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage7.MinutesAbbrevated}" />
                                                                </FormattedString.Spans>
                                                            </FormattedString>
                                                        </Label.FormattedText>
                                                    </Label>
                                                </StackLayout>
                                            </Grid>

                                            <Grid Grid.Column="2">
                                                <Image
                                                    Source="arrowforwardlightblue.png"
                                                    HorizontalOptions="Center"
                                                    VerticalOptions="Center"
                                                    WidthRequest="6"
                                                    HeightRequest="12" />
                                            </Grid>
                                        </Grid>

                                        <Grid HeightRequest="75">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="80" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="30" />
                                            </Grid.ColumnDefinitions>

                                            <Grid Grid.Column="0">
                                                <Image
                                                    Source="tutorialrecommendation2.png"
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="Center"
                                                    Aspect="Fill"
                                                    HeightRequest="70"
                                                    WidthRequest="70" />
                                            </Grid>

                                            <Grid Grid.Column="1">
                                                <StackLayout
                                                    Margin="10,0,0,0"
                                                    Spacing="3"
                                                    VerticalOptions="Center">
                                                    <Label
                                                        TextColor="{x:StaticResource ColorText}"
                                                        FontSize="14"
                                                        FontFamily="FontTextLight"
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Start"
                                                        Text='{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage7.Task2Text}' />
                                                    <Label
                                                        TextColor="{x:StaticResource ColorText}"
                                                        Opacity="0.5"
                                                        FontSize="12"
                                                        FontFamily="FontTextLight"
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Start">
                                                        <Label.FormattedText>
                                                            <FormattedString>
                                                                <FormattedString.Spans>
                                                                    <Span Text="5 " />
                                                                    <Span
                                                                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage7.MinutesAbbrevated}" />
                                                                </FormattedString.Spans>
                                                            </FormattedString>
                                                        </Label.FormattedText>
                                                    </Label>
                                                </StackLayout>
                                            </Grid>

                                            <Grid Grid.Column="2">
                                                <Image
                                                    Source="arrowforwardlightblue.png"
                                                    HorizontalOptions="Center"
                                                    VerticalOptions="Center"
                                                    WidthRequest="6"
                                                    HeightRequest="12" />
                                            </Grid>
                                        </Grid>

                                        <Grid HeightRequest="75">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="80" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="30" />
                                            </Grid.ColumnDefinitions>

                                            <Grid Grid.Column="0">
                                                <Image
                                                    Source="tutorialrecommendation3.png"
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="Center"
                                                    Aspect="Fill"
                                                    HeightRequest="70"
                                                    WidthRequest="70" />
                                            </Grid>

                                            <Grid Grid.Column="1">
                                                <StackLayout
                                                    Margin="10,0,0,0"
                                                    Spacing="3"
                                                    VerticalOptions="Center">
                                                    <Label
                                                        TextColor="{x:StaticResource ColorText}"
                                                        FontSize="14"
                                                        FontFamily="FontTextLight"
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Start"
                                                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage7.Task3Text}" />
                                                    <Label
                                                        TextColor="{x:StaticResource ColorText}"
                                                        Opacity="0.5"
                                                        FontSize="12"
                                                        FontFamily="FontTextLight"
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Start">
                                                        <Label.FormattedText>
                                                            <FormattedString>
                                                                <FormattedString.Spans>
                                                                    <Span Text="7 " />
                                                                    <Span
                                                                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage7.MinutesAbbrevated}" />
                                                                </FormattedString.Spans>
                                                            </FormattedString>
                                                        </Label.FormattedText>
                                                    </Label>
                                                </StackLayout>
                                            </Grid>

                                            <Grid Grid.Column="2">
                                                <Image
                                                    Source="arrowforwardlightblue.png"
                                                    HorizontalOptions="Center"
                                                    VerticalOptions="Center"
                                                    WidthRequest="6"
                                                    HeightRequest="12" />
                                            </Grid>
                                        </Grid>

                                        <Grid HeightRequest="75">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="80" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="30" />
                                            </Grid.ColumnDefinitions>

                                            <Grid Grid.Column="0">
                                                <Image
                                                    Source="tutorialrecommendation4.png"
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="Center"
                                                    Aspect="Fill"
                                                    HeightRequest="70"
                                                    WidthRequest="70" />
                                            </Grid>

                                            <Grid Grid.Column="1">
                                                <StackLayout
                                                    Margin="10,0,0,0"
                                                    Spacing="3"
                                                    VerticalOptions="Center">
                                                    <Label
                                                        TextColor="{x:StaticResource ColorText}"
                                                        FontSize="14"
                                                        FontFamily="FontTextLight"
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Start"
                                                        Text='{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage7.Task4Text}' />
                                                    <Label
                                                        TextColor="{x:StaticResource ColorText}"
                                                        Opacity="0.5"
                                                        FontSize="12"
                                                        FontFamily="FontTextLight"
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Start">
                                                        <Label.FormattedText>
                                                            <FormattedString>
                                                                <FormattedString.Spans>
                                                                    <Span Text="12 " />
                                                                    <Span
                                                                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage7.MinutesAbbrevated}" />
                                                                </FormattedString.Spans>
                                                            </FormattedString>
                                                        </Label.FormattedText>
                                                    </Label>
                                                </StackLayout>
                                            </Grid>

                                            <Grid Grid.Column="2">
                                                <Image
                                                    Source="arrowforwardlightblue.png"
                                                    HorizontalOptions="Center"
                                                    VerticalOptions="Center"
                                                    WidthRequest="6"
                                                    HeightRequest="12" />
                                            </Grid>
                                        </Grid>

                                    </StackLayout>

                                </StackLayout>
                            </Frame>

                        </Grid>


                        <Label
                            Margin="0,34,0,0"
                            TextColor="#000000"
                            FontAttributes="Bold"
                            FontSize="19"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="317"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage7.Header}" />
                        <Label
                            Margin="0,0,0,0"
                            TextColor="{x:StaticResource ColorText}"
                            FontSize="16"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="318"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage7.Description}" />


                    </StackLayout>

                </Grid>

                <Grid Grid.Row="2">
                    <Button
                        Command="{Binding Source={x:Reference this},Path=GoNext}"
                        VerticalOptions="Start"
                        HorizontalOptions="Fill"
                        Margin="63,0,63,0"
                        Style="{x:StaticResource yellow_btn}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage7.GoNext}" />

                </Grid>

            </Grid>
        </ScrollView>
    </Grid>
</ContentPage>