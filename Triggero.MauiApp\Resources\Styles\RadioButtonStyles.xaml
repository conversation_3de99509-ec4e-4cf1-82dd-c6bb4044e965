﻿<?xml version="1.0" encoding="utf-8"?>

<ResourceDictionary
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp">


    <Style
        x:Key="yellow_rb"
        TargetType="RadioButton">
        <Style.Triggers>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Frame
                                Padding="0"
                                BackgroundColor="#FDCE72"
                                CornerRadius="12"
                                HasShadow="False">
                                <Label
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    Text="{TemplateBinding Content}"
                                    TextColor="#4D4D4D"
                                    VerticalOptions="Center" />
                            </Frame>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Frame
                                Padding="0"
                                BackgroundColor="Transparent"
                                CornerRadius="12"
                                HasShadow="False">
                                <Label
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    Text="{TemplateBinding Content}"
                                    TextColor="#4D4D4D"
                                    VerticalOptions="Center" />
                            </Frame>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="favorite_hearted_rb"
        TargetType="RadioButton">
        <Style.Triggers>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Image
                                HeightRequest="20"
                                Source="likeset.png"
                                WidthRequest="20" />
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Image
                                HeightRequest="20"
                                Source="likeunset.png"
                                WidthRequest="20" />
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="favorite_hearted_white_rb"
        TargetType="RadioButton">
        <Style.Triggers>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Image
                                HeightRequest="20"
                                Source="likeset.png"
                                WidthRequest="20" />
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Image
                                HeightRequest="20"
                                Source="likeunsetwhite.png"
                                WidthRequest="20" />
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="yellow_checkMark_rb"
        TargetType="RadioButton">
        <Style.Triggers>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Image
                                BackgroundColor="Transparent"
                                HeightRequest="24"
                                HorizontalOptions="Center"
                                Source="completedcircleyellow.png"
                                VerticalOptions="Center"
                                WidthRequest="24" />
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Frame
                                Padding="0"
                                BackgroundColor="Transparent"
                                BorderColor="{x:StaticResource ColorPrimaryLight}"
                                CornerRadius="12"
                                HasShadow="False"
                                HeightRequest="24"
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                WidthRequest="24" />
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="thumb_like_rb"
        TargetType="RadioButton">
        <Style.Triggers>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Frame
                                Grid.Column="1"
                                Padding="8"
                                BackgroundColor="{x:StaticResource ColorCardColored}"
                                CornerRadius="16"
                                HasShadow="False">
                                <Image
                                    HeightRequest="24"
                                    HorizontalOptions="Center"
                                    Source="thumblikeset.png"
                                    VerticalOptions="Center"
                                    WidthRequest="24" />
                            </Frame>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Frame
                                Grid.Column="1"
                                Padding="8"
                                BackgroundColor="{x:StaticResource ColorCardColored}"
                                CornerRadius="16"
                                HasShadow="False">
                                <Image
                                    HeightRequest="24"
                                    HorizontalOptions="Center"
                                    Source="thumblike.png"
                                    VerticalOptions="Center"
                                    WidthRequest="24" />
                            </Frame>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="thumb_dislike_rb"
        TargetType="RadioButton">
        <Style.Triggers>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Frame
                                Grid.Column="1"
                                Padding="8"
                                BackgroundColor="{x:StaticResource ColorCardColored}"
                                CornerRadius="16"
                                HasShadow="False">
                                <Image
                                    HeightRequest="24"
                                    HorizontalOptions="Center"
                                    Source="thumbdislikeset.png"
                                    VerticalOptions="Center"
                                    WidthRequest="24" />
                            </Frame>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Frame
                                Grid.Column="1"
                                Padding="8"
                                BackgroundColor="{x:StaticResource ColorCardColored}"
                                CornerRadius="16"
                                HasShadow="False">
                                <Image
                                    HeightRequest="24"
                                    HorizontalOptions="Center"
                                    Source="thumbdislike.png"
                                    VerticalOptions="Center"
                                    WidthRequest="24" />
                            </Frame>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="player_start_pause_rb"
        TargetType="RadioButton">
        <Style.Triggers>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Grid HorizontalOptions="Start" 
                                    HeightRequest="70"
                                  WidthRequest="70">
                                <Image
                                HeightRequest="70"
                                Source="playerpausecircle.png"
                                WidthRequest="70" />
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Image
                                HeightRequest="70"
                                Source="playerstartcircle.png"
                                WidthRequest="70" />
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="mood_tracker_period_rb"
        TargetType="RadioButton">
        <Style.Triggers>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Frame
                                Padding="0"
                                BackgroundColor="#F8F9FC"
                                BorderColor="{x:StaticResource ColorPrimaryLight}"
                                CornerRadius="16"
                                HasShadow="False">
                                <Label
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    Text="{TemplateBinding Content}"
                                    TextColor="#4D4D4D"
                                    VerticalOptions="Center" />
                            </Frame>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Frame
                                Padding="0"
                                BackgroundColor="Transparent"
                                BorderColor="{x:StaticResource ColorPrimaryLight}"
                                CornerRadius="16"
                                HasShadow="False">
                                <Label
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    Text="{TemplateBinding Content}"
                                    TextColor="#4D4D4D"
                                    VerticalOptions="Center" />
                            </Frame>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>


    <Style
        x:Key="library_exercises_rb"
        TargetType="RadioButton">
        <Style.Triggers>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Grid>

                                <Image
                                    Aspect="Fill"
                                    HorizontalOptions="Fill"
                                    Source="menuexercisesselected.png"
                                    VerticalOptions="Fill" />

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="60" />
                                        <RowDefinition Height="30" />
                                    </Grid.RowDefinitions>


                                    <Grid Grid.Row="0">
                                        <Image
                                            Aspect="Fill"
                                            HeightRequest="46"
                                            HorizontalOptions="Center"
                                            Source="menuexercises.png"
                                            VerticalOptions="Center"
                                            WidthRequest="60" />
                                    </Grid>

                                    <Grid Grid.Row="1">
                                        <Label
                                            FontSize="10"
                                            HorizontalOptions="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Exercises}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Start" />
                                    </Grid>

                                </Grid>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Grid>
                                <Image
                                    Aspect="Fill"
                                    HorizontalOptions="Fill"
                                    Source="menuexercisesunselected.png"
                                    VerticalOptions="Fill" />

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="60" />
                                        <RowDefinition Height="30" />
                                    </Grid.RowDefinitions>


                                    <Grid Grid.Row="0">
                                        <Image
                                            Aspect="Fill"
                                            HeightRequest="46"
                                            HorizontalOptions="Center"
                                            Source="menuexercises.png"
                                            VerticalOptions="Center"
                                            WidthRequest="60" />
                                    </Grid>

                                    <Grid Grid.Row="1">
                                        <Label
                                            FontSize="10"
                                            HorizontalOptions="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Exercises}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Start" />
                                    </Grid>

                                </Grid>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="practices_exercises_rb"
        TargetType="RadioButton">
        <Style.Triggers>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Grid>
                                <Image
                                    Aspect="Fill"
                                    HorizontalOptions="Fill"
                                    Source="menupracticesselected.png"
                                    VerticalOptions="Fill" />

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="60" />
                                        <RowDefinition Height="30" />
                                    </Grid.RowDefinitions>

                                    <Grid Grid.Row="0">
                                        <Image
                                            Aspect="Fill"
                                            HeightRequest="45"
                                            HorizontalOptions="Center"
                                            Source="menupractices.png"
                                            VerticalOptions="Center"
                                            WidthRequest="52" />
                                    </Grid>

                                    <Grid Grid.Row="1">
                                        <Label
                                            FontSize="10"
                                            HorizontalOptions="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Practices}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Start" />
                                    </Grid>

                                </Grid>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Grid>
                                <Image
                                    Aspect="Fill"
                                    HorizontalOptions="Fill"
                                    Source="menupracticesunselected.png"
                                    VerticalOptions="Fill" />

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="60" />
                                        <RowDefinition Height="30" />
                                    </Grid.RowDefinitions>

                                    <Grid Grid.Row="0">
                                        <Image
                                            Aspect="Fill"
                                            HeightRequest="45"
                                            HorizontalOptions="Center"
                                            Source="menupractices.png"
                                            VerticalOptions="Center"
                                            WidthRequest="52" />
                                    </Grid>

                                    <Grid Grid.Row="1">
                                        <Label
                                            FontSize="10"

                                            HorizontalOptions="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Practices}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Start" />
                                    </Grid>

                                </Grid>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="practices_topics_rb"
        TargetType="RadioButton">
        <Style.Triggers>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Grid>
                                <Image
                                    Aspect="Fill"
                                    HorizontalOptions="Fill"
                                    Source="menutopicsselected.png"
                                    VerticalOptions="Fill" />

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="60" />
                                        <RowDefinition Height="30" />
                                    </Grid.RowDefinitions>

                                    <Grid Grid.Row="0">
                                        <Image
                                            Aspect="Fill"
                                            HeightRequest="50"
                                            HorizontalOptions="Center"
                                            Source="menutopics.png"
                                            VerticalOptions="Center"
                                            WidthRequest="59" />
                                    </Grid>

                                    <Grid Grid.Row="1">
                                        <Label
                                            FontSize="10"

                                            HorizontalOptions="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Topics}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Start" />
                                    </Grid>

                                </Grid>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Grid>
                                <Image
                                    Aspect="Fill"
                                    HorizontalOptions="Fill"
                                    Source="menutopicsunselected.png"
                                    VerticalOptions="Fill" />

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="60" />
                                        <RowDefinition Height="30" />
                                    </Grid.RowDefinitions>

                                    <Grid Grid.Row="0">
                                        <Image
                                            Aspect="Fill"
                                            HeightRequest="50"
                                            HorizontalOptions="Center"
                                            Source="menutopics.png"
                                            VerticalOptions="Center"
                                            WidthRequest="59" />
                                    </Grid>

                                    <Grid Grid.Row="1">
                                        <Label
                                            FontSize="10"

                                            HorizontalOptions="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Topics}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Start" />
                                    </Grid>

                                </Grid>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="breath_topics_rb"
        TargetType="RadioButton">
        <Style.Triggers>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Grid>
                                <Image
                                    Aspect="Fill"
                                    HorizontalOptions="Fill"
                                    Source="menubreathselected.png"
                                    VerticalOptions="Fill" />

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="60" />
                                        <RowDefinition Height="30" />
                                    </Grid.RowDefinitions>


                                    <Grid Grid.Row="0">
                                        <Image
                                            Aspect="Fill"
                                            HeightRequest="47"
                                            HorizontalOptions="Center"
                                            Source="menubreath3.png"
                                            VerticalOptions="Center"
                                            WidthRequest="44" />
                                    </Grid>

                                    <Grid Grid.Row="1">
                                        <Label
                                            FontSize="10"

                                            HorizontalOptions="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Breath}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Start" />
                                    </Grid>

                                </Grid>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Grid>
                                <Image
                                    Aspect="Fill"
                                    HorizontalOptions="Fill"
                                    Source="menubreathunselected.png"
                                    VerticalOptions="Fill" />

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="60" />
                                        <RowDefinition Height="30" />
                                    </Grid.RowDefinitions>


                                    <Grid Grid.Row="0">
                                        <Image
                                            Aspect="Fill"
                                            HeightRequest="47"
                                            HorizontalOptions="Center"
                                            Source="menubreath3.png"
                                            VerticalOptions="Center"
                                            WidthRequest="44" />
                                    </Grid>

                                    <Grid Grid.Row="1">
                                        <Label
                                            FontSize="10"

                                            HorizontalOptions="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Breath}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Start" />
                                    </Grid>

                                </Grid>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="library_tests_rb"
        TargetType="RadioButton">
        <Style.Triggers>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Grid>
                                <Image
                                    Aspect="Fill"
                                    HorizontalOptions="Fill"
                                    Source="menubreathselected.png"
                                    VerticalOptions="Fill" />

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="60" />
                                        <RowDefinition Height="30" />
                                    </Grid.RowDefinitions>


                                    <Grid Grid.Row="0">
                                        <Image
                                            HeightRequest="52"
                                            HorizontalOptions="Center"
                                            Source="menubreath.png"
                                            VerticalOptions="Center"
                                            WidthRequest="47" />
                                    </Grid>

                                    <Grid Grid.Row="1">
                                        <Label
                                            FontSize="10"

                                            HorizontalOptions="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Tests.Tests}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Start" />
                                    </Grid>

                                </Grid>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                <Setter Property="ControlTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Grid>
                                <Image
                                    Aspect="Fill"
                                    HorizontalOptions="Fill"
                                    Source="menubreathunselected.png"
                                    VerticalOptions="Fill" />

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="60" />
                                        <RowDefinition Height="30" />
                                    </Grid.RowDefinitions>


                                    <Grid Grid.Row="0">
                                        <Image
                                            HeightRequest="52"
                                            HorizontalOptions="Center"
                                            Source="menubreath.png"
                                            VerticalOptions="Center"
                                            WidthRequest="47" />
                                    </Grid>

                                    <Grid Grid.Row="1">
                                        <Label
                                            FontSize="10"

                                            HorizontalOptions="Center"
                                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Tests.Tests}"
                                            TextColor="{x:StaticResource ColorText}"
                                            VerticalOptions="Start" />
                                    </Grid>

                                </Grid>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>


</ResourceDictionary>