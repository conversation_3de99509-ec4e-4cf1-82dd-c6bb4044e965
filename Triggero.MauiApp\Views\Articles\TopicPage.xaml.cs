﻿using AppoMobi.Specials;
using MobileAPIWrapper;
using System.Threading.Tasks;
using System.Windows.Input;
using Triggero.Controls.Cards.NextItemCard;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Extensions.Helpers.Modules;
using Triggero.MauiMobileApp.Services;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;

namespace Triggero.MauiMobileApp.Views.Pages.Library
{
    public partial class TopicPage : ContentPage
    {
        private Topic topic;

        public Topic Topic
        {
            get { return topic; }
            set
            {
                topic = value;
                OnPropertyChanged(nameof(Topic));
            }
        }

#if PREVIEWS

        [HotPreview.Preview]
        public static void PreviewTopic()
        {
            var model = new Topic()
            {
                Category = new()
                {
                    Title = "Category"
                },
                Description = "description ...",
                Title = "Preview",
                Localizations = new List<TopicLocalization>()
            };

            App.OpenPage(new TopicPage(model));
        }
#endif

        public TopicPage(Topic topic)
        {
            try
            {
                Topic = topic;

                InitializeComponent();

                if (Constants.ShowReferenceLinks && !string.IsNullOrEmpty(Topic.ReferenceLink))
                {
                    LabelExternalLink.IsVisible = true;
                }

                img.Source = App.GetFullImageUrl(Topic.ImgPath, ThumbnailSize.Large, ThumbnailType.Jpg);

                NavigationPage.SetHasNavigationBar(this, false);

                if (topic.Id > 0)
                {
                    Tasks.StartDelayed(TimeSpan.FromMilliseconds(30), () =>
                    {
                        Load();
                    });
                }
            }
            catch (Exception e)
            {
                Super.DisplayException(this, e);
            }
        }

        private void Load()
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    titleLabel.Text = Topic.GetLocalizedTitle(LanguageHelper.LangCode);
                    authorLabel.Text = Topic.GetLocalizedAuthor(LanguageHelper.LangCode);
                    likesCountLabel.Text = Topic.Likes.ToString();

                    isFav = ApplicationState.UserFavorites.HasFavoriteTopic(Topic.Id);
                    favoriteRb.IsToggled = isFav;

                    //img.Source = await ResorcesHelper.GetImageSource(Topic.ImgPath);

                    SetRandomNextRecommendation();

                    await StatsHelper.AddTopicWatch(Topic.Id);

                    if (ConnectionHelper.HasInternet())
                    {
                        var rate = await TriggeroMobileAPI.LibraryMethods.TopicsMethods.GetRate(AuthHelper.UserId,
                            Topic.Id);
                        ourCurrentRate = rate;

                        if (rate == RateType.Like)
                        {
                            dislikeRb.IsChecked = false;
                            likeRb.IsChecked = true;
                        }
                        else if (rate == RateType.Dislike)
                        {
                            dislikeRb.IsChecked = true;
                            likeRb.IsChecked = false;
                        }
                    }
                    else
                    {
                        dislikeRb.IsChecked = false;
                        likeRb.IsChecked = false;
                    }
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }
            });
        }


        public ICommand CommandOpenReferenceLink
        {
            get
            {
                return new Command((context) =>
                {
                    if (!string.IsNullOrEmpty(Topic.ReferenceLink))
                    {
                        MainThread.BeginInvokeOnMainThread(() => { PlatformUi.Instance.OpenUrl(Topic.ReferenceLink); });
                    }
                });
            }
        }


        bool once;

        protected override void OnAppearing()
        {
            base.OnAppearing();

            PlatformUi.Instance.HideStatusBar();

            if (!once)
            {
                once = true;
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    descLabel.Text = Topic.GetLocalizedDescription(LanguageHelper.LangCode);

                    var pause = 700; //on iOS this will be triggered BEFORE the page is shown, so we delay a bit
                    if (Device.RuntimePlatform == Device.Android)
                        pause = 10;
                    await Task.Delay(pause);

                    await mainStackLayout.FadeTo(1, 700);
                });
            }
        }

        private bool isFav;

        private async void toggleFavorite(object? sender, bool b)
        {
            if (isFav != b)
            {
                isFav = await ApplicationState.UserFavorites.ToggleTopic(Topic.Id);
                App.Messager.All("FavChanged", new FavChangedObject(Topic.Id, isFav));
            }
        }

        private void SetRandomNextRecommendation()
        {
            nextItemLayout.Children.Clear();
            View card = null;

            var recommendation = ApplicationState.Data.GetRandomRecommendation();
            switch (recommendation.GetRecommendationType())
            {
                case RecomendationType.Test:
                    card = new TestNextItemCard(recommendation.Test);
                    break;
                case RecomendationType.Exercise:
                    card = new ExerciseNextItemCard(recommendation.Exercise);
                    break;
                case RecomendationType.Practice:
                    card = new PracticeNextItemCard(recommendation.Practice);
                    break;
                case RecomendationType.Topic:
                    card = new TopicNextItemCard(recommendation.Topic);
                    break;
            }

            if (card != null)
            {
                nextItemLayout.Children.Add(card);
            }
        }


        RateType ourCurrentRate = RateType.None;

        private async void likeSet(object sender, EventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                bool result = await StatsHelper.ToggleTopicRate(Topic.Id, RateType.Like);
                dislikeRb.IsChecked = false;
                likeRb.IsChecked = result;


                //Изменяем число визуально
                if ((ourCurrentRate == RateType.None || ourCurrentRate == RateType.Dislike) && result)
                {
                    Topic.Likes++;
                    likesCountLabel.Text = Topic.Likes.ToString();
                    ourCurrentRate = RateType.Like;
                }
                else if (ourCurrentRate == RateType.Like && !result)
                {
                    Topic.Likes--;
                    likesCountLabel.Text = Topic.Likes.ToString();
                    ourCurrentRate = RateType.None;
                }
            });
        }

        private async void dislikeSet(object sender, EventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                bool result = await StatsHelper.ToggleTopicRate(Topic.Id, RateType.Dislike);
                dislikeRb.IsChecked = result;
                likeRb.IsChecked = false;

                //Изменяем число визуально
                if (ourCurrentRate == RateType.None && result)
                {
                    ourCurrentRate = RateType.Dislike;
                }

                if (ourCurrentRate == RateType.Like && result)
                {
                    Topic.Likes--;
                    likesCountLabel.Text = Topic.Likes.ToString();
                    ourCurrentRate = RateType.Dislike;
                }
                else if (ourCurrentRate == RateType.Dislike && !result)
                {
                    ourCurrentRate = RateType.None;
                }
            });
        }


        private ICommand close;

        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj => { App.ClosePage(this); });
        }
    }
}