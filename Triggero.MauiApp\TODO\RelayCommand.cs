﻿namespace Triggero.MauiMobileApp.Controls;

public class RelayCommand : Command
{
    public RelayCommand(Action<object> execute) : base(execute)
    {
    }

    public RelayCommand(Action execute) : base(execute)
    {
    }

    public RelayCommand(Action<object> execute, Func<object, bool> canExecute) : base(execute, canExecute)
    {
    }

    public RelayCommand(Action execute, Func<bool> canExecute) : base(execute, canExecute)
    {
    }
}