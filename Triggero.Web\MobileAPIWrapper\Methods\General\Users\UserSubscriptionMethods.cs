﻿using Newtonsoft.Json;
using MobileAPIWrapper.Helpers;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using System.Threading.Tasks;
using Triggero.Models.Plans;
using Triggero.Models.Tests;

namespace MobileAPIWrapper.Methods.General.Users
{
    public class UserSubscriptionMethods
    {
        private string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("UserSubscription/");


        public async Task<List<Plan>> GetSubscriptions()
        {
            string url = BASE_HOST + $"GetSubscriptions";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Plan>>(response.Content);
            return obj;
        }

        public async Task<List<PlanOption>> GetSubscriptionOptions()
        {
            string url = BASE_HOST + $"GetSubscriptionOptions";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<PlanOption>>(response.Content);
            return obj;
        }

        public async Task<bool> StartTrial(int userId)
        {
            string url = BASE_HOST + $"StartTrial?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }

        public async Task SetInfinite(int userId)
        {
            string url = BASE_HOST + $"SetInfinite?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put);
        }
    }
}
