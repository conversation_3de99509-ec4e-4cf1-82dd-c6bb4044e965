﻿using System.IO;
using Newtonsoft.Json;
using Triggero.ChatBot;
using Triggero.ChatBot.ChatBot;
using Triggero.MauiMobileApp.Extensions.Helpers;

namespace Triggero.MauiMobileApp.Extensions.Helpers.Modules
{
    public class ChatBotData
    {
        public static string FilePath = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + @"/chatBotData.json";

        public ChatBotResponse LastResponse { get; set; }

        public DateTime LastResponseDate { get; set; }
        public DateTime LastUserActionDate { get; set; }
        public bool IsHelpMsgSent { get; set; }


        public TriggeroChatBot Bot { get; set; }



        public bool IsInSupport { get; set; }



        public bool WasStartMsg { get; set; }
        //public bool WasUserActivity
        //{
        //    get
        //    {
        //        bool was = LastResponseDate.AddHours(1) > DateTime.Now
        //                        && Bot is not null;
        //        if (was)
        //        {
        //            WasStartMsg = false;
        //        }
        //        return was;
        //    }
        //}
        public bool IsUserAFK => LastUserActionDate.AddHours(1) < DateTime.Now;
        public bool ShouldSendHelpMessage
        {
            get
            {
                return IsUserAFK && !IsHelpMsgSent;
            }
        }


        public TriggeroChatBot GetChatBot()
        {
            if (Bot == null || AuthHelper.User?.Id != Bot.User?.Id)
            {
                Bot = new TriggeroChatBot(AuthHelper.User);
                SaveChangesToMemory();
            }
            return Bot;
        }

        public void SaveResponse(ChatBotResponse resp)
        {
            LastResponse = resp;
            LastResponseDate = DateTime.Now;
            SaveChangesToMemory();
        }


        public void SaveChangesToMemory()
        {
            try
            {
                var json = JsonConvert.SerializeObject(this);
                File.WriteAllText(FilePath, json);
            }
            catch { }
        }
    }
}
