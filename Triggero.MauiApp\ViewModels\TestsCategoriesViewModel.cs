﻿using System.Threading.Tasks;
using Triggero.Controls.Templates;
using Triggero.Models.Tests;
using Triggero.MauiMobileApp.Views;

using Triggero.MauiMobileApp.Extensions.Helpers;

namespace Triggero.MauiMobileApp.ViewModels;

public class TestsCategoriesViewModel : BaseCategoriesViewModel
{
    public override Color ThemeColor
    {
        get
        {
            return Color.FromHex("#F5F9FD");
        }
    }

    public override async Task InitializeAsyc()
    {
        var items = await ApplicationState.Data.GetTestCategories();
        items.Insert(0, new TestCategory
        {
            Title = App.This.Interface.Tests.AllTests,
            Description = App.This.Interface.Tests.AllTestsDescription,
            ImgPath = "/built_in/images/allTests.png"
        });


        MainThread.BeginInvokeOnMainThread(() =>
        {
            ItemTemplate = new DataTemplate(() =>
            {
                return new CellCategoryDrawn();
            });

            Items.AddRange(items);

            //preload all images..
            //var cancel = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            //CancelPreload = cancel;
            //SkiaImageManager.Instance.PreloadImages(items.Select(s => s.ImgPath.AddBaseUrl(Triggero.MauiMobileApp.Constants.UrlContent)), cancel);
        });

        IsInitialized = true;
    }

    public async Task Refresh()
    {
        var items = await ApplicationState.Data.GetTestCategories();
        items.Insert(0, new TestCategory
        {
            Title = App.This.Interface.Tests.AllTests,
            Description = App.This.Interface.Tests.AllTestsDescription,
            ImgPath = "/built_in/images/allTests.png"
        });

        MainThread.BeginInvokeOnMainThread(() =>
        {
            Items.Clear();
            items.AddRange(items);
        });
    }

    protected override void OnViewTapped(SkiaControl control)
    {
        //App.OpenView(new ListElementsView(control.BindingContext as AbstractCategory));

        App.OpenView(new ListTestsView(control.BindingContext as TestCategory));

        //if (control.BindingContext is TestCategory model)
        //{
        //    App.OpenView(new TestsView(model));
        //}
    }
}