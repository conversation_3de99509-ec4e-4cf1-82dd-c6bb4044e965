﻿
using AppoMobi.Specials;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Controls;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Views.Pages;
using Triggero.MauiMobileApp.Views.Pages.Auth;
using Triggero.MauiMobileApp.Views.Pages.Start;
using Triggero.MauiMobileApp.Views.Pages.Subscriptions;


namespace Triggero
{

    public partial class SplashPage : ContentPage
    {
        protected override void OnAppearing()
        {
            base.OnAppearing();

            PlatformUi.Instance.ApplyTheme();
        }


        public SplashPage()
        {
            AwaitForLoading();


            InitializeComponent();

            NavigationPage.SetHasNavigationBar(this, false);
 
        }


        private async Task AwaitForLoading()
        {
            while (!App.This.IsMainDataLoaded)
            {
                await Task.Delay(100);
            }
            await Redirect();
        }

        private async Task Redirect()
        {

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                if (ApplicationState.ConfigData.IsFirstUse)
                {
                    App.OpenPage(new PageOnboarding1());
                    ApplicationState.ConfigData.IsFirstUse = false;
                    ApplicationState.SaveChangesToMemory();
                }
                else
                {
                    if (AuthHelper.IsAuthorized)
                    {
                        if (AuthHelper.TrialActivated)
                        {
                            App.OpenPage(new MainPage());
                        }
                        else
                        {
                            if (!ApplicationState.ConfigData.WasShownStartTutorial)
                                App.OpenPage(new StartTutorialPage());
                            else
                                App.OpenPage(new SelectTrialPage());
                        }
                    }
                    else
                    {
                        App.OpenPage(new LoginStartPage());
                    }
                }

                Tasks.StartDelayed(TimeSpan.FromSeconds(1), () =>
                {
                    App.ClosePage(this);
                });

            });
        }


    }
}