﻿using AppoMobi.Maui.Gestures;
using AppoMobi.Specials;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using Triggero.Controls.Templates;
using Triggero.MauiMobileApp.Views.Pages.Library;
using Triggero.MauiMobileApp.Views.Pages.Tests;
using Triggero.Models.Practices;
using Triggero.Models.Tests;

namespace Triggero.MauiMobileApp.ViewModels;

public abstract class ElementsListViewModel : BaseItemsViewModel<IElementDetails>
{
    private bool _firstLoad = false;

    protected int OrderBy;

    protected abstract Task<IEnumerable<IElementDetails>> LoadItemsAsync();

    private bool _WasBusy;
    public bool WasBusy
    {
        get
        {
            return _WasBusy;
        }
        set
        {
            if (_WasBusy != value)
            {
                _WasBusy = value;
                OnPropertyChanged();
            }
        }
    }

    bool _postLoad;

    public void ClearData()
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            Items.Clear();
        });
    }

    protected virtual void OnItemsChanged()
    {

    }

    public virtual async Task LoadDataAsync()
    {
        if (IsBusy)
        {
            WasBusy = true;
            return;
        }

        try
        {

            if (!IsInitialized)
            {
                SetTemplate();
            }

            List<IElementDetails> sorted = null;

            var items = await LoadItemsAsync();

            if (OrderBy == 0)
            {
                sorted = items.OrderByDescending(o => o.CreatedAt).ToList();
            }
            else
            {
                sorted = items.OrderByDescending(o => o.Watches).ToList();
            }

            MainThread.BeginInvokeOnMainThread(() =>
            {
                Items.Clear();
                if (sorted != null)
                {
                    Items.AddRange(sorted);
                    System.Diagnostics.Debug.WriteLine($"LIST ITEMS ADDED {sorted.Count}");
                }
                OnPropertyChanged(nameof(IsEmpty));
                OnItemsChanged();
            });

            if (!IsInitialized)
            {
                IsInitialized = true;

                OnDataInitialized();
            }

        }
        catch (Exception e)
        {
            Super.Log(e);
        }
        finally
        {
            IsBusy = false;
            //if (WasBusy)
            //{
            //    WasBusy = false;
            //    if (!_postLoad)
            //    {
            //        _postLoad = true;
            //        Tasks.StartDelayed(TimeSpan.FromMilliseconds(100), async () =>
            //        {
            //            await LoadDataAsync();
            //            _postLoad = false;
            //        });
            //    }
            //}
        }

    }

    protected virtual void OnDataInitialized()
    {
        //preload all images..

        //var cancel = new CancellationTokenSource(TimeSpan.FromSeconds(10));
        //CancelPreload = cancel;
        //SkiaImageManager.Instance.PreloadImages(items.Select(s => s.ImgPath.AddBaseUrl(Triggero.MauiMobileApp.Constants.UrlContent)), cancel);
    }

    protected virtual void SetTemplate()
    {
        ItemTemplate = new DataTemplate(() =>
        {
            return new CellElementDrawn();
        });
    }

    public bool IsInitialized { get; set; }

    public abstract Color ThemeColor { get; }
    public abstract Color ThemeColorB { get; }

    private string _Title;
    public string Title
    {
        get
        {
            return _Title;
        }
        set
        {
            if (_Title != value)
            {
                _Title = value;
                OnPropertyChanged();
            }
        }
    }

    private string _Description;
    public string Description
    {
        get
        {
            return _Description;
        }
        set
        {
            if (_Description != value)
            {
                _Description = value;
                OnPropertyChanged();
            }
        }
    }

    private string _Details;
    public string Details
    {
        get
        {
            return _Details;
        }
        set
        {
            if (_Details != value)
            {
                _Details = value;
                OnPropertyChanged();
            }
        }
    }

    protected virtual void OnViewTapped(SkiaControl control)
    {
        if (TouchEffect.CheckLockAndSet())
            return;

        if (control.BindingContext is Test test)
        {
            App.OpenPage(new TestPage(test));
        }
        else
        if (control.BindingContext is Practice practice)
        {
            App.OpenPage(new PracticePage(practice));
        }
        else
        if (control.BindingContext is Exercise exercise)
        {
            App.OpenPage(new ExercisePage(exercise));
        }
        else
        if (control.BindingContext is Topic topic)
        {
            App.OpenPage(new TopicPage(topic));
        }
    }

    public ICommand CommandChildTapped
    {
        get
        {
            return new Command((child) =>
            {
                if (child is DrawnUi.Draw.SkiaControlWithRect wrapper)
                {
                    if (wrapper.Control != null)
                    {
                        OnViewTapped(wrapper.Control);
                    }
                }
                else
                if (child is SkiaControl control)
                {
                    OnViewTapped(control);
                }
            });
        }
    }

    private DataTemplate _itemTemplate;
    public DataTemplate ItemTemplate
    {
        get
        {
            return _itemTemplate;
        }
        set
        {
            if (_itemTemplate != value)
            {
                _itemTemplate = value;
                OnPropertyChanged();
            }
        }
    }

    public CancellationTokenSource CancelPreload { get; set; }

    protected virtual void Reload()
    {
        if (!_firstLoad)
        {
            _firstLoad = true;
            Tasks.StartDelayed(TimeSpan.FromMilliseconds(50), async () =>
            {
                await LoadDataAsync();
            });
        }
        else
        {
            LoadDataAsync().ConfigureAwait(false);
        }
    }

    public virtual ICommand CommandOrderA
    {
        get
        {
            return new Command((child) =>
            {
                OrderBy = 0;
                Reload();
            });
        }
    }

    public virtual ICommand CommandOrderB
    {
        get
        {
            return new Command((child) =>
            {
                OrderBy = 1;
                Reload();
            });
        }
    }
}