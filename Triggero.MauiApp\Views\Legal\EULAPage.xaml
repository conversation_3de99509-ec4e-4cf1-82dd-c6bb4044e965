﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this"
             BackgroundColor="White"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Legal.EULAPage">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="{x:OnPlatform Android=70,iOS=100}"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">

                <BoxView
                    BackgroundColor="#000000"
                    VerticalOptions="End"
                    HorizontalOptions="Fill"
                    HeightRequest="1"/>

                <ImageButton 
                    Command="{Binding Source={x:Reference this},Path=Close}"
                    CornerRadius="0"
                    WidthRequest="14"
                    HeightRequest="14"
                    Source="close.png"
                    Margin="0,0,25,21"
                    HorizontalOptions="End"
                    VerticalOptions="End"
                    BackgroundColor="Transparent"/>

            </Grid>

            <Grid Grid.Row="1">


                <ScrollView>
                    <Label 
                        Margin="10,10,0,0"
                        TextColor="Black"
                        FontSize="12"
                        FontAttributes="Bold"
                        FontFamily="FontTextLight"
                        VerticalOptions="Start"
                        HorizontalOptions="Start"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Legal.EULAContent}"/>
                </ScrollView>


            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>