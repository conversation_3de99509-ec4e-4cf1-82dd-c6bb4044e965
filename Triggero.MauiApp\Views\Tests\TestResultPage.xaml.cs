﻿using AppoMobi.Maui.Gestures;
using AppoMobi.Specials;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using Triggero.MauiMobileApp.Controls.Tests;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Extensions.Helpers.Modules;
using Triggero.MauiMobileApp.ViewModels;
using Triggero.Models.General.UserStats;
using Triggero.Models.Tests;

namespace Triggero.MauiMobileApp.Views.Pages.Tests
{

    public partial class TestResultPage : ContentPage
    {
        public ICommand GoToTests => new Command((ctx) =>
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {

                try
                {
                    if (_saveResult)
                    {
                        SaveResult();
                    }

                    //Страницы вопросов, результат и стартовая страница
                    for (int i = 0; i < _test.Questions.Count + 1; i++)
                    {
                        var page = App.Current.MainPage.Navigation.NavigationStack[App.Current.MainPage.Navigation.NavigationStack.Count - 2];
                        App.Current.MainPage.Navigation.RemovePage(page);
                    }

                    var page2 = App.Current.MainPage.Navigation.NavigationStack[App.Current.MainPage.Navigation.NavigationStack.Count - 1];
                    App.Current.MainPage.Navigation.RemovePage(page2);

                    App.GoBack(false);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }

                if (_saveResult)
                {
                    RerenderTestsView();
                }

            });
        });

        public ICommand DeleteResult => new Command((ctx) =>
        {

            Tasks.StartDelayed(TimeSpan.FromSeconds(0.5), () =>
            {
                void Action()
                {
                    try
                    {
                        _saveResult = false;

                        deleteResultBtn.IsVisible = false;

                        App.ShowToast($"Результат удален");

                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }
                }

                if (MainThread.IsMainThread)
                {
                    Action();
                }
                else
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        Action();
                    });
                }

            });
            ;
        });


        public ICommand Close => new Command((ctx) =>
        {

            if (TouchEffect.CheckLockAndSet())
                return;


            void Action()
            {
                try
                {
                    if (_saveResult)
                    {
                        SaveResult();
                    }

                    //Страницы вопросов, результат и стартовая страница
                    for (int i = 0; i < _test.Questions.Count + 1; i++)
                    {
                        var page = App.Current.MainPage.Navigation.NavigationStack[App.Current.MainPage.Navigation.NavigationStack.Count - 2];
                        App.Current.MainPage.Navigation.RemovePage(page);
                    }
                    var page2 = App.Current.MainPage.Navigation.NavigationStack[App.Current.MainPage.Navigation.NavigationStack.Count - 1];
                    App.Current.MainPage.Navigation.RemovePage(page2);

                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            }

            if (MainThread.IsMainThread)
            {
                Action();
            }
            else
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    Action();
                });
            }

            if (_saveResult)
            {
                RerenderTestsView();
            }

        });

        public ICommand PassAgain => new Command((ctx) =>
        {


            MainThread.BeginInvokeOnMainThread(async () =>
            {


                //Страницы вопросов, результат и стартовая страница
                App.OpenPage(new TestPage(_test));
                for (int i = 0; i < _test.Questions.Count + 2; i++)
                {
                    var page = App.Current.MainPage.Navigation.NavigationStack[App.Current.MainPage.Navigation.NavigationStack.Count - 2];
                    App.Current.MainPage.Navigation.RemovePage(page);
                }

            });
        });



        public ICommand CommandOpenReferenceLink
        {
            get
            {
                return new Command((context) =>
                {
                    if (!string.IsNullOrEmpty(_test.ReferenceLink))
                    {
                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            PlatformUi.Instance.OpenUrl(_test.ReferenceLink);
                        });

                    }

                });
            }
        }

        private Test _test;

        private double _scores;
        private Dictionary<TestScale, int> _scoresGroup;

        private TestResult _result;
        private List<TestResult> _results;

        private bool _saveResult = true;
        public TestResultPage(Test test, double scores)
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            _test = test;
            _scores = scores;

            if (Constants.ShowReferenceLinks && !string.IsNullOrEmpty(_test.ReferenceLink))
            {
                LabelExternalLink.IsVisible = true;
            }
        }



        public TestResultPage(Test test, Dictionary<TestScale, int> scoresGroup)
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            _test = test;
            _scoresGroup = scoresGroup;

        }


        protected override void OnAppearing()
        {
            base.OnAppearing();

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                switch (_test.Type)
                {

                case TestType.Simple:
                RenderSimpleTestScores();
                break;

                case TestType.Scaled:
                RenderScaledTestScores();
                break;

                case TestType.ScaledWithConditions:
                RenderScaledWithConditionsTestScores();
                break;
                }

                img.Source = await ResorcesHelper.GetImageSource(_test.ResultImgPath);
            });
        }

        private void RenderSimpleTestScores()
        {
            var yourResultStr = App.This.Interface.Tests.YourResult;
            var scoresStr = "";

            //Единственное и множественное число слова  баллы
            if (_scores > 5 || _scores == 0)
            {
                scoresStr = App.This.Interface.Tests.ScoresLowercasePlural;
            }
            else if (_scores > 1)
            {
                scoresStr = App.This.Interface.Tests.ScoresLowercaseMiddle;
            }
            else
            {
                scoresStr = App.This.Interface.Tests.ScoresLowercaseSingular;
            }

            resultLabel.Text = $"{yourResultStr} {_scores} {scoresStr}";

            _result = _test.GetTestResult(_scores);
            if (_result != null)
            {
                titleLabel.Text = _result.GetLocalizedTitle(LanguageHelper.LangCode);
                textLabel.Text = _result.GetLocalizedText(LanguageHelper.LangCode);
            }
        }

        private void RenderScaledTestScores()
        {
            scalesLayout.Children.Clear();
            var scales = new TestResultScales(_scoresGroup);
            scalesLayout.Children.Add(scales);

            simpleResultZone.IsVisible = false;
            titleLabel.IsVisible = false;
            textLabel.IsVisible = false;
            resultImgFrame.HeightRequest = 240;

            _result = _test.Results.FirstOrDefault(o => o.ConditionType == TestResultConditionType.NoCondition);
            if (_result is null)
            {
                _result = _test.Results.FirstOrDefault();
            }
            if (_result != null)
            {
                titleLabel.Text = _result.GetLocalizedTitle(LanguageHelper.LangCode);
                textLabel.Text = _result.GetLocalizedText(LanguageHelper.LangCode);

                var resultView = new TestResultView(_result);
                scalesResultsLayout.Children.Add(resultView);
            }
        }

        private void RenderScaledWithConditionsTestScores()
        {
            scalesLayout.Children.Clear();
            scalesResultsLayout.Children.Clear();

            simpleResultZone.IsVisible = false;
            titleLabel.IsVisible = false;
            textLabel.IsVisible = false;
            resultImgFrame.HeightRequest = 240;

            var scales = new TestResultScales(_scoresGroup);
            scalesLayout.Children.Add(scales);


            _results = _test.GetTestResults(_scoresGroup);
            foreach (var result in _results)
            {
                var resultView = new TestResultView(result);
                scalesResultsLayout.Children.Add(resultView);
            }

        }

        private string imgPath = "";
        public string ImgPath
        {
            get { return imgPath; }
            set { imgPath = value; OnPropertyChanged(nameof(ImgPath)); }
        }


        private void RerenderTestsView()
        {
            try
            {
                //reloading tests data to have check over completed test

                var mainPage = App.Current.MainPage.Navigation.NavigationStack.FirstOrDefault(o => o is MainPage) as MainPage;
                if (mainPage != null && mainPage.contentGrid.Children.Count > 0)
                {
                    if (mainPage.contentGrid.Children[0] is ListTestsView testsView)
                    {
                        if (testsView.BindingContext is ListTestsViewModel vm)
                        {
                            vm.LoadDataAsync().ConfigureAwait(false);
                        }
                    }
                    else
                    if (mainPage.contentGrid.Children[0] is FavoritesView favsView)
                    {
                        if (favsView.BindingContext is FavoritesViewModel vm)
                        {
                            vm.LoadDataAsync().ConfigureAwait(false);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }


        private void SaveResult()
        {
            Tasks.StartDelayed(TimeSpan.FromMilliseconds(50), async () =>
            {
                try
                {
                    var scoresGroup = new List<QuestionOptionScaleInfo>();

                    if (_scoresGroup != null)
                    {
                        foreach (var scale in _scoresGroup.Where(o => o.Key != null))
                        {
                            scoresGroup.Add(new QuestionOptionScaleInfo
                            {
                                ScaleId = scale.Key.Id,
                                Score = scale.Value
                            });
                        }
                    }

                    await StatsHelper.AddTestPassingResult(new TestPassingResult
                    {
                        TestId = _test.Id,
                        Score = _scores,
                        Scores = scoresGroup
                    });
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            });

        }



    }
}