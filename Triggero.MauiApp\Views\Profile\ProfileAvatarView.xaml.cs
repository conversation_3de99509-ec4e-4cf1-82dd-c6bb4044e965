﻿using AppoMobi.Specials;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows.Input;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models;
using Triggero.Models.General;


namespace Triggero.MauiMobileApp.Views.Pages.Profile
{
    public partial class ProfileAvatarView : ContentPage
    {
        private List<UserAvatar> avatars;

        public ProfileAvatarView()
        {
            try
            {
                InitializeComponent();
                NavigationPage.SetHasNavigationBar(this, false);

                //Tasks.StartDelayed(TimeSpan.FromMilliseconds(150), async () =>
                //{
                //    try
                //    {
                //        avatars = await ApplicationState.Data.GetAvatars();

                //        MainThread.BeginInvokeOnMainThread(async () =>
                //        {
                //            try
                //            {
                //                Disappearing += ProfileAvatarView_Disappearing;

                //                SetupAvatars();
                //            }
                //            catch (Exception e)
                //            {
                //                Super.Log(e);
                //                App.GoBack(false);
                //            }
                //        });
                //    }
                //    catch (Exception e)
                //    {
                //        Super.Log(e);
                //        App.GoBack(false);
                //    }
                //});
            }
            catch (Exception e)
            {
                Super.DisplayException(this, e);
            }
        }

        protected override async void OnAppearing()
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    if (avatars == null)
                    {
                        avatars = await ApplicationState.Data.GetAvatars();
                        Disappearing += ProfileAvatarView_Disappearing;
                    }

                    SetupAvatars();
                }
                catch (Exception e)
                {
                    Super.Log(e);
                    App.GoBack(false);
                }

            });

            base.OnAppearing();
        }

        /// <summary>
        /// Gets or sets the selected avatar index
        /// </summary>
        public int SelectedIndex
        {
            get { return selectedIndex; }
            set
            {
                if (selectedIndex != value)
                {
                    selectedIndex = value;
                    OnPropertyChanged(nameof(SelectedIndex));

                    if (dotsView != null && avatars != null)
                    {
                        dotsView.SetDots(avatars.Count, SelectedIndex);
                    }
                }
            }
        }

        /// <summary>
        /// Sets up the avatar carousel and dots view with the current user's selected avatar
        /// </summary>
        void SetupAvatars()
        {
            if (avatars != null)
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    var user = await AuthHelper.GetUser();

                    dotsView.DefaultHeightRequest = 8;
                    dotsView.DefaultWidthRequest = 8;
                    dotsView.SelectedHeightRequest = 8;
                    dotsView.SelectedWidthRequest = 8;

                    dotsView.SetDots(avatars.Count, 0);
                    dotsView.DotTapped += DotsView_DotTapped;

                    if (Wrappers.Count == 0)
                    {
                        Wrappers.AddRange(avatars.Select(x => new AvatarWrapper(x)));
                    }

                    await Task.Delay(10);

                    var found = avatars.FirstOrDefault(o => o.Id == user.AvatarId);
                    if (found != null)
                    {
                        SelectedIndex = avatars.IndexOf(found);
                        dotsView.SetDots(avatars.Count, SelectedIndex);
                    }

                    MainCarousel.IsVisible = true;
                });
        }




        private int selectedIndex = -1;


        private ObservableRangeCollection<AvatarWrapper> wrappers = new ();

        public ObservableRangeCollection<AvatarWrapper> Wrappers
        {
            get { return wrappers; }
            set { wrappers = value; }
        }


        private void DotsView_DotTapped(object sender, int e)
        {
            // carousel.SelectedIndex = e;
        }


        private async void ProfileAvatarView_Disappearing(object sender, EventArgs e)
        {
            Disappearing -= ProfileAvatarView_Disappearing;

            try
            {
                var selectedAvatar = Wrappers[SelectedIndex].Avatar;
                await AuthHelper.SetUserAvatar(selectedAvatar);
                GlobalEvents.OnUserPropertyChanged(AuthHelper.User);
            }
            catch (Exception exception)
            {
                Super.Log(exception);
            }
        }

        private ICommand close;

        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj => { await App.Current.MainPage.Navigation.PopAsync(); });
        }
    }
}