﻿using System.Linq;
using Triggero.MauiMobileApp.Extensions.Helpers;


namespace Triggero.MauiMobileApp.Views
{

    public partial class NewTutorialPage7 : ContentPage
    {
        public NewTutorialPage7()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        private RelayCommand goNext;
        public RelayCommand GoNext
        {
            get => goNext ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new NewTutorialPage8());
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    if (App.Current.MainPage.Navigation.NavigationStack.Contains(this))
                    {
                        try
                        {
                            App.ClosePage(this);
                        }
                        catch { }
                    }

                });
            });
        }
    }
}