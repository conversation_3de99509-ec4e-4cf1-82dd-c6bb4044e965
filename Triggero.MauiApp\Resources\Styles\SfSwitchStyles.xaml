﻿<?xml version="1.0" encoding="utf-8" ?>
<ResourceDictionary
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:buttons="http://schemas.syncfusion.com/maui">

    <Style
        x:Key="sf_switch_yellow"
        TargetType="buttons:SfSwitch">
        <Setter Property="VisualStateManager.VisualStateGroups">
            <Setter.Value>
                <VisualStateGroupList>
                    <VisualStateGroup x:Name="CommonStates">

                        <VisualState x:Name="On">
                            <VisualState.Setters>
                                <Setter Property="SwitchSettings">
                                    <Setter.Value>
                                        <buttons:SwitchSettings
                                            ThumbStroke="White"
                                            ThumbBackground="White"
                                            ThumbCornerRadius="13"
                                            ThumbHeightRequest="27"
                                            ThumbWidthRequest="27"
                                            TrackStroke="#FDCE72"
                                            TrackBackground="#FDCE72"
                                            TrackCornerRadius="15"
                                            TrackHeightRequest="31"
                                            TrackWidthRequest="51" />
                                    </Setter.Value>
                                </Setter>
                            </VisualState.Setters>
                        </VisualState>

                        <VisualState x:Name="Off">
                            <VisualState.Setters>
                                <Setter Property="SwitchSettings">
                                    <Setter.Value>
                                        <buttons:SwitchSettings
                                            ThumbStroke="White"
                                            ThumbBackground="White"
                                            ThumbCornerRadius="13"
                                            ThumbHeightRequest="27"
                                            ThumbWidthRequest="27"
                                            TrackStroke="#E9E9EB"
                                            TrackBackground="#E9E9EB"
                                            TrackCornerRadius="15"
                                            TrackHeightRequest="31"
                                            TrackWidthRequest="51" />
                                    </Setter.Value>
                                </Setter>
                            </VisualState.Setters>
                        </VisualState>

                        <VisualState x:Name="Indeterminate">
                            <VisualState.Setters>
                                <Setter Property="SwitchSettings">
                                    <Setter.Value>
                                        <buttons:SwitchSettings
                                            ThumbStroke="White"
                                            ThumbBackground="#E9E9EB"
                                            ThumbCornerRadius="13"
                                            ThumbHeightRequest="27"
                                            ThumbWidthRequest="27"
                                            TrackStroke="White"
                                            TrackBackground="#E9E9EB"
                                            TrackCornerRadius="15"
                                            TrackHeightRequest="31"
                                            TrackWidthRequest="51" />
                                    </Setter.Value>
                                </Setter>
                            </VisualState.Setters>
                        </VisualState>

                    </VisualStateGroup>
                </VisualStateGroupList>

            </Setter.Value>
        </Setter>
    </Style>




</ResourceDictionary>