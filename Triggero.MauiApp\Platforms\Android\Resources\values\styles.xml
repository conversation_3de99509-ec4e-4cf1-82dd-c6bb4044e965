<?xml version="1.0" encoding="utf-8"?>
<resources>
    
    <!-- Base application theme -->
    <style name="MainTheme" parent="Maui.SplashTheme">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorControlHighlight">@color/colorControlHighlight</item>
        <item name="colorControlActivated">@color/colorControlActivated</item>
        <item name="android:colorControlHighlight">@color/ripple_color</item>
        <item name="android:colorControlActivated">@color/colorAccent</item>

      <!-- Hide status bar and navigation bar for splash -->
      <item name="android:windowFullscreen">true</item>
      <item name="android:windowTranslucentNavigation">true</item>
      <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
      <item name="android:windowTranslucentStatus">true</item>
      
    </style>
    
    <!-- Button style with custom ripple -->
    <style name="CustomButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="rippleColor">@color/ripple_color</item>
        <item name="backgroundTint">@null</item>
        <item name="android:colorControlHighlight">@color/ripple_color</item>
    </style>
    
</resources>
