﻿using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiPort.Models;
using Triggero.Models.Abstractions;
using Triggero.Models.Tests.QuestionOptions;


namespace Triggero.Controls.Cards.Tests.Questions.Answers.Single
{

    public partial class MultipleTextAnswerOption : BaseQuestionOptionView
    {

        private string text;
        public string Text
        {
            get { return text; }
            set { text = value; OnPropertyChanged(nameof(Text)); }
        }

        public MultipleTextAnswerOption(QuestionOption option) : base(option)
        {
            InitializeComponent();
            Load();
        }
        private async void Load()
        {
            Text = (Option as SimpleQuestionOption).GetLocalizedText(LanguageHelper.LangCode);
            cb.IsChecked = IsChecked;

            onChecked(null, null);
        }




        private void onChecked(object sender, CheckedChangedEventArgs e)
        {
            checkedState.IsVisible = IsChecked;
            uncheckedState.IsVisible = !checkedState.IsVisible;
        }

        public event EventHandler OnCheckedChanged;

        private void onTapped(object sender, EventArgs e)
        {
            IsChecked = !IsChecked;
            OnCheckedChanged?.Invoke(this, e);
        }
    }
}