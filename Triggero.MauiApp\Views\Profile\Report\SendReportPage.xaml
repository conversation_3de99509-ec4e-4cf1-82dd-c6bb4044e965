﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
              
             x:Class="Triggero.MauiMobileApp.Views.Pages.Profile.Report.SendReportPage"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
             xmlns:buttons="http://schemas.syncfusion.com/maui"
             x:Name="this">
    <ContentPage.Content>
        <Grid>

            <Image
                Aspect="Fill"
                Source="trackerbggradient.png"/>

            <StackLayout
                Margin="20,50,20,0">

                <StackLayout
                    Spacing="21"
                    Orientation="Horizontal">
                    
                    <ImageButton 
                        Command="{Binding Source={x:Reference this},Path=Close}"
                        CornerRadius="0"
                        WidthRequest="8"
                        HeightRequest="16"
                        Source="arrowback.png"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        BackgroundColor="Transparent"/>

                    <Label 
                        TextColor="{x:StaticResource ColorText}"
                        FontSize="17"
                        FontAttributes="Bold"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileDownloadData.DownloadYourData}">
                        <Label.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=Close}"/>
                        </Label.GestureRecognizers>
                    </Label>

                </StackLayout>
                
               

                <Label 
                    Margin="14,35,0,0"
                    Opacity="0.6"
                    TextColor="{x:StaticResource ColorText}"
                    FontSize="16"
                    FontFamily="FontTextLight"
                    VerticalOptions="Center"
                    HorizontalOptions="Start"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileDownloadData.Description}"/>

                <Label 
                    Margin="14,35,0,0"
                    TextColor="{x:StaticResource ColorText}"
                    FontAttributes="Bold"
                    FontSize="16"
                    FontFamily="FontTextLight"
                    VerticalOptions="Center"
                    HorizontalOptions="Start"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileDownloadData.DataPeriodSelection}"/>


                <Frame
                    Margin="0,3,0,0"
                    HeightRequest="52"
                    VerticalOptions="Start"
                    CornerRadius="15"
                    BackgroundColor="#FFFFFF"
                    Padding="0"
                    HasShadow="False">
                    <Frame.Shadow>
                        <Shadow Brush="#27527A" Offset="2,2" Radius="12" Opacity="0.06" />
                    </Frame.Shadow>
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=SetPeriod}"/>
                    </Frame.GestureRecognizers>
                    <Grid>
                        <Label
                            x:Name="periodLabel"
                            Margin="14,0,0,0"
                            TextColor="{x:StaticResource ColorText}"
                            FontSize="17"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            Text=""/>

                        <Image
                            Aspect="Fill"
                            Opacity="0.5"
                            WidthRequest="7"
                            HeightRequest="13"
                            HorizontalOptions="End"
                            VerticalOptions="Center"
                            Margin="0,0,25,0"
                            Source="arrowforwardblack.png"/>

                    </Grid>
                </Frame>

                <Label 
                    Margin="14,35,0,0"
                    TextColor="{x:StaticResource ColorText}"
                    FontAttributes="Bold"
                    FontSize="16"
                    FontFamily="FontTextLight"
                    VerticalOptions="Center"
                    HorizontalOptions="Start"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileDownloadData.WhatDataToSend}"/>

                <Frame
                    Margin="0,3,0,0"
                    HeightRequest="204"
                    VerticalOptions="Start"
                    CornerRadius="15"
                    BackgroundColor="#FFFFFF"
                    Padding="0"
                    HasShadow="False">
                    <Frame.Shadow>
                        <Shadow Brush="#27527A" Offset="2,2" Radius="12" Opacity="0.06" />
                    </Frame.Shadow>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0">

                                <Label 
                                    Margin="16,0,0,0"
                                    TextColor="{x:StaticResource ColorText}"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    FontFamily="FontTextLight"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileDownloadData.MoodDiary}"/>

                                <buttons:SfSwitch 
                                    Visual="Custom"
                                    Style="{x:StaticResource sf_switch_yellow}"
                                    IsOn="{Binding Source={x:Reference this},Path=Settings.SendMoodDiary,Mode=TwoWay}"
                                    Margin="0,0,20,0"
                                    WidthRequest="51"
                                    HeightRequest="31"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center"/>

                                <BoxView
                                    Opacity="0.6"
                                    HorizontalOptions="Fill"
                                    VerticalOptions="End"
                                    Margin="16,0,0,0"
                                    HeightRequest="1"
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"/>

                            </Grid>

                            <Grid Grid.Row="1">

                                <Label 
                                    Margin="16,0,0,0"
                                    TextColor="{x:StaticResource ColorText}"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    FontFamily="FontTextLight"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileDownloadData.TestResults}"/>

                                <buttons:SfSwitch 
                                    Visual="Custom"
                                    Style="{x:StaticResource sf_switch_yellow}"
                                    IsOn="{Binding Source={x:Reference this},Path=Settings.SendTestPassingResults,Mode=TwoWay}"
                                    Margin="0,0,20,0"
                                    WidthRequest="51"
                                    HeightRequest="31"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center"/>

                                <BoxView
                                    Opacity="0.6"
                                    HorizontalOptions="Fill"
                                    VerticalOptions="End"
                                    Margin="16,0,0,0"
                                    HeightRequest="1"
                                    BackgroundColor="{x:StaticResource ColorPrimaryLight}"/>


                            </Grid>

                            <Grid Grid.Row="2">

                                <Label 
                                    Margin="16,0,0,0"
                                    TextColor="{x:StaticResource ColorText}"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    FontFamily="FontTextLight"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    WidthRequest="200"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileDownloadData.MoodDiaryStats}"/>

                                <buttons:SfSwitch 
                                    Visual="Custom"
                                    Style="{x:StaticResource sf_switch_yellow}"
                                    IsOn="{Binding Source={x:Reference this},Path=Settings.SendMoodDiaryStats,Mode=TwoWay}"
                                    Margin="0,0,20,0"
                                    WidthRequest="51"
                                    HeightRequest="31"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center"/>


                            </Grid>

                        </Grid>
                </Frame>



                <Frame
                    Margin="0,40,0,0"
                    HeightRequest="56"
                    VerticalOptions="Start"
                    CornerRadius="15"
                    BackgroundColor="#FFFFFF"
                    Padding="0"
                    HasShadow="False">
                    <Frame.Shadow>
                        <Shadow Brush="#27527A" Offset="2,2" Radius="12" Opacity="0.06" />
                    </Frame.Shadow>
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=SetEmail}"/>
                    </Frame.GestureRecognizers>
                    <Grid>
                        <Label
                            x:Name="emailLabel"
                            Margin="14,0,0,0"
                            TextColor="{x:StaticResource ColorText}"
                            FontSize="16"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            Text=""/>
                    </Grid>
                </Frame>
                
                <Button 
                    Command="{Binding Source={x:Reference this},Path=SendReport}"
                    VerticalOptions="Center"
                    HorizontalOptions="Fill"
                    Margin="0,12,0,0"
                    Style="{x:StaticResource yellow_btn}"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileDownloadData.Send}"/>


            </StackLayout>

        </Grid>
    </ContentPage.Content>
</ContentPage>