﻿
using MobileAPIWrapper;
using System.Windows.Input;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Views.Pages.Auth;
using Triggero.MauiMobileApp.Views.Pages.Legal;
using Triggero.MauiMobileApp.Views.Pages.Profile.Notifications;
using Triggero.MauiMobileApp.Views.Pages.Profile.Report;
using Triggero.MauiMobileApp.Views.Pages.Subscriptions;
using Triggero.Models.General;

namespace Triggero.MauiMobileApp.Views.Pages.Profile
{

    public partial class ProfilePage : ContentPage
    {
        private string oldName;
        public ProfilePage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
            GlobalEvents.UserPropertyChanged += GlobalEvents_UserPropertyChanged;

            var user = AuthHelper.User;
            avatar.Source = App.GetFullImageUrl(user.Avatar.AvatarPath, ThumbnailSize.Small, ThumbnailType.Png);
            nameTextEdit.Text = user.Name;
            emailLabel.Text = user.Email;
        }

        private void GlobalEvents_UserPropertyChanged(object sender, User e)
        {
            Load(e);
        }

        protected override async void OnAppearing()
        {
            var user = await AuthHelper.GetUser();
            Load(user);
        }

        private async void Load(User user)
        {
            if (user == null)
                return;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    triggeroDaysCountLabel.Text = user.UserStatistics.GetDaysInTriggero(user.RegisteredAt).ToString();
                    testPassedCountLabel.Text = user.UserStatistics.TestPassingResultsCount.ToString();
                    practicesPassedCountLabel.Text = user.UserStatistics.PracticePassingResultsCount.ToString();

                    oldName = user.Name;
                    nameTextEdit.Text = user.Name;

                    avatar.Source = App.GetFullImageUrl(user.Avatar.AvatarPath, ThumbnailSize.Small, ThumbnailType.Png);
                    //avatar.Source = await ResorcesHelper.GetImageSource(user.Avatar.AvatarPath);
                    emailLabel.Text = user.Email;

                    if (!Constants.IsFreeVersion)
                    {
                        subGrid.IsVisible = true;
                    }

                }
                catch (Exception ex)
                {
                    Super.Log(ex);
                }

            });

        }

        #region Edit name
        private bool isEditing;
        public bool IsEditing
        {
            get { return isEditing; }
            set { isEditing = value; OnPropertyChanged(nameof(IsEditing)); }
        }


        private bool nameWasChanged = false;

        /*
        private ICommand toggleEditing;
        public ICommand ToggleEditing
        {
            get => toggleEditing ??= new RelayCommand(async obj =>
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {

                    IsEditing = !IsEditing;
                    if (IsEditing)
                    {
                        toggleEditingBtn.Margin = new Thickness(15, 0, 0, 0);
                        nameTextEdit.Focus();
                    }
                    else
                    {
                        if (!nameWasChanged)
                        {
                            toggleEditingBtn.Margin = new Thickness(15, 0, 0, 0);
                        }
                        else
                        {
                            toggleEditingBtn.Margin = new Thickness(15, 0, 0, 0);
                            await AuthHelper.ChangeUserName(nameTextEdit.Text);
                        }
                    }

                });

            });
        }
        */

        private ICommand nameChanged;
        public ICommand NameChanged
        {
            get => nameChanged ??= new RelayCommand(async obj =>
            {
                if (obj is null) return;

                if (!nameWasChanged)
                {
                    nameWasChanged = (string)obj != oldName && !string.IsNullOrEmpty((string)obj);
                }

            });
        }

        #endregion

        #region Avatar
        private ICommand showAvatars;
        public ICommand ShowAvatars
        {
            get => showAvatars ??= new RelayCommand(async obj =>
            {
                try
                {
                    var page = new ProfileAvatarView();
                    App.OpenPage(page);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            });
        }
        #endregion

        #region Navigation

        private ICommand goToSubscription;
        public ICommand GoToSubscription
        {
            get => goToSubscription ??= new RelayCommand(async obj =>
            {
                if (AuthHelper.UserSubscription == SubscriptionType.None)
                {
                    App.OpenPage(new SelectTrialPage());
                }
                else
                {
                    App.OpenNeedToPayNow();
                }
            });
        }

        private ICommand goToDownloadData;
        public ICommand GoToDownloadData
        {
            get => goToDownloadData ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new SendReportPage());
            });
        }

        private ICommand goToNotifications;
        public ICommand GoToNotifications
        {
            get => goToNotifications ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new NotificationsPage());
            });
        }
        private ICommand goToSupport;
        public ICommand GoToSupport
        {
            get => goToSupport ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new SupportPage());
            });
        }
        private ICommand goToTerms;
        public ICommand GoToTerms
        {
            get => goToTerms ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new TermsPage());
            });
        }
        private ICommand goToPrivacy;
        public ICommand GoToPrivacy
        {
            get => goToPrivacy ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new PrivacyPage());
            });
        }
        #endregion


        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await App.Current.MainPage.Navigation.PopAsync();
                });

            });
        }

        private ICommand logout;
        public ICommand Logout
        {
            get => logout ??= new RelayCommand(async obj =>
            {
                AuthHelper.Logout();

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    App.Current.MainPage.Navigation.InsertPageBefore(new LoginStartPage(), App.Current.MainPage.Navigation.NavigationStack[0]);
                    await App.Current.MainPage.Navigation.PopToRootAsync(false);

                });

            });
        }
        private ICommand deleteAccount;
        public ICommand DeleteAccount
        {
            get => deleteAccount ??= new RelayCommand(async obj =>
            {


                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    var result = await DisplayAlert("Внимание", "Вы действительно хотите удалить аккаунт? Данное действие нельзя будет отменить", "Удалить", "Назад");
                    if (result)
                    {
                        await TriggeroMobileAPI.GeneralMethods.UserMethods.UsersMethods.DeleteUserAccount(AuthHelper.UserId);

                        AuthHelper.Logout();
                        App.Current.MainPage.Navigation.InsertPageBefore(new LoginStartPage(), App.Current.MainPage.Navigation.NavigationStack[0]);
                        await App.Current.MainPage.Navigation.PopToRootAsync(false);
                    }

                });

            });
        }
    }
}