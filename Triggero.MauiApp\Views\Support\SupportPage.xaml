﻿<?xml version="1.0" encoding="utf-8"?>

<pages:BasePage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
                xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                x:Name="this"
                BackgroundColor="White"
                xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
                xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
                xmlns:androidSpecific="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.AndroidSpecific;assembly=Microsoft.Maui.Controls"
                xmlns:views="http://schemas.appomobi.com/drawnUi/2023/draw"
                xmlns:pages="clr-namespace:Triggero.MauiMobileApp.Views.Pages"
                androidSpecific:Application.WindowSoftInputModeAdjust="Resize"
                x:Class="Triggero.MauiMobileApp.Views.Pages.SupportPage">
    <pages:BasePage.Content>

        <Grid
            VerticalOptions="Fill"
            HorizontalOptions="Fill"
            Grid.RowSpacing="0"
            Margin="20,0,20,0">

            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition Height="90" />
                <RowDefinition Height="*" />
                <RowDefinition x:Name="sendMsgRowDefinition" Height="100" />
                <RowDefinition x:Name="rowKeyboard" Height="0" />
            </Grid.RowDefinitions>

            <Grid Grid.Row="1"
                  VerticalOptions="Fill"
                  HorizontalOptions="Fill">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="50" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">
                    <Image
                        WidthRequest="47"
                        HeightRequest="47"
                        HorizontalOptions="Center"
                        VerticalOptions="Center"
                        Source="chatbotavatar.png" />
                </Grid>

                <Grid Grid.Column="1">
                    <StackLayout
                        Spacing="0"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        Margin="15,0,0,0">

                        <Label
                            TextColor="{x:StaticResource ColorText}"
                            FontSize="17"
                            FontAttributes="Bold"
                            Margin="0,0,0,0"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Support.TriggeroSupport}" />


                        <StackLayout
                            Spacing="4"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            Orientation="Horizontal">

                            <BoxView
                                WidthRequest="8"
                                HeightRequest="8"
                                CornerRadius="4"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Background="#34C759" />

                            <Label
                                TextColor="{x:StaticResource ColorText}"
                                Opacity="0.5"
                                FontSize="12"
                                Margin="0,0,0,0"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Support.Online}" />
                        </StackLayout>

                    </StackLayout>
                </Grid>

                <ImageButton
                    Command="{Binding Source={x:Reference this},Path=Close}"
                    Grid.Column="1"
                    CornerRadius="0"
                    WidthRequest="14"
                    HeightRequest="14"
                    Source="close.png"
                    Margin="0,0,0,21"
                    HorizontalOptions="End"
                    VerticalOptions="Center"
                    BackgroundColor="Transparent" />

            </Grid>

            <Grid Grid.Row="2"
                  VerticalOptions="Fill"
                  HorizontalOptions="Fill">
                <ScrollView x:Name="messagesScrollView"
                            VerticalOptions="End"
                            HorizontalOptions="Fill">

                    <StackLayout 
                        Spacing="8"
                        x:Name="messagesLayout">

                    </StackLayout>
                </ScrollView>
            </Grid>

            <Grid Grid.Row="3">

                <Grid
                    VerticalOptions="Start"
                    ColumnSpacing="6"
                    HeightRequest="100">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="45" />
                    </Grid.ColumnDefinitions>

                    <!--grayTextEditor-->
                    <Grid
                        HeightRequest="40">

                        <views:Canvas 
                            HorizontalOptions="Fill" VerticalOptions="Fill">

                            <views:SkiaShape
                                HorizontalOptions="Fill" VerticalOptions="Fill"
                                UseCache="Operations"
                                StrokeColor="#DEEAF6"
                                StrokeWidth="1.5"
                                CornerRadius="16" />

                        </views:Canvas>

                        <Editor
                            Keyboard="Chat"
                            IsTextPredictionEnabled="False"
                            IsSpellCheckEnabled="False"
                            Margin="10,8,10,8"
                            Focused="textEditFocused"
                            Unfocused="textEditUnfocused"
                            Text="{Binding Source={x:Reference this},Path=Message,Mode=TwoWay}"
                            Style="{x:StaticResource grayTextEditor}"
                            VerticalOptions="Center"
                            HeightRequest="-1"
                            HorizontalOptions="Fill"
                            Placeholder="{Binding Source={x:Static mobile:App.This},Path=Interface.Support.MessagePlaceholder}">
                        </Editor>

                    </Grid>


                    <ImageButton
                        Command="{Binding Source={x:Reference this},Path=SendMessage}"
                        Grid.Column="1"
                        Source="chatsendbtn.png"
                        BackgroundColor="Transparent"
                        HeightRequest="40"
                        WidthRequest="40" />

                </Grid>

            </Grid>

        </Grid>

    </pages:BasePage.Content>
</pages:BasePage>