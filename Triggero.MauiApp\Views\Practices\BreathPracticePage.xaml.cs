﻿using DrawnUi.Draw;
using Plugin.Maui.Audio;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers.Modules;
using Triggero.Models.Practices;


namespace Triggero.MauiMobileApp.Views.Pages
{
    public partial class BreathPracticePage : ContentPage //, IDisposable
    {

#if PREVIEWS
        [HotPreview.Preview]
        public static void Preview()
        {
            var practice = new BreathPractice()
            {
            };

            App.OpenPage(new BreathPracticePage(practice));
        }
#endif

        public BreathPracticePage(BreathPractice breathPractice)
        {
            BreathPractice = breathPractice;
            _timeLeft = TimeSpan.FromMinutes(BreathPractice.TimeMinutes);

            InitializeComponent();

            NavigationPage.SetHasNavigationBar(this, false);

            StatsHelper.AddBreathPracticePassingResult(BreathPractice.Id);


            //_player = new MediaElement()
            //{
            //    Aspect = Aspect.AspectFill,
            //    AutoPlay = false,
            //    HorizontalOptions = LayoutOptions.Fill,
            //    VerticalOptions = LayoutOptions.Fill,
            //    IsLooping = true,
            //    ShowsPlaybackControls = false,
            //    Source = "ms-appx:///breath.mp4"
            //};
        }


        private bool _rendererSet;

        protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            base.OnPropertyChanged(propertyName);

            if (propertyName == "Renderer")
            {
                if (_rendererSet)
                {
                    Dispose();
                }
                else
                {
                    _rendererSet = true;
                }
            }
        }

        public void Dispose()
        {
            DrawnCanvas.DisableUpdates();
            DrawnCanvas?.Dispose();
            if (BindingContext is IDisposable dispose)
            {
                dispose.Dispose();
            }
        }

        private TimeSpan _timeLeft;
        private BreathPracticeCycleStage _stage = BreathPracticeCycleStage.Inhale;
        private int secondsInStage;

        private BreathPractice breathPractice;

        public BreathPractice BreathPractice
        {
            get { return breathPractice; }
            set
            {
                breathPractice = value;
                OnPropertyChanged(nameof(BreathPractice));
            }
        }

        //        private MediaElement _player;

        bool once;

        protected override void OnAppearing()
        {
            base.OnAppearing();

            PlatformUi.Instance.HideStatusBar();

            MainThread.BeginInvokeOnMainThread(async () => { Super.KeepScreenOn = true; });

            if (!once)
            {
                once = true;
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    //_player.Play();

                    //PlayerContainer.Content = _player;

                    var pause = 700; //on iOS this will be triggered BEFORE the page is shown, so we delay a bit
                    if (Device.RuntimePlatform == Device.Android)
                        pause = 10;
                    await Task.Delay(pause);

                    await ContentStack.FadeTo(1, 700);

                    BreathAnimation.Start(10);
                    Device.StartTimer(TimeSpan.FromSeconds(1), OnTimerTick);
                });
            }
        }

        protected override void OnDisappearing()
        {
            base.OnDisappearing();

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    Super.KeepScreenOn = false;

                    //_player.Stop();
                    _closeTimer = true;

                    await App.Current.MainPage.Navigation.PopAsync();
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }
            });
        }


        private bool OnTimerTick()
        {
            _timeLeft.Subtract(TimeSpan.FromSeconds(1));
            timeLabel.Text = _timeLeft.ToString(@"mm\:ss");
            secondsInStage++;

            _timeLeft = _timeLeft.Subtract(TimeSpan.FromSeconds(1));

            counterLabel.Text = $"{secondsInStage}";

            switch (_stage)
            {
                case BreathPracticeCycleStage.Inhale:

                    if (BreathPractice.InhaleSeconds < 3)
                        actionLabel.Text = App.This.Interface.Library.BreathPractice.MakeQuickInhale;
                    else if (BreathPractice.InhaleSeconds < 5)
                        actionLabel.Text = App.This.Interface.Library.BreathPractice.MakeMiddleInhale;
                    else
                        actionLabel.Text = App.This.Interface.Library.BreathPractice.MakeSlowInhale;

                    if (secondsInStage > BreathPractice.InhaleSeconds)
                    {
                        secondsInStage = 0;
                        _stage = BreathPracticeCycleStage.Delay;
                    }

                    break;
                case BreathPracticeCycleStage.Delay:

                    actionLabel.Text = App.This.Interface.Library.BreathPractice.MakeBreathDelay;

                    if (secondsInStage > BreathPractice.DelaySeconds)
                    {
                        secondsInStage = 0;
                        _stage = BreathPracticeCycleStage.Exhale;
                    }

                    break;
                case BreathPracticeCycleStage.Exhale:

                    if (BreathPractice.InhaleSeconds < 3)
                        actionLabel.Text = App.This.Interface.Library.BreathPractice.MakeQuickExhale;
                    else if (BreathPractice.InhaleSeconds < 5)
                        actionLabel.Text = App.This.Interface.Library.BreathPractice.MakeMiddleExhale;
                    else
                        actionLabel.Text = App.This.Interface.Library.BreathPractice.MakeSlowExhale;


                    if (secondsInStage > BreathPractice.ExhaleSeconds)
                    {
                        secondsInStage = 0;
                        _stage = BreathPracticeCycleStage.Inhale;
                    }

                    break;
            }

            if (_timeLeft.TotalSeconds < 1 || _closeTimer)
            {
                actionLabel.Text = App.This.Interface.Library.BreathPractice.PracticeFinished;
                timeLabel.Text = TimeSpan.Zero.ToString(@"mm\:ss");
                counterLabel.Text = $"";

                return false;
            }

            return true;
        }

        bool _closeTimer;


        private ICommand close;


        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj => { await App.Current.MainPage.Navigation.PopAsync(); });
        }
    }
}