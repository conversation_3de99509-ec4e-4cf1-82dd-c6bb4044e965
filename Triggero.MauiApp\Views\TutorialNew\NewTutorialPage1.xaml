﻿<?xml version="1.0" encoding="utf-8"?>

<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage1"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">

    <Grid VerticalOptions="Fill">

        <Image
            VerticalOptions="Fill"
            HorizontalOptions="Fill"
            Aspect="Fill"
            Source="tutorialblur1.png" />

        <ScrollView VerticalOptions="Fill">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="170" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="140" />
                </Grid.RowDefinitions>

                <Grid Grid.Row="0" Padding="0,0">

                    <Grid
                        VerticalOptions="End"
                        HeightRequest="125">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="3*" />
                            <ColumnDefinition Width="1*" />
                        </Grid.ColumnDefinitions>

                        <Image
                            Grid.ColumnSpan="2"
                            Aspect="Fill"
                            Source="tutorialprofilecontainer.png" />

                        <Grid Grid.Column="0">

                            <StackLayout
                                Spacing="12"
                                HeightRequest="66"
                                VerticalOptions="Center"
                                Orientation="Horizontal">

                                <Frame
                                    Margin="20,0,0,0"
                                    CornerRadius="33"
                                    Padding="0"
                                    IsClippedToBounds="True"
                                    BackgroundColor="#DEEDF9"
                                    BorderColor="{x:StaticResource ColorPrimary}"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    HeightRequest="66"
                                    WidthRequest="66"
                                    HasShadow="False">
                                    <Image
                                        x:Name="avatar"
                                        HorizontalOptions="Start"
                                        VerticalOptions="Center"
                                        HeightRequest="66"
                                        WidthRequest="66" />
                                </Frame>

                                <StackLayout
                                    HorizontalOptions="Fill"
                                    VerticalOptions="Center"
                                    Spacing="8">
                                    <Label
                                        x:Name="hiName"
                                        TextColor="{x:StaticResource ColorText}"
                                        FontSize="16"
                                        FontAttributes="Bold"
                                        HorizontalOptions="Start"
                                        Text="Привет, Кристина" />

                                    <Label
                                        TextColor="{x:StaticResource ColorText}"
                                        HorizontalOptions="Start"
                                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage1.WhatWillDo}" />

                                </StackLayout>

                            </StackLayout>
                        </Grid>

                        <Grid Grid.Column="1">
                            <StackLayout
                                Margin="0,0,20,0"
                                HorizontalOptions="End"
                                VerticalOptions="Center"
                                Spacing="20"
                                Orientation="Horizontal">
                                <ImageButton
                                    BackgroundColor="Transparent"
                                    WidthRequest="20"
                                    HeightRequest="20"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Center"
                                    Source="search.png" />
                                <ImageButton
                                    BackgroundColor="Transparent"
                                    WidthRequest="20"
                                    HeightRequest="20"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Center"
                                    Source="likeset.png" />
                            </StackLayout>
                        </Grid>

                    </Grid>

                </Grid>

                <Grid Grid.Row="1" Padding="10,0">

                    <StackLayout
                        Margin="0,40,0,0"
                        Spacing="20">
                        <Label
                            Margin="0,0,0,0"
                            TextColor="#000000"
                            FontAttributes="Bold"
                            FontSize="19"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="317"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage1.Header}" />
                        <Label
                            Margin="0,0,0,0"
                            TextColor="{x:StaticResource ColorText}"
                            FontSize="16"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="307"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage1.Description}" />


                    </StackLayout>

                </Grid>

                <Grid Grid.Row="2">
                    <Button
                        Command="{Binding Source={x:Reference this},Path=GoNext}"
                        VerticalOptions="Start"
                        HorizontalOptions="Fill"
                        Margin="63,0,63,0"
                        Style="{x:StaticResource yellow_btn}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage1.GoNext}" />

                </Grid>

            </Grid>
        </ScrollView>

    </Grid>
</ContentPage>