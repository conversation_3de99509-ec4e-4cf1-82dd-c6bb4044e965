using System.Collections.Generic;
using System.Threading.Tasks;
using Shiny.Push;
using AppoMobi.Specials;
using Triggero.MauiMobileApp.Helpers;
using MobileAPIWrapper;
using Triggero.MauiPort.Views.Pages;

namespace Triggero.MauiMobileApp.Services
{
    /// <summary>
    /// Shiny.net push notification delegate for handling Firebase push notifications
    /// </summary>
    public class PushNotificationDelegate : IPushDelegate
    {
        /// <summary>
        /// Fires when the user taps on a push notification
        /// </summary>
        /// <param name="notification">Push notification</param>
        /// <returns>Task</returns>
        public async Task OnEntry(PushNotification notification)
        {
            try
            {
                Super.Log($"[PUSH] OnEntry - User tapped notification: {notification?.Notification?.Title}");

                // Handle notification tap - navigate to specific page based on notification data
                await MainThread.InvokeOnMainThreadAsync(async () =>
                {
                    try
                    {
                        if (notification?.Data?.ContainsKey("type") == true)
                        {
                            var notificationType = notification.Data["type"];
                            Super.Log($"[PUSH] Handling notification type: {notificationType}");

                            switch (notificationType.ToLower())
                            {
                                case "reminder":
                                case "practice_reminder":
                                    // Navigate to practices/breath section
                                    NavigateToMainPageSection("practices");
                                    break;

                                case "update":
                                case "news":
                                    // Navigate to main/home section
                                    NavigateToMainPageSection("home");
                                    break;

                                case "chat":
                                case "chatbot":
                                    // Navigate to chatbot section
                                    NavigateToMainPageSection("chatbot");
                                    break;

                                case "test":
                                case "assessment":
                                    // Navigate to tests section
                                    NavigateToMainPageSection("tests");
                                    break;

                                default:
                                    // Navigate to main page by default
                                    NavigateToMainPageSection("home");
                                    break;
                            }
                        }
                        else
                        {
                            // No specific type, navigate to main page
                            NavigateToMainPageSection("home");
                        }
                    }
                    catch (Exception navEx)
                    {
                        Super.Log($"[PUSH] Error in navigation: {navEx}");
                    }
                });
            }
            catch (Exception ex)
            {
                Super.Log($"[PUSH] Error in OnEntry: {ex}");
            }
        }

        private void NavigateToMainPageSection(string section)
        {
            try
            {
                // This mimics the Xamarin app's navigation logic
                // The main page should handle switching to the appropriate section
                if (App.Current?.MainPage != null)
                {
                    Super.Log($"[PUSH] Navigating to section: {section}");

                    // If we're not on the main page, navigate to it first
                    if (App.Current.MainPage.GetType().Name != "MainPage")
                    {
                        App.SetMainPage(new MainPage());
                    }

                    // Send a message to the main page to switch sections
                    MessagingCenter.Send<object, string>(this, "NavigateToSection", section);
                }
            }
            catch (Exception ex)
            {
                Super.Log($"[PUSH] Error in NavigateToMainPageSection: {ex}");
            }
        }

        /// <summary>
        /// Fires when a push notification is received while app is running
        /// iOS: requires content-available: 1 in payload
        /// Android: requires data portion in payload
        /// </summary>
        /// <param name="notification">Push notification</param>
        /// <returns>Task</returns>
        public async Task OnReceived(PushNotification notification)
        {
            try
            {
                Super.Log($"[PUSH] OnReceived - Notification received: {notification?.Notification?.Title}");

                // Handle background notification processing
                // This is where you can update app state, refresh data, etc.

                if (notification?.Data != null)
                {
                    // Check for data refresh requests
                    if (notification.Data.ContainsKey("refresh") && notification.Data["refresh"] == "true")
                    {
                        Super.Log($"[PUSH] Background refresh requested");
                        await RefreshAppData();
                    }

                    // Check for user data updates
                    if (notification.Data.ContainsKey("user_update") && notification.Data["user_update"] == "true")
                    {
                        Super.Log($"[PUSH] User data update requested");
                        await RefreshUserData();
                    }

                    // Check for subscription updates
                    if (notification.Data.ContainsKey("subscription_update"))
                    {
                        Super.Log($"[PUSH] Subscription update received");
                        await RefreshUserData(); // This will reload subscription status
                    }

                    // Log all notification data for debugging
                    foreach (var kvp in notification.Data)
                    {
                        Super.Log($"[PUSH] Data: {kvp.Key} = {kvp.Value}");
                    }
                }

                // The notification will be shown automatically by the system
                // We don't need to create local notifications here unless we want custom behavior
            }
            catch (Exception ex)
            {
                Super.Log($"[PUSH] Error in OnReceived: {ex}");
            }
        }

        private async Task RefreshAppData()
        {
            try
            {
                Super.Log($"[PUSH] Refreshing app data in background");

                // Refresh any cached data that might have changed
                // This could include content updates, new exercises, etc.

                // Example: Refresh content from API
                // await ContentService.RefreshContent();

                // Send message to UI to update if app is active
                MessagingCenter.Send<object>(this, "RefreshAppData");
            }
            catch (Exception ex)
            {
                Super.Log($"[PUSH] Error refreshing app data: {ex}");
            }
        }

        private async Task RefreshUserData()
        {
            try
            {
                Super.Log($"[PUSH] Refreshing user data in background");

                // Refresh user-specific data like subscription status, profile, etc.
                if (AuthHelper.User?.Id > 0)
                {
                    await AuthHelper.ReloadUser();
                    Super.Log($"[PUSH] User data refreshed for user {AuthHelper.User.Id}");

                    // Send message to UI to update if app is active
                    MessagingCenter.Send<object>(this, "RefreshUserData");
                }
            }
            catch (Exception ex)
            {
                Super.Log($"[PUSH] Error refreshing user data: {ex}");
            }
        }

        /// <summary>
        /// Fires when a push registration token changes
        /// Also fires with RequestAccess value changes (or initial request)
        /// </summary>
        /// <param name="token">New push token</param>
        /// <returns>Task</returns>
        public async Task OnNewToken(string token)
        {
            try
            {
                Super.Log($"[PUSH] OnNewToken - New token received: {token?.Substring(0, Math.Min(20, token?.Length ?? 0))}...");
                
                // Send the new token to your backend server
                if (!string.IsNullOrEmpty(token) && AuthHelper.User?.Id > 0)
                {
                    await TriggeroMobileAPI.Common.AddUserDeviceIfNew(AuthHelper.User.Id, token);
                    Super.Log($"[PUSH] Token sent to server for user {AuthHelper.User.Id}");
                }
            }
            catch (Exception ex)
            {
                Super.Log($"[PUSH] Error in OnNewToken: {ex}");
            }
        }

        /// <summary>
        /// Fires when IPushManager.UnRegister is called
        /// or on startup when permissions are denied to push
        /// </summary>
        /// <param name="token">Unregistered token</param>
        /// <returns>Task</returns>
        public async Task OnUnRegistered(string token)
        {
            try
            {
                Super.Log($"[PUSH] OnUnRegistered - Token unregistered: {token?.Substring(0, Math.Min(20, token?.Length ?? 0))}...");

                // Notify backend that this device should no longer receive notifications
                if (!string.IsNullOrEmpty(token) && AuthHelper.User?.Id > 0)
                {
                    try
                    {
                        // TODO: Implement RemoveUserDevice method in CommonMethods API
                        // For now, we'll just log the unregistration - the server will handle expired tokens
                        Super.Log($"[PUSH] Token unregistered for user {AuthHelper.User.Id}: {token?.Substring(0, Math.Min(20, token?.Length ?? 0))}...");

                        // When RemoveUserDevice API method is implemented, uncomment this:
                        // await TriggeroMobileAPI.Common.RemoveUserDevice(AuthHelper.User.Id, token);
                        // Super.Log($"[PUSH] Token removed from server for user {AuthHelper.User.Id}");
                    }
                    catch (Exception apiEx)
                    {
                        Super.Log($"[PUSH] Failed to process token unregistration: {apiEx}");
                        // Don't throw - this is not critical for app functionality
                    }
                }
            }
            catch (Exception ex)
            {
                Super.Log($"[PUSH] Error in OnUnRegistered: {ex}");
            }
        }
    }
}
