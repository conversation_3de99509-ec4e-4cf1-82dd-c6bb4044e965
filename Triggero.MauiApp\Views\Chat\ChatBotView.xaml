﻿<?xml version="1.0" encoding="UTF-8"?>

<ContentView
    x:Class="Triggero.MauiMobileApp.Views.ChatBotView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
    x:Name="ThisView">

    <ContentView.Content>

        <Grid
            HorizontalOptions="Fill" VerticalOptions="Fill"
            Margin="20,30,20,0"
            ColumnSpacing="0"
            RowSpacing="0">

            <Grid.RowDefinitions>
                <RowDefinition Height="90" />
                <RowDefinition Height="*" />
                <RowDefinition
                    x:Name="interactionsRowDef"
                    Height="70" />
                <RowDefinition
                    x:Name="padding"
                    Height="{x:Static triggeroV2:Globals.BottomOffsetForTabs}" />
            </Grid.RowDefinitions>

            <!--CHAT HEADER-->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="50" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">
                    <Image
                        HeightRequest="47"
                        HorizontalOptions="Center"
                        Source="chatbotavatar.png"
                        VerticalOptions="Center"
                        WidthRequest="47" />

                </Grid>

                <Grid Grid.Column="1">

                    <StackLayout
                        Margin="15,0,0,0"
                        HorizontalOptions="Start"
                        Spacing="0"
                        VerticalOptions="Center">

                        <Label
                            x:Name="xxxLabel"
                            Margin="0,0,0,0"
                            FontAttributes="Bold"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="Triggero"
                            TextColor="{x:StaticResource ColorText}"
                            VerticalOptions="Center" />

                        <Label
                            Margin="0,0,0,0"
                            FontSize="12"
                            HorizontalOptions="Start"
                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.ChatBot.VirtualAssistant}"
                            TextColor="{x:StaticResource ColorTextGray}"
                            VerticalOptions="Center" />

                        <StackLayout
                            HorizontalOptions="Start"
                            Orientation="Horizontal"
                            Spacing="4"
                            VerticalOptions="Center">

                            <!--ONLINE-->
                            <BoxView
                                Background="#34C759"
                                CornerRadius="4"
                                HeightRequest="8"
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                WidthRequest="8" />

                            <Label
                                Margin="0,0,0,0"
                                FontSize="12"
                                HorizontalOptions="Start"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.ChatBot.Online}"
                                TextColor="{x:StaticResource ColorTextGray}"
                                VerticalOptions="Center" />

                        </StackLayout>

                    </StackLayout>
                </Grid>

            </Grid>

            <!--messagesScrollView-->

            <!--MESSAGES-->
            <controls:ChatScroll
                Grid.Row="1"
                Orientation="Vertical"
                HorizontalOptions="Fill" VerticalOptions="Fill"
                x:Name="messagesScrollView"
                Scrolled="onMessagesScrollViewScrolled"
                VerticalScrollBarVisibility="Never">

                <StackLayout
                    x:Name="messagesLayout"
                    Spacing="8"
                    Margin="0,0,0,20"
                    HorizontalOptions="Fill">

                </StackLayout>
            </controls:ChatScroll>


            <!--interactionsGrid-->
            <Grid
                x:Name="interactionsGrid"
                Grid.Row="2"
                BackgroundColor="Transparent" />


        </Grid>
    </ContentView.Content>
</ContentView>