﻿using AppoMobi.Specials;

using System.Linq;
using System.Windows.Input;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Services;
using Triggero.MauiMobileApp.ViewModels;
using Triggero.MauiMobileApp.Views.Pages;

namespace Triggero.MauiMobileApp.Views
{

    public partial class FavoritesView : ContentView, IDisposable
    {
        public void Dispose()
        {
            DrawnCanvas.DisableUpdates();
            DrawnCanvas?.Dispose();
            if (BindingContext is IDisposable dispose)
            {
                dispose.Dispose();
            }

            App.Messager.Unsubscribe(this, "FavChanged");
        }


        private readonly FavoritesViewModel _vm;

        public FavoritesView()
        {
            _vm = new FavoritesViewModel();

            InitializeComponent();

            BindingContext = _vm;

            _vm.Section = FavoriteSectionType;

            //RenderControls();

            Tasks.StartDelayed(TimeSpan.FromMilliseconds(30), async () =>
            {
                _vm.ClearData();
                await _vm.LoadDataAsync();
            });

            App.Messager.Subscribe<FavChangedObject>(this, "FavChanged", async (sender, arg) =>
            {
                var existing = _vm.Items.FirstOrDefault(x => x.Id == arg.Id);
                if (existing != null && !arg.Value)
                {
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        try
                        {
                            _vm.Items.Remove(existing);

                            var save = StackCells.BindingContext;
                            StackCells.BindingContext = null;
                            StackCells.BindingContext = save;
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine(e);
                        }
                    });
                }
            });
        }

        //private void RenderControls()
        //{
        //    testsCollection.ItemsSource = ApplicationState.UserFavorites.GetFavoriteTests();
        //    TopicsCollection.ItemsSource = ApplicationState.UserFavorites.GetFavoriteTopics();
        //    practicesCollection.ItemsSource = ApplicationState.UserFavorites.GetFavoritePractices();
        //    exercisesCollection.ItemsSource = ApplicationState.UserFavorites.GetFavoriteExercises();
        //}

        #region Переключение разделов

        private FavoriteSectionType favoriteSectionType = FavoriteSectionType.Exercises;
        public FavoriteSectionType FavoriteSectionType
        {
            get => favoriteSectionType;
            set
            {
                favoriteSectionType = value;
                OnPropertyChanged(nameof(FavoriteSectionType));
                if (_vm != null)
                {
                    _vm.Section = FavoriteSectionType;
                }
            }
        }

        private void onExercisesChecked(object sender, CheckedChangedEventArgs e)
        {
            if (e.Value)
            {
                FavoriteSectionType = FavoriteSectionType.Exercises;
            }
        }
        private void onPracticeChecked(object sender, CheckedChangedEventArgs e)
        {
            if (e.Value)
            {
                FavoriteSectionType = FavoriteSectionType.Practices;
            }
        }
        private void onTopicsChecked(object sender, CheckedChangedEventArgs e)
        {
            if (e.Value)
            {
                FavoriteSectionType = FavoriteSectionType.Topics;
            }
        }
        private void onTestsChecked(object sender, CheckedChangedEventArgs e)
        {
            if (e.Value)
            {
                FavoriteSectionType = FavoriteSectionType.Tests;
            }
        }
        #endregion

        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj =>
            {
                var mainPage = App.Current.MainPage.Navigation.NavigationStack.FirstOrDefault(o => o is MainPage) as MainPage;
                if (mainPage != null)
                {
                    mainPage.GoBack();
                }
            });
        }


    }
}