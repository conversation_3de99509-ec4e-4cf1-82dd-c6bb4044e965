﻿using AppoMobi.Specials;

using System.Threading.Tasks;
using System.Windows.Input;
using Triggero.Custom;
using Triggero.Domain.Models;

namespace Triggero.MauiMobileApp.Views.Pages.Profile.Report
{

    public partial class ReportPeriodPage : ContentPage
    {
        public ReportPeriodPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            Tasks.StartDelayed(TimeSpan.FromMilliseconds(250), () =>
            {
                Load();
            });
        }

        private async Task Load()
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                _calendar = new ScrollableCalendarV2();
                await _calendar.BuildCalendar(DateTime.Now.AddMonths(-6), DateTime.Now.AddMonths(6));
                CalendarWrapper.Content = _calendar;
                await Task.Delay(100);
                await _calendar.ScrollToMonth(DateTime.Now.Year, DateTime.Now.Month);
            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
        private ICommand selectDate;
        private ScrollableCalendarV2 _calendar;

        public ICommand SelectDate
        {
            get => selectDate ??= new RelayCommand(async obj =>
            {

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    Selected?.Invoke(this, new MoodTrackerReportSettings() { From = _calendar.From, To = _calendar.To });
                    await App.Current.MainPage.Navigation.PopAsync();

                });

            });
        }
        public event EventHandler<MoodTrackerReportSettings> Selected;
    }
}