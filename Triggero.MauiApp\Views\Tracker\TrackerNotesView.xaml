﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="Triggero.MauiMobileApp.Views.MoodTracker.TrackerNotesView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    x:Name="this">
    <ContentView.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="83" />
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <ScrollView Padding="20,0,20,0">
                    <StackLayout x:Name="layout">

                    </StackLayout>
                </ScrollView>
            </Grid>

            <Grid Grid.Row="1">
                <BoxView
                    BackgroundColor="{x:StaticResource ColorTextGray}"
                    HeightRequest="1"
                    HorizontalOptions="Fill"
                    VerticalOptions="Start" />

                <Grid Margin="0,20,0,0">

                    <Label
                        x:Name="notesCountLabel"
                        FontFamily="FontTextLight"
                        FontSize="14"

                        HorizontalOptions="Center"
                        Text="4 заметки"
                        TextColor="{x:StaticResource ColorText}"
                        VerticalOptions="Start" />

                    <ImageButton
                        Margin="0,0,25,0"
                        BackgroundColor="Transparent"
                        Command="{Binding Source={x:Reference this}, Path=GoToNewNote}"
                        CornerRadius="0"
                        HeightRequest="20"
                        HorizontalOptions="End"
                        Source="notesicon.png"
                        VerticalOptions="Start"
                        WidthRequest="17" />


                </Grid>

            </Grid>


        </Grid>
    </ContentView.Content>
</ContentView>