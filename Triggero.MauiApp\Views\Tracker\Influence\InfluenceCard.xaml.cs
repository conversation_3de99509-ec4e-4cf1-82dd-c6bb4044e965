﻿using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.MoodTracker.User;


namespace Triggero.Controls.Cards.Tracker.Influence
{

    public partial class InfluenceCard : ContentView
    {
        private MoodTrackerInfluence Influence { get; set; }
        public InfluenceCard(MoodTrackerInfluence item)
        {
            Influence = item;
            InitializeComponent();
            Load();
        }

        private async void Load()
        {
            titleLabel.Text = Influence.Factor.GetLocalizedTitle(LanguageHelper.LangCode);
            img.Source = await ResorcesHelper.GetImageSource(Influence.Factor.ImgPath);

            layout.Children.Clear();
            foreach (var item in Influence.FactorDetails)
            {
                var card = new InfluenceCardItem(item)
                {
                    HeightRequest = 32,
                    HorizontalOptions = LayoutOptions.Start
                };
                layout.Children.Add(card);
            }
        }
    }
}