﻿using Triggero.Models.MoodTracker.User;


namespace Triggero.Controls.Cards.Tracker
{

    public partial class TrackerNoteCard : ContentView
    {
        private MoodtrackerNote _item;
        public TrackerNoteCard(MoodtrackerNote item)
        {
            _item = item;
            InitializeComponent();
            Load();
        }

        private void Load()
        {
            noteLabel.Text = _item.Text;
            dateLabel.Text = _item.Date.ToString("dd.MM.yyyy");
        }

        public event EventHandler<MoodtrackerNote> Tapped;

        private void onTapped(object sender, EventArgs e)
        {
            Tapped?.Invoke(this, _item);
        }
    }
}