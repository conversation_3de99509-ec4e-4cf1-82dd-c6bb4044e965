﻿
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using Triggero.Controls.Cards.Tracker;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Views.Pages.MoodTracker;
using Triggero.Models.MoodTracker.User;


namespace Triggero.MauiMobileApp.Views.MoodTracker
{

    public partial class TrackerNotesView : ContentView
    {
        private List<MoodtrackerNote> notes = new List<MoodtrackerNote>();
        public TrackerNotesView()
        {
            InitializeComponent();
            LoadNotes();

            GlobalEvents.NoteChanged += GlobalEvents_NoteChanged;
        }



        public async void LoadNotes()
        {
            notes = await ApplicationState.Data.GetMoodtrackerNotes();
            RenderNotes();
        }
        private void RenderNotes()
        {
            layout.Children.Clear();
            foreach (var note in notes.OrderByDescending(o => o.Date))
            {
                var card = new TrackerNoteCard(note)
                {
                    MinimumHeightRequest = 80,
                };
                card.Tapped += Card_Tapped;
                layout.Children.Add(card);
            }



            string noteStr = "";
            if (notes.Count.ToString().EndsWith("1"))
            {
                noteStr = App.This.Interface.MoodTracker.TrackerMainPage.NotesLowercaseSingular;
            }
            else if (notes.Count.ToString().EndsWith("2")
                || notes.Count.ToString().EndsWith("3")
                || notes.Count.ToString().EndsWith("4"))
            {
                noteStr = App.This.Interface.MoodTracker.TrackerMainPage.NotesLowercaseMiddle;
            }
            else
            {
                noteStr = App.This.Interface.MoodTracker.TrackerMainPage.NotesLowercasePlural;
            }
            notesCountLabel.Text = $"{notes.Count} {noteStr}";
        }



        private void GlobalEvents_NoteChanged(object sender, MoodtrackerNote e)
        {
            if (notes.Any(o => o.GlobalId == e.GlobalId))
            {
                notes.Remove(notes.FirstOrDefault(o => o.GlobalId == e.GlobalId));
                notes.Add(e);
            }
            else
            {
                notes.Add(e);
            }
            RenderNotes();
        }


        private async void Card_Tapped(object sender, MoodtrackerNote e)
        {
            App.OpenPage(new TrackerNote(e));
        }

        private ICommand goToNewNote;
        public ICommand GoToNewNote
        {
            get => goToNewNote ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new TrackerNote());
            });
        }
    }
}