﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Practices;
using Triggero.Models.Tests;


namespace Triggero.MauiMobileApp.ViewModels;

public class ListTestsViewModel : ElementsListViewModel
{
    private readonly TestCategory _category;

    public ListTestsViewModel(TestCategory category)
    {
        _category = category;

        Title = _category.Localizations.GetLocalizedTitle(LanguageHelper.LangCode, _category.Title);
    }

    public override Color ThemeColor
    {
        get
        {
            return Color.FromHex("#FDCE72");
        }
    }


    public override Color ThemeColorB
    {
        get
        {
            return Color.FromHex("#FDCE72");
        }
    }

    protected override async Task<IEnumerable<IElementDetails>> LoadItemsAsync()
    {
        return await ApplicationState.Data.GetTests(_category.Id);
    }




}