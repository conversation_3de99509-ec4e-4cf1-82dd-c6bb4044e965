using System.IO;
using Microsoft.Extensions.Configuration;

namespace Triggero.Api.PaymentTests;

public static class TestConfiguration
{
    private static IConfiguration _configuration;

    static TestConfiguration()
    {
        // Find the API project path relative to the test project
        var currentDir = Directory.GetCurrentDirectory();
        var apiProjectPath = FindApiProjectPath(currentDir);

        var builder = new ConfigurationBuilder();

        if (!string.IsNullOrEmpty(apiProjectPath))
        {
            builder.SetBasePath(apiProjectPath)
                   .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                   .AddJsonFile("secrets.json", optional: true, reloadOnChange: true)
                   .AddJsonFile("appsettings.Development.json", optional: true, reloadOnChange: true);
        }
        else
        {
            // Fallback to current directory
            builder.SetBasePath(currentDir);
        }

        builder.AddEnvironmentVariables();
        _configuration = builder.Build();
    }

    private static string FindApiProjectPath(string startPath)
    {
        var currentDir = new DirectoryInfo(startPath);

        // Look for the API project in common locations
        while (currentDir != null)
        {
            var apiPath = Path.Combine(currentDir.FullName, "Triggero.Web", "Triggero.Api");
            if (Directory.Exists(apiPath))
            {
                return apiPath;
            }

            currentDir = currentDir.Parent;
        }

        return null;
    }

    public static IConfiguration Configuration => _configuration;

    // Helper properties for common test settings
    public static string ConnectionString => _configuration.GetConnectionString("DefaultConnection");
    public static string JwtKey => _configuration["Jwt:Key"];
    public static string JwtIssuer => _configuration["Jwt:Issuer"];

    // Test-specific settings with fallback defaults
    public static int TestUserId => int.Parse(_configuration["TestSettings:UserId"] ?? "26");

    public static string TestAuthToken => _configuration["TestSettings:AuthToken"] ?? "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkNTM1ZjYwMy0wNGQ5LTQyZDktYmQ1Ni0xZDhmNzBmMGU2OGUiLCJqdGkiOiJjZjlmYmI3Zi1hNzQ5LTRmMTItOWQ5Yi0zNTZmM2EzM2JmZmYiLCJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6ImQ1MzVmNjAzLTA0ZDktNDJkOS1iZDU2LTFkOGY3MGYwZTY4ZSIsImh0dHA6Ly9zY2hlbWFzLnhtbHNvYXAub3JnL3dzLzIwMDUvMDUvaWRlbnRpdHkvY2xhaW1zL25hbWUiOiLQkNC70LjQvdCwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9yb2xlIjoiVXNlciIsImV4cCI6MTc1OTA1NTY0OCwiaXNzIjoiQXBpIiwiYXVkIjoiQXBpIn0.0-XYpWkoMtDz4XmxXelRNvU7IVBr9YHG1RW-3j-LlEE";

    public static string ApiBaseUrl => _configuration["TestSettings:ApiBaseUrl"] ?? "https://localhost:7013";

    // UKassa settings (for debugging purposes)
    public static string UKassaShopId => _configuration["Secrets:UKassa:ShopId"];
    public static string UKassaKey => _configuration["Secrets:UKassa:Key"];
}
