﻿using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiPort.Models;
using Triggero.Models;
using Triggero.Models.Practices;

namespace Triggero.Controls.Cards.TasksForToday
{

    public partial class ExerciseRecommendation : BaseRecommendationView
    {
        private Exercise _exercise;
        public ExerciseRecommendation(RecommendationModel recommendation) : base(recommendation)
        {
            _exercise = recommendation.Exercise;
            InitializeComponent();
            Load();
        }
        private void Load()
        {
            titleLabel.Text = _exercise.GetLocalizedTitle(LanguageHelper.LangCode);
            minutesSpan.Text = $"{_exercise.PassingTimeInMinutes}";
            img.Source = Constants.BuildContentUrl(_exercise.IconImgPath);
        }
    }
}