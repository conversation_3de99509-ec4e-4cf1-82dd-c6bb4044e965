﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Start.StartTutorialPage"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        <Grid>

            <Image
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightbluegradientbg.png"/>


            <StackLayout
                Spacing="0"
                Margin="20,0,20,0"
                VerticalOptions="Center">

                <!--<Image
                    WidthRequest="253"
                    HeightRequest="232"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    Source="starttutorial.png"/>-->

                <Grid
                    x:Name="animatedLayout"
                    WidthRequest="253"
                    HeightRequest="232"
                    VerticalOptions="Start"
                    HorizontalOptions="Center">

                    <Image
                        x:Name="notAnimatedImg"
                        WidthRequest="253"
                        HeightRequest="232"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        Source="animationrunninggirl.png"/>

                </Grid>
                
               

                <Label 
                    Margin="0,40,0,0"
                    TextColor="{x:StaticResource ColorText}"
                    FontSize="22"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Tutorial.StartTutorialPage.Header}"/>


                <StackLayout
                    Spacing="1"
                    HorizontalOptions="Center"
                    Margin="0,20,0,0">

                    <Label 
                        TextColor="{x:StaticResource ColorText}"
                        Opacity="0.5"
                        FontSize="17"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="{x:OnPlatform Android=250,iOS=250}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Tutorial.StartTutorialPage.Text}"/>


                </StackLayout>

                <Button 
                    Command="{Binding Source={x:Reference this},Path=StartTest}"
                    Margin="0,112,0,0"
                    Style="{x:StaticResource yellow_btn}"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Tutorial.StartTutorialPage.Start}"/>


            </StackLayout>
            
            
            
            

        </Grid>
    </ContentPage.Content>
</ContentPage>