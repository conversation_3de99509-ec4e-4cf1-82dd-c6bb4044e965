﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;


namespace Triggero.MauiMobileApp.ViewModels;

public class ListPracticesViewModel : ElementsListViewModel
{
    private readonly PracticeCategory _category;

    public ListPracticesViewModel(PracticeCategory category)
    {
        _category = category;

        Title = _category.Localizations.GetLocalizedTitle(LanguageHelper.LangCode, _category.Title);
    }

    public override Color ThemeColorB
    {
        get
        {
            return Color.FromHex("#AE9FAD");
        }
    }

    public override Color ThemeColor
    {
        get
        {
            return Color.FromHex("#F8F8FF");
        }
    }

    protected override async Task<IEnumerable<IElementDetails>> LoadItemsAsync()
    {
        return await ApplicationState.Data.GetPractices(_category.Id);
    }



}