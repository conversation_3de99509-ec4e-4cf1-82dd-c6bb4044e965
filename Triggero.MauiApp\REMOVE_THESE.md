# Syncfusion Controls Inventory for DrawnUI Migration

This document catalogs all Syncfusion controls used in the Triggero MAUI project and their exact locations. Use this list to systematically replace them with DrawnUI controls.

## 📦 Installed Syncfusion Packages

From `Triggero.MauiPort.csproj`:
- `Syncfusion.Maui.Buttons` Version="30.1.37"
- `Syncfusion.Maui.Calendar` Version="30.1.37"
- `Syncfusion.Maui.Carousel` Version="30.1.37"
- `Syncfusion.Maui.Charts` Version="30.1.37"
- `Syncfusion.Maui.Gauges` Version="30.1.37"
- `Syncfusion.Maui.Picker` Version="30.1.37"
- `Syncfusion.Maui.ProgressBar` Version="30.1.37"
- `Syncfusion.Maui.Sliders` Version="30.1.37"
- `Syncfusion.Maui.Rotator` Version="30.1.37"

## 🎯 Syncfusion Controls by Category

### 1. Progress Controls

#### SfLinearProgressBar
**Files using this control:**

**📁 `Controls/Tests/TestResultScale.xaml`** (Lines 16-26)
```xml
<progressBar:SfLinearProgressBar
    x:Name="progressBar"
    TrackHeight="20"
    TrackCornerRadius="3"
    ProgressCornerRadius="3"
    SecondaryProgressCornerRadius="3"
    VerticalOptions="Start"
    HeightRequest="20"
    ProgressFill="#CEE3F4"
    TrackFill="Transparent"
    SecondaryProgressFill="Transparent"/>
```
**Properties used:** TrackHeight, TrackCornerRadius, ProgressCornerRadius, SecondaryProgressCornerRadius, ProgressFill, TrackFill, SecondaryProgressFill

**📁 `Views/Tracker/TrackerInfluenceProgressCard.xaml`** (Lines 27-35)
```xml
<progressBar:SfLinearProgressBar 
    x:Name="progressBar"
    TrackHeight="20"
    TrackCornerRadius="3"
    ProgressCornerRadius="3"
    SecondaryProgressCornerRadius="3"
    VerticalOptions="Start"
    HeightRequest="20"
    ProgressFill="#CEE3F4"
    TrackFill="Transparent"
    SecondaryProgressFill="Transparent"/>
```
**Properties used:** Same as above

**DrawnUI Replacement:** Use `SkiaShape` with custom drawing or create a custom progress control

### 2. Button Controls

#### SfSwitch
**Files using this control:**

**📁 `Controls/PlanOptionCard.xaml`** (Lines 34-42)
```xml
<buttons:SfSwitch 
    x:Name="MainSwitch"
    x:DataType="controls1:PlanOptionCard"
    IsOn="{Binding Source={x:Reference this},Path=IsSelected,Mode=TwoWay}"
    Style="{x:StaticResource sf_switch_yellow}"
    HorizontalOptions="End"
    VerticalOptions="Start"
    WidthRequest="51"
    HeightRequest="31"/>
```
**Properties used:** IsOn, Style, WidthRequest, HeightRequest
**Style reference:** `sf_switch_yellow` from `Resources/Styles/SfSwitchStyles.xaml`

**📁 `Resources/Styles/SfSwitchStyles.xaml`** (Lines 7-60)
```xml
<Style x:Key="sf_switch_yellow" TargetType="buttons:SfSwitch">
    <!-- Complex visual states with SwitchSettings -->
    <!-- On state: ThumbStroke="White", ThumbBackground="White", TrackBackground="#FDCE72" -->
    <!-- Off state: ThumbStroke="#E5E5EA", ThumbBackground="White", TrackBackground="#E5E5EA" -->
</Style>
```
**Properties used:** SwitchSettings, ThumbStroke, ThumbBackground, ThumbCornerRadius, ThumbHeightRequest, ThumbWidthRequest, TrackStroke, TrackBackground, TrackCornerRadius, TrackHeightRequest, TrackWidthRequest

**DrawnUI Replacement:** Use `SkiaButton` with custom toggle logic and visual states

### 3. Calendar Controls

#### SfCalendar
**Files using this control:**

**📁 `Views/MoodTracker/TrackerCalendarView.xaml`** (Lines 12-35)
```xml
<syncfusion:SfCalendar
    x:Name="calendar"
    Margin="{x:OnPlatform Android='0', iOS='0,15,0,0'}"
    BackgroundColor="White"
    ViewChanged="monthChanged"
    SelectionChanged="selectionChanged"
    SelectionMode="Single"
    ShowTrailingAndLeadingDates="False"
    AllowViewNavigation="False"
    View="Month">
    <syncfusion:SfCalendar.CalendarMonthView>
        <syncfusion:CalendarMonthView
            TodayBackground="#FDCE72"
            TodayTextStyle="{x:StaticResource CalendarTodayTextStyle}"
            TextStyle="{x:StaticResource CalendarTextStyle}"
            TrailingLeadingDatesTextStyle="{x:StaticResource CalendarTrailingLeadingTextStyle}"
            WeekendTextStyle="{x:StaticResource CalendarWeekendTextStyle}"
            DisabledDatesTextStyle="{x:StaticResource CalendarDisabledTextStyle}"
            SpecialDatesTextStyle="{x:StaticResource CalendarSpecialTextStyle}"
            SelectionTextStyle="{x:StaticResource CalendarSelectionTextStyle}"
            RangeTextStyle="{x:StaticResource CalendarRangeTextStyle}"
            HeaderTextStyle="{x:StaticResource CalendarHeaderTextStyle}"
            WeekNumberTextStyle="{x:StaticResource CalendarWeekNumberTextStyle}"/>
    </syncfusion:SfCalendar.CalendarMonthView>
</syncfusion:SfCalendar>
```
**Properties used:** ViewChanged event, SelectionChanged event, SelectionMode, ShowTrailingAndLeadingDates, AllowViewNavigation, View, CalendarMonthView with extensive text styling
**Events:** ViewChanged, SelectionChanged

**DrawnUI Replacement:** Create custom calendar using `SkiaLayout` with `SkiaButton` for date cells

### 4. Picker Controls

#### SfPicker
**Files using this control:**

**📁 `Views/Profile/Notifications/SetNotificationDatePage.xaml`** (Lines 89-103)
```xml
<syncfusion:SfPicker
    x:Name="hoursSfPicker"
    Margin="0,-20,0,0"
    WidthRequest="70"
    HeightRequest="200"
    Mode="Default"
    EnableLooping="True"
    HorizontalOptions="Center"
    VerticalOptions="Fill">
    <syncfusion:SfPicker.Columns>
        <syncfusion:PickerColumn ItemsSource="{Binding Source={x:Reference this},Path=Hours}" />
    </syncfusion:SfPicker.Columns>
</syncfusion:SfPicker>
```
**Properties used:** Mode, EnableLooping, Columns with PickerColumn and ItemsSource
**TODO Comments:** Properties like ColumnHeaderBackgroundColor, HeaderBackgroundColor, BorderColor, ShowHeader, UnSelectedItemFontSize, SelectedItemTextColor, UnSelectedItemTextColor, SelectedItemFontSize need to be set via TextStyle and SelectedTextStyle

**DrawnUI Replacement:** Use `SkiaScroll` with `SkiaLabel` items or create custom picker wheel

### 5. Rotator/Carousel Controls

#### SfRotator
**Files using this control:**

**📁 `Views/Start/HelloSliderPage.xaml`** (Lines 48-59)
```xml
<syncfusion:SfRotator
    DotPlacement="None"
    WidthRequest="330"
    SelectedIndexChanged="onIndexChanged"
    ItemsSource="{Binding Source={x:Reference this},Path=Items}"
    HorizontalOptions="Center">
    <syncfusion:SfRotator.ItemTemplate>
        <DataTemplate>
            <sliders:HelloSliderItemCard HelloSliderItem="{Binding}"/>
        </DataTemplate>
    </syncfusion:SfRotator.ItemTemplate>
</syncfusion:SfRotator>
```
**Properties used:** DotPlacement, SelectedIndexChanged event, ItemsSource, ItemTemplate
**Events:** SelectedIndexChanged

**DrawnUI Replacement:** Use `SkiaCarousel` (DrawnUI has native carousel support)

### 6. Gauge Controls

#### SfGauge (from Xamarin - needs investigation)
**Files referencing this control:**

**📁 `Controls/MoodTracker/TrackerMoodGauge.xaml`** (Lines 1-10)
```xml
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:gauge="clr-namespace:Syncfusion.Maui.Gauges;assembly=Syncfusion.Maui.Gauges"
             x:Name="this"
             x:Class="Triggero.Controls.MoodTracker.TrackerMoodGauge">
```
**Note:** This file declares the gauge namespace but actual usage needs to be investigated

**DrawnUI Replacement:** Create custom gauge using `SkiaShape` with arc drawing

### 7. Chart Controls (From Xamarin Version)

#### SfChart (Xamarin Reference - May Need MAUI Port)
**Files from Xamarin version that may need porting:**

**📁 `TriggeroV2/Controls/MoodTracker/TrackerMoodChart.xaml`** (Lines 229-288)
```xml
<xforms:SfChart
    x:Name="chart"
    Margin="10,0,0,0"
    ChartPadding="0,0,0,0"
    BackgroundColor="Transparent"
    HorizontalOptions="FillAndExpand"
    VerticalOptions="FillAndExpand">
    <xforms:SfChart.PrimaryAxis>
        <xforms:DateTimeAxis
            x:Name="dateAxis"
            Interval="1"
            ShowMajorGridLines="False"
            IntervalType="Days"
            IsVisible="True">
            <xforms:DateTimeAxis.LabelStyle>
                <xforms:ChartAxisLabelStyle
                    FontSize="10"
                    LabelFormat="dd"
                    TextColor="#363B4099"/>
            </xforms:DateTimeAxis.LabelStyle>
        </xforms:DateTimeAxis>
    </xforms:SfChart.PrimaryAxis>
    <xforms:SfChart.SecondaryAxis>
        <xforms:NumericalAxis
            Minimum="-0.5"
            Maximum="5.30"
            ShowMajorGridLines="False"
            IsVisible="False"
            Interval="1"/>
    </xforms:SfChart.SecondaryAxis>
    <xforms:SplineSeries
        Color="#4D4D4D50"
        SplineType="Monotonic"
        ItemsSource="{Binding Source={x:Reference this},Path=Data,Mode=TwoWay}"
        XBindingPath="Date"
        YBindingPath="Mood"/>
    <xforms:LineSeries
        Color="Transparent"
        ItemsSource="{Binding Source={x:Reference this},Path=Data5,Mode=TwoWay}"
        XBindingPath="Date"
        YBindingPath="Mood">
        <xforms:LineSeries.DataMarker>
            <xforms:ChartDataMarker
                MarkerWidth="12"
                MarkerHeight="12"
                MarkerColor="#FFCD62"
                ShowMarker="True"
                ShowLabel="False"
                MarkerType="Ellipse"/>
        </xforms:LineSeries.DataMarker>
    </xforms:LineSeries>
</xforms:SfChart>
```
**Properties used:** PrimaryAxis (DateTimeAxis), SecondaryAxis (NumericalAxis), SplineSeries, LineSeries, DataMarker
**Chart Types:** Spline chart with data markers for mood tracking visualization

**📁 `TriggeroV2/Controls/MoodTracker/TrackerMoodPentagram.xaml`** (Lines 9-10)
```xml
xmlns:xforms="clr-namespace:Syncfusion.SfChart.XForms;assembly=Syncfusion.SfChart.XForms"
```
**Note:** References chart namespace, actual usage needs investigation

**DrawnUI Replacement:** Create custom chart using `SkiaPath` for spline curves and `SkiaShape` for data points

## 🚀 Migration Priority

### High Priority (Blocking Compilation)
1. **SfSwitch** - Used in critical UI components (PlanOptionCard)
2. **SfLinearProgressBar** - Used in test results and tracker progress
3. **SfCalendar** - Core functionality for mood tracking

### Medium Priority (Feature Complete)
1. **SfRotator** - Used in onboarding slider
2. **SfPicker** - Used in notification time selection

### Low Priority (Investigation Needed)
1. **SfGauge** - Namespace declared but usage unclear
2. **SfChart** - Complex mood tracking charts from Xamarin version need MAUI porting
3. **SfCarousel** - Package installed but usage not found
4. **SfSliders** - Package installed but no current usage found

## 📋 DrawnUI Replacement Strategy

### 1. Progress Controls → SkiaShape
- Replace `SfLinearProgressBar` with custom `SkiaShape` rectangles
- Use `SkiaLayout` to layer track and progress shapes
- Implement corner radius using `SkiaShape.CornerRadius`

### 2. Switch Controls → SkiaButton
- Replace `SfSwitch` with `SkiaButton` with toggle behavior
- Use visual states for on/off appearance
- Implement thumb animation using DrawnUI animations

### 3. Calendar → Custom SkiaLayout Grid
- Create month view using `SkiaLayout` with grid layout
- Use `SkiaButton` for individual date cells
- Implement selection and navigation logic

### 4. Picker → SkiaScroll + SkiaLabel
- Replace `SfPicker` with vertical `SkiaScroll`
- Use `SkiaLabel` for picker items
- Implement looping and selection logic

### 5. Rotator → SkiaCarousel
- Direct replacement with DrawnUI's `SkiaCarousel`
- Migrate ItemTemplate to DrawnUI template system

## 🔧 Implementation Notes

1. **Styling Migration**: All Syncfusion styles need to be recreated using DrawnUI styling system
2. **Event Handling**: Syncfusion events need to be mapped to DrawnUI equivalents
3. **Data Binding**: Ensure all data binding scenarios work with DrawnUI controls
4. **Performance**: DrawnUI should provide better performance than Syncfusion
5. **Platform Consistency**: DrawnUI provides consistent rendering across platforms

## 📊 Summary Statistics

### Active Syncfusion Controls in MAUI Project
- **SfSwitch**: 1 usage (PlanOptionCard.xaml) + 1 style file (SfSwitchStyles.xaml)
- **SfLinearProgressBar**: 2 usages (TestResultScale.xaml, TrackerInfluenceProgressCard.xaml)
- **SfCalendar**: 1 usage (TrackerCalendarView.xaml)
- **SfPicker**: 1 usage (SetNotificationDatePage.xaml)
- **SfRotator**: 1 usage (HelloSliderPage.xaml)
- **SfGauge**: 1 namespace declaration (TrackerMoodGauge.xaml)

### Total Files to Modify: 7 files
1. `Controls/PlanOptionCard.xaml`
2. `Resources/Styles/SfSwitchStyles.xaml`
3. `Controls/Tests/TestResultScale.xaml`
4. `Views/Tracker/TrackerInfluenceProgressCard.xaml`
5. `Views/MoodTracker/TrackerCalendarView.xaml`
6. `Views/Profile/Notifications/SetNotificationDatePage.xaml`
7. `Views/Start/HelloSliderPage.xaml`
8. `Controls/MoodTracker/TrackerMoodGauge.xaml`

### Xamarin Legacy (For Reference)
- **SfChart**: Complex mood tracking charts that may need porting
- **SfGauge**: Gauge controls from Xamarin version

## 📝 Next Steps

1. Start with SfSwitch replacement (highest impact)
2. Create reusable DrawnUI control templates
3. Test each replacement thoroughly
4. Update documentation and migration guides
5. Remove Syncfusion package dependencies once complete

## 🎯 Replacement Checklist

- [ ] **SfSwitch** → SkiaButton with toggle logic
- [ ] **SfLinearProgressBar** → SkiaShape progress bars
- [ ] **SfCalendar** → Custom SkiaLayout calendar
- [ ] **SfPicker** → SkiaScroll picker wheel
- [ ] **SfRotator** → SkiaCarousel
- [ ] **SfGauge** → Custom SkiaShape gauge (if needed)
- [ ] **SfChart** → Custom SkiaPath charts (if porting from Xamarin)

---
*Last updated: Current migration status*
*Total Syncfusion controls to replace: 6 active controls across 8 files*
