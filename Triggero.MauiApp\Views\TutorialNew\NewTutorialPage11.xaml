﻿<?xml version="1.0" encoding="utf-8"?>

<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:parts="clr-namespace:Triggero.Controls.Parts"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage11"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <Grid VerticalOptions="Fill">

        <Image
            VerticalOptions="Fill"
            HorizontalOptions="Fill"
            Aspect="Fill"
            Source="tutorialblur3.png" />

        <ScrollView VerticalOptions="Fill">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="340" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="140" />
                </Grid.RowDefinitions>


                <Grid Grid.Row="0" Padding="10,0">

                    <Grid
                        VerticalOptions="End"
                        HeightRequest="270">

                        <Image
                            Aspect="Fill"
                            Margin="-2,0,0,0"
                            Source="tutorialchatbotcontainer.png" />

                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="1*" />
                                <RowDefinition Height="1*" />
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0"
                                  Margin="20,0,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="50" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <Grid Grid.Column="0">
                                    <Image
                                        WidthRequest="47"
                                        HeightRequest="47"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        Source="chatbotavatar.png" />
                                </Grid>

                                <Grid Grid.Column="1">
                                    <StackLayout
                                        Spacing="0"
                                        HorizontalOptions="Start"
                                        VerticalOptions="Center"
                                        Margin="15,0,0,0">

                                        <Label
                                            TextColor="{x:StaticResource ColorText}"
                                            FontSize="17"
                                            FontAttributes="Bold"
                                            Margin="0,0,0,0"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start"
                                            Text="Triggero" />
                                        <Label
                                            TextColor="{x:StaticResource ColorText}"
                                            Opacity="0.5"
                                            FontSize="12"
                                            Margin="0,0,0,0"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start"
                                            Text="Виртуальный психолог" />

                                        <StackLayout
                                            Spacing="4"
                                            Margin="0,5,0,0"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start"
                                            Orientation="Horizontal">

                                            <BoxView
                                                WidthRequest="8"
                                                HeightRequest="8"
                                                CornerRadius="4"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Start"
                                                Background="#34C759" />

                                            <Label
                                                TextColor="{x:StaticResource ColorText}"
                                                Opacity="0.5"
                                                FontSize="12"
                                                Margin="0,0,0,0"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Start"
                                                Text="В сети" />

                                        </StackLayout>

                                    </StackLayout>
                                </Grid>

                            </Grid>


                            <Grid Grid.Row="1">

                                <StackLayout Margin="0,-10,0,0">
                                    <Label
                                        TextColor="{x:StaticResource ColorText}"
                                        Opacity="0.5"
                                        FontSize="14"
                                        Margin="0,0,0,0"
                                        FontAttributes="Bold"
                                        VerticalOptions="Start"
                                        HorizontalOptions="Center"
                                        Text="4 октября" />

                                    <Frame
                                        CornerRadius="12"
                                        HeightRequest="35"
                                        VerticalOptions="Start"
                                        HorizontalOptions="Start"
                                        Margin="20,7,0,0"
                                        BackgroundColor="#EEF5FB"
                                        HasShadow="False"
                                        Padding="0">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="50" />
                                            </Grid.ColumnDefinitions>

                                            <Grid Grid.Column="0">
                                                <Label
                                                    TextColor="{x:StaticResource ColorText}"
                                                    FontSize="16"
                                                    Margin="20,0,0,0"
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="Start"
                                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage11.ChatMessage1Text}" />
                                            </Grid>

                                            <Grid Grid.Column="1">
                                                <Label
                                                    TextColor="{x:StaticResource ColorText}"
                                                    Opacity="0.5"
                                                    FontSize="12"
                                                    Margin="0,0,0,0"
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="Center"
                                                    Text="8:38" />
                                            </Grid>

                                        </Grid>
                                    </Frame>

                                    <Frame
                                        CornerRadius="12"
                                        HeightRequest="35"
                                        VerticalOptions="Start"
                                        HorizontalOptions="Start"
                                        Margin="20,0,0,0"
                                        BackgroundColor="#EEF5FB"
                                        HasShadow="False"
                                        Padding="0">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="50" />
                                            </Grid.ColumnDefinitions>

                                            <Grid Grid.Column="0">
                                                <Label
                                                    TextColor="{x:StaticResource ColorText}"
                                                    FontSize="16"
                                                    Margin="20,0,0,0"
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="Start"
                                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage11.ChatMessage2Text}" />
                                            </Grid>

                                            <Grid Grid.Column="1">
                                                <Label
                                                    TextColor="{x:StaticResource ColorText}"
                                                    Opacity="0.5"
                                                    FontSize="12"
                                                    Margin="0,0,0,0"
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="Center"
                                                    Text="8:38" />
                                            </Grid>

                                        </Grid>
                                    </Frame>

                                </StackLayout>


                            </Grid>

                        </Grid>

                    </Grid>


                </Grid>

                <Grid
                    Grid.Row="1" Padding="10,0">

                    <StackLayout
                        Margin="20,0,20,0">

                        <parts:TransparentFooter
                            IsChatBotPageSelected="True"
                            Margin="0,10,0,0"
                            HorizontalOptions="Fill"
                            InputTransparent="False" />


                        <Label
                            Margin="0,35,0,0"
                            TextColor="#000000"
                            FontAttributes="Bold"
                            FontSize="19"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="317"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage11.Header}" />
                        <Label
                            Margin="0,0,0,0"
                            TextColor="{x:StaticResource ColorText}"
                            FontSize="16"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="320"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage11.Description}" />

                    </StackLayout>

                </Grid>


                <Grid Grid.Row="2">
                    <Button
                        Command="{Binding Source={x:Reference this},Path=GoNext}"
                        VerticalOptions="Start"
                        HorizontalOptions="Fill"
                        Margin="63,0,63,0"
                        Style="{x:StaticResource yellow_btn}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage11.GoNext}" />

                </Grid>

            </Grid>
        </ScrollView>
    </Grid>
</ContentPage>