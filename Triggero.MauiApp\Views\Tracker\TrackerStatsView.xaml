﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="Triggero.MauiMobileApp.Views.MoodTracker.TrackerStatsView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:moodtracker="clr-namespace:Triggero.Controls.MoodTracker"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw">
    <ContentView.Content>

        <ScrollView
            HorizontalOptions="Fill"
            Margin="0"
            Padding="20,0,20,30">
            <StackLayout Spacing="0" HorizontalOptions="Fill">

                <Grid
                    HorizontalOptions="Fill"
                    HeightRequest="100"
                    VerticalOptions="Start">

                    <!--  даты  -->

                    <!--  buttons select period  -->
                    <StackLayout
                        x:Name="MainLayout"
                        Margin="0,0,0,0"
                        HorizontalOptions="Start"
                        Orientation="Horizontal"
                        Spacing="5"
                        VerticalOptions="Center">


                        <!--  1 week  -->
                        <RadioButton
                            x:Name="week1_rb"
                            CheckedChanged="week1_checkedChanged"
                            Content="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerMainPage.Week1}"
                            HeightRequest="32"
                            IsChecked="True"
                            Style="{x:StaticResource mood_tracker_period_rb}"
                            VerticalOptions="Center"
                            WidthRequest="85" />

                        <!--  2 weeks  -->
                        <RadioButton
                            x:Name="week2_rb"
                            CheckedChanged="week2_checkedChanged"
                            Content="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerMainPage.Week2}"
                            HeightRequest="32"
                            Style="{x:StaticResource mood_tracker_period_rb}"
                            VerticalOptions="Center"
                            WidthRequest="85" />

                        <!--  1 month  -->
                        <RadioButton
                            x:Name="month1_rb"
                            CheckedChanged="month1_checkedChanged"
                            Content="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerMainPage.Month1}"
                            HeightRequest="32"
                            Style="{x:StaticResource mood_tracker_period_rb}"
                            VerticalOptions="Center"
                            WidthRequest="85" />

                        <Frame
                            Padding="0"
                            BackgroundColor="Transparent"
                            BorderColor="{x:StaticResource ColorPrimaryLight}"
                            CornerRadius="16"
                            HasShadow="False"
                            HeightRequest="32"
                            VerticalOptions="Center"
                            WidthRequest="45">
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Tapped="periodFrameBtnTapped" />
                            </Frame.GestureRecognizers>
                            <Grid>
                                <Image
                                    HeightRequest="16"
                                    HorizontalOptions="Center"
                                    Source="calendaricon.png"
                                    VerticalOptions="Center"
                                    WidthRequest="16" />
                            </Grid>
                        </Frame>

                    </StackLayout>

                </Grid>

                <moodtracker:TrackerMoodChart
                    x:Name="moodChart"
                    HeightRequest="493"
                    VerticalOptions="Start" />

                <moodtracker:TrackerMoodGauge
                    x:Name="gaugeChart"
                    Margin="0,50,0,0"
                    HeightRequest="380"
                    VerticalOptions="Start" />

                <moodtracker:TrackerMoodPentagram
                    x:Name="pentagramChart"
                    Margin="0,24,0,0"
                    HeightRequest="471"
                    VerticalOptions="Start" />

                <moodtracker:TrackerAffectsView
                    x:Name="affectsView"
                    Margin="0,32,0,0" />

            </StackLayout>
        </ScrollView>

    </ContentView.Content>
</ContentView>