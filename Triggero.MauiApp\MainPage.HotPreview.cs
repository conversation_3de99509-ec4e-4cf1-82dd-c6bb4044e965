﻿#if PREVIEWS

using HotPreview;
using Triggero.MauiMobileApp.Views.Pages.Subscriptions;

namespace Triggero.MauiPort.Views.Pages
{
    public partial class MainPage
    {
        [PreviewCommand]
        public static void Payments()
        {
            var selectionsModel = new CustomPlanSelectionsModel()
            {
                IsLibrarySelected = true,
                IsChatBotSelected = true,
                //IsMoodTrackerSelected = true,
                IsTestsSelected = true
            };

            App.OpenPage(new BuyCustomPlanPage(selectionsModel));
        }

        /*
        public static SkiaLayout CreatePreviewWrapper(SkiaControl control, string comments)
        {
            return new SkiaStack()
            {
                Spacing = 0,
                BackgroundColor = Colors.DarkGrey,
                VerticalOptions = LayoutOptions.Fill,
                Children =
                {
                    // navbar
                    new SkiaLayer()
                    {
                        HeightRequest = 44,
                        UseCache = SkiaCacheType.Operations,
                        BackgroundColor = Colors.Black,
                        Children =
                        {
                            new SkiaRichLabel($"← {ResStrings.BtnGoBack}")
                                {
                                    FontSize = 16,
                                    VerticalOptions = LayoutOptions.Center,
                                    Padding = new(16, 0),
                                    UseCache = SkiaCacheType.Operations
                                }
                                .OnTapped(me => { _ = ViewsContainer?.PopPage(); })
                        }
                    },

                    control,

                    //overlay
                    new SkiaRichLabel(comments)
                    {
                        Opacity = 0.5,
                        FontSize = 16,
                        VerticalOptions = LayoutOptions.Center,
                        HorizontalOptions = LayoutOptions.Fill,
                        HorizontalTextAlignment = DrawTextAlignment.Center,
                        Margin = new (32),
                        Padding = new(12),
                        BackgroundColor = Color.Parse("#22000000"),
                        TextColor = Colors.White,
                        UseCache = SkiaCacheType.Operations
                    },

                }
            };
        }

        private static void Preview(SkiaControl control, string comments = null)
        {
            if (ViewsContainer != null)
            {
                ViewsContainer.PushView(CreatePreviewWrapper(control, comments), true, false);
            }
        }
        */
    }
}

#endif