﻿
using Plugin.Maui.Audio;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Triggero.Controls.Cards.NextItemCard;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Extensions.Helpers.Modules;
using Triggero.MauiMobileApp.Services;
using Triggero.Models.Practices;
using ImageButton = Microsoft.Maui.Controls.ImageButton;

namespace Triggero.MauiMobileApp.Views.Pages.Library
{

    public partial class ExercisePage : ContentPage
    {
        public ExercisePage(Exercise exercise)
        {
                        
            Exercise = exercise;

            //SkiaImageManager.Instance.Preload(image, default(CancellationTokenSource)).ConfigureAwait(false);

            InitializeComponent();

#if IOS
            MainGrid.Margin = new(0, -Globals.TopInsets, 0, 0);
#endif

            if (Constants.ShowReferenceLinks && !string.IsNullOrEmpty(Exercise.ReferenceLink))
            {
                LabelExternalLink.IsVisible = true;
            }

            headerImg.Source = App.GetFullImageUrl(Exercise.HeaderImgPath, ThumbnailSize.Large, ThumbnailType.Jpg);

            Load();
        }

        public ICommand CommandOpenReferenceLink
        {
            get
            {
                return new Command((context) =>
                {
                    if (!string.IsNullOrEmpty(Exercise.ReferenceLink))
                    {
                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            PlatformUi.Instance.OpenUrl(Exercise.ReferenceLink);
                        });

                    }

                });
            }
        }

        private bool isFav;

        private void Load()
        {

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                titleLabel.Text = Exercise.GetLocalizedTitle(LanguageHelper.LangCode);
                subLabel.Text = Exercise.GetLocalizedSubtext(LanguageHelper.LangCode);

               isFav = ApplicationState.UserFavorites.HasFavoriteExercise(Exercise.Id);
               favoriteRb.IsToggled = isFav;

                SetRandomNextRecommendation();
                StatsHelper.AddExerciseWatch(Exercise.Id);

                var foundResult = ApplicationState.Data.ExercisePassingResults.FirstOrDefault(o => o.ExerciseId == Exercise.Id);
                if (foundResult != null)
                {
                    switch (foundResult.EmojiType)
                    {
                        case FeelingEmojiType.Annoyance:
                        emojiAnnoyance.SetChecked();
                        break;
                        case FeelingEmojiType.Anger:
                        emojiAnger.SetChecked();
                        break;
                        case FeelingEmojiType.Anxienty:
                        emojiAnxienty.SetChecked();
                        break;
                        case FeelingEmojiType.Panic:
                        emojiPanic.SetChecked();
                        break;
                        case FeelingEmojiType.Happiness:
                        emojiHappiness.SetChecked();
                        break;
                        case FeelingEmojiType.Confidence:
                        emojiConfidence.SetChecked();
                        break;
                        case FeelingEmojiType.Fear:
                        emojiFear.SetChecked();
                        break;
                        case FeelingEmojiType.Sadness:
                        emojiSadness.SetChecked();
                        break;
                        case FeelingEmojiType.Tranquility:
                        emojiTranquility.SetChecked();
                        break;
                    }
                }
            });


        }

        protected override void OnDisappearing()
        {
            base.OnDisappearing();

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                Super.KeepScreenOn = false;
            });
        }

        bool once;
        protected override void OnAppearing()
        {
            base.OnAppearing();

            PlatformUi.Instance.HideStatusBar();


            MainThread.BeginInvokeOnMainThread(async () =>
            {
                Super.KeepScreenOn = true;
            });

            if (!once)
            {
                once = true;
                MainThread.BeginInvokeOnMainThread(async () =>
                {

                    img.Source = App.GetFullImageUrl(Exercise.ImgPath, ThumbnailSize.Large, ThumbnailType.Jpg);
                    //img.Source = await ResorcesHelper.GetImageSource(Exercise.ImgPath);

                    descLabel.Text = Exercise.GetLocalizedDescription(LanguageHelper.LangCode);
                    desc2Label.Text = Exercise.GetLocalizedDescription2(LanguageHelper.LangCode);

                    var pause = 700; //on iOS this will be triggered BEFORE the page is shown, so we delay a bit
                    if (Device.RuntimePlatform == Device.Android)
                        pause = 10;
                    await Task.Delay(pause);

                    await ContentStack.FadeTo(1, 700);
                });
            }
        }


        private void SetRandomNextRecommendation()
        {
            nextItemLayout.Children.Clear();
            View card = null;

            var recommendation = ApplicationState.Data.GetRandomRecommendation();
            switch (recommendation.GetRecommendationType())
            {
                case RecomendationType.Test:
                card = new TestNextItemCard(recommendation.Test);
                break;
                case RecomendationType.Exercise:
                card = new ExerciseNextItemCard(recommendation.Exercise);
                break;
                case RecomendationType.Practice:
                card = new PracticeNextItemCard(recommendation.Practice);
                break;
                case RecomendationType.Topic:
                card = new TopicNextItemCard(recommendation.Topic);
                break;
            }

            if (card != null)
            {
                nextItemLayout.Children.Add(card);
            }
        }

        private void toggleFavorite(object? sender, bool b)
        {
            if (isFav != favoriteRb.IsToggled)
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    isFav = favoriteRb.IsToggled;
                    App.Messager.All("FavChanged", new FavChangedObject(Exercise.Id, isFav));
                    try
                    {
                        await ApplicationState.UserFavorites.Set(Exercise.Id, AppContentType.Exercise, isFav, exercise);
                    }
                    catch (Exception exception)
                    {
                        Console.WriteLine(exception);
                    }
                });

            }
        }


        private void onEmojiClicked(object sender, EventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var imgButton = sender as ImageButton;
                await imgButton.ScaleTo(1.15, 300);
                await imgButton.ScaleTo(1, 300);
            });
        }


        private Exercise exercise;
        public Exercise Exercise
        {
            get { return exercise; }
            set { exercise = value; OnPropertyChanged(nameof(Exercise)); }
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                App.ClosePage(this);
            });
        }

        private void onEmojiTapped(object sender, FeelingEmojiType e)
        {
            StatsHelper.AddExercisePassingResult(Exercise, e);
        }
    }
}