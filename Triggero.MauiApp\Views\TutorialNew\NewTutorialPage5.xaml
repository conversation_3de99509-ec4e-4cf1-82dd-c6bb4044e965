﻿<?xml version="1.0" encoding="utf-8"?>

<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage5"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <Grid VerticalOptions="Fill">

        <Image
            VerticalOptions="Fill"
            HorizontalOptions="Fill"
            Aspect="Fill"
            Source="tutorialblur3.png" />

        <ScrollView VerticalOptions="Fill">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="170" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="140" />
                </Grid.RowDefinitions>


                <Grid Grid.Row="0">


                </Grid>

                <Grid Grid.Row="1" Padding="10,0">

                    <StackLayout
                        Margin="0,-10,0,0"
                        Spacing="12">


                        <Grid
                            Margin="-3,0,0,0"
                            HorizontalOptions="Fill"
                            VerticalOptions="Start"
                            HeightRequest="269">
                            <Image
                                Aspect="Fill"
                                Source="tutorialcontainer1.png" />

                            <Grid
                                HeightRequest="210"
                                RowSpacing="12"
                                Margin="20,20,20,20">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="1*" />
                                    <RowDefinition Height="1*" />
                                </Grid.RowDefinitions>

                                <Grid Grid.Row="0"
                                      ColumnSpacing="12">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="1*" />
                                        <ColumnDefinition Width="1*" />
                                    </Grid.ColumnDefinitions>

                                    <Image Grid.Column="0"
                                           Aspect="Fill"
                                           Source="knowyourselfblured.png">

                                    </Image>

                                    <Image Grid.Column="1"
                                           Aspect="Fill"
                                           Source="breathblured.png">

                                    </Image>


                                </Grid>

                                <Grid Grid.Row="1">
                                    <Grid Padding="0">

                                        <Frame
                                            Opacity="0.4"
                                            CornerRadius="16"
                                            HasShadow="False">
                                            <Frame.Background>
                                                <LinearGradientBrush>
                                                    <LinearGradientBrush.GradientStops>
                                                        <GradientStop Color="#B3B3E6" Offset="0.1" />
                                                        <GradientStop Color="#F9ACC0" Offset="1.0" />
                                                    </LinearGradientBrush.GradientStops>
                                                </LinearGradientBrush>
                                            </Frame.Background>
                                        </Frame>

                                        <Grid>
                                            <Label
                                                TextColor="{x:StaticResource ColorText}"
                                                FontSize="14"
                                                FontAttributes="Bold"
                                                HorizontalOptions="Start"
                                                VerticalOptions="Start"
                                                Margin="12,16,0,0"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage5.Header}" />

                                            <Image
                                                Source="practices.png"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Start"
                                                HeightRequest="130"
                                                Margin="0,-5,0,-5"
                                                WidthRequest="119" />
                                        </Grid>
                                    </Grid>
                                </Grid>

                            </Grid>

                        </Grid>


                        <Label
                            Margin="0,60,0,0"
                            TextColor="#000000"
                            FontAttributes="Bold"
                            FontSize="19"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="317"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage5.Header}" />
                        <Label
                            Margin="0,0,0,0"
                            TextColor="{x:StaticResource ColorText}"
                            FontSize="16"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            WidthRequest="318"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage5.Description}" />


                    </StackLayout>

                </Grid>

                <Grid Grid.Row="2" Padding="10,0">
                    <Button
                        Command="{Binding Source={x:Reference this},Path=GoNext}"
                        VerticalOptions="Start"
                        HorizontalOptions="Fill"
                        Margin="63,0,63,0"
                        Style="{x:StaticResource yellow_btn}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage5.GoNext}" />

                </Grid>

            </Grid>
        </ScrollView>
    </Grid>
</ContentPage>