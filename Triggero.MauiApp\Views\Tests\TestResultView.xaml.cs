﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Tests;



namespace Triggero.MauiMobileApp.Controls.Tests
{

    public partial class TestResultView : ContentView
	{
		private TestResult _result;
        public TestResultView(TestResult result)
        {
            _result = result;
            InitializeComponent();

            titleLabel.Text = _result.GetLocalizedTitle(LanguageHelper.LangCode);
            textLabel.Text = _result.GetLocalizedText(LanguageHelper.LangCode);
        }


    }
}