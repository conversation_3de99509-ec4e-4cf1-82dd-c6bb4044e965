﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models;
using Triggero.Models.General;



namespace Triggero.Controls
{
    public partial class Avatar : ContentView
    {

        public static readonly BindableProperty AvatarWrapperProperty = BindableProperty.Create(nameof(AvatarWrapper), typeof(AvatarWrapper), typeof(Avatar));
        public AvatarWrapper AvatarWrapper
        {
            get { return (AvatarWrapper)GetValue(AvatarWrapperProperty); }
            set { SetValue(AvatarWrapperProperty, value); }
        }
        public Avatar(AvatarWrapper wrapper)
        {
            AvatarWrapper = wrapper;
            InitializeComponent();
        }
        public Avatar()
        {
            InitializeComponent();
        }


    }
}