﻿<?xml version="1.0" encoding="UTF-8"?>

<ContentView
    x:Class="Triggero.Controls.MoodTracker.TrackerFeelingsView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this">
    <ContentView.Resources>
        <ResourceDictionary>

            <LinearGradientBrush x:Key="mood_0">
                <LinearGradientBrush.GradientStops>
                    <GradientStop Offset="0" Color="#E8ECF1" />
                    <GradientStop Offset="1" Color="#C8CED3" />
                </LinearGradientBrush.GradientStops>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="mood_1">
                <LinearGradientBrush.GradientStops>
                    <GradientStop Offset="0" Color="#E8ECF1" />
                    <GradientStop Offset="1" Color="#C8CED3" />
                </LinearGradientBrush.GradientStops>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="mood_2">
                <LinearGradientBrush.GradientStops>
                    <GradientStop Offset="0" Color="#EAF3FA" />
                    <GradientStop Offset="1" Color="#D1E5F5" />
                </LinearGradientBrush.GradientStops>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="mood_3">
                <LinearGradientBrush.GradientStops>
                    <GradientStop Offset="0" Color="#EAF3FA" />
                    <GradientStop Offset="1" Color="#D1E5F5" />
                </LinearGradientBrush.GradientStops>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="mood_4">
                <LinearGradientBrush.GradientStops>
                    <GradientStop Offset="0" Color="#F8E9C9" />
                    <GradientStop Offset="1" Color="#FDD079" />
                </LinearGradientBrush.GradientStops>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="mood_5">
                <LinearGradientBrush.GradientStops>
                    <GradientStop Offset="0" Color="#F8E9C9" />
                    <GradientStop Offset="1" Color="#FDD079" />
                </LinearGradientBrush.GradientStops>
            </LinearGradientBrush>

        </ResourceDictionary>
    </ContentView.Resources>
    <ContentView.Content>

        <!--<Frame
          CornerRadius="15"
          HasShadow="False"
          BackgroundColor="White">-->

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="100" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <StackLayout Spacing="0">
                    <Label
                        FontAttributes="Bold"
                        FontSize="17"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.Happiness}"
                        TextColor="Black" />

                    <Frame
                        Margin="0,19,0,0"
                        Padding="0"
                        BackgroundColor="#F5F9FD"
                        CornerRadius="10"
                        HasShadow="False"
                        HeightRequest="22">
                        
                        <Grid>
                            
                            <Frame
                                x:Name="happinessProgress"
                                Padding="0"
                                CornerRadius="10"
                                HasShadow="False"
                                HorizontalOptions="Start"
                                WidthRequest="0">
                            </Frame>

                            <Slider
                                x:Name="happinessSlider"
                                Margin="{x:OnPlatform Android='0,-3,0,0', iOS='1,-2,1,0'}"
                                Maximum="5"
                                MaximumTrackColor="Transparent"
                                Minimum="0"
                                MinimumTrackColor="Transparent"
                                ThumbColor="White"
                                ValueChanged="happinessValueChanged"
                                Value="{Binding Source={x:Reference this}, Path=Tracker.Happiness, Mode=TwoWay}">

                                <!--<Slider.ThumbImageSource>
                                    <FontImageSource
                                        FontFamily="{x:OnPlatform iOS=Ionicons,
                                                                Android=ionicons.ttf#}"
                                        Glyph="&#9679;"
                                        Size="23"
                                        Color="White" />

                                </Slider.ThumbImageSource>-->

                            </Slider>

                        </Grid>
                    </Frame>


                    <Grid
                        x:Name="happinessGridScale"
                        Margin="2,8,4,0"
                        HeightRequest="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                        </Grid.ColumnDefinitions>

                        <Label
                            Grid.Column="0"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="0"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="1"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="1"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="2"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="2"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="3"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="3"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="4"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="4"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="4"
                            FontSize="14"
                            HorizontalOptions="End"
                            Text="5"
                            TextColor="{x:StaticResource ColorText}" />
                    </Grid>

                </StackLayout>
            </Grid>

            <Grid Grid.Row="1">
                <StackLayout Spacing="0">
                    <Label
                        FontAttributes="Bold"
                        FontSize="17"

                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.Inspiration}"
                        TextColor="Black" />

                    <Frame
                        Margin="0,19,0,0"
                        Padding="0"
                        BackgroundColor="#F5F9FD"
                        CornerRadius="10"
                        HasShadow="False"
                        HeightRequest="22">
                        <Grid>
                            <Frame
                                x:Name="insprirationProgress"
                                Padding="0"
                                CornerRadius="10"
                                HasShadow="False"
                                HorizontalOptions="Start"
                                WidthRequest="0">
                            </Frame>

                            <Slider
                                x:Name="InsprirationSlider"
                                Margin="{x:OnPlatform Android='0,-3,0,0',
                                                    iOS='1,-2,1,0'}"
                                Maximum="5"
                                MaximumTrackColor="Transparent"
                                Minimum="0"
                                MinimumTrackColor="Transparent"
                                ThumbColor="White"

                                ValueChanged="InsprirationValueChanged"
                                Value="{Binding Source={x:Reference this}, Path=Tracker.Inspiration, Mode=TwoWay}">
                                <!--<Slider.ThumbImageSource>
                                    <FontImageSource
                                        FontFamily="{x:OnPlatform iOS=Ionicons,
                                                                Android=ionicons.ttf#}"
                                        Glyph="&#9679;"
                                        Size="{x:OnPlatform Android=44,
                                                          iOS=24}"
                                        Color="White" />
                                </Slider.ThumbImageSource>-->
                            </Slider>

                        </Grid>
                    </Frame>


                    <Grid
                        x:Name="inspirationGridScale"
                        Margin="2,8,4,0"
                        HeightRequest="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                        </Grid.ColumnDefinitions>

                        <Label
                            Grid.Column="0"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="0"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="1"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="1"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="2"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="2"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="3"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="3"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="4"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="4"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="4"
                            FontSize="14"
                            HorizontalOptions="End"
                            Text="5"
                            TextColor="{x:StaticResource ColorText}" />
                    </Grid>

                </StackLayout>
            </Grid>

            <Grid Grid.Row="2">
                <StackLayout Spacing="0">
                    <Label
                        FontAttributes="Bold"
                        FontSize="17"

                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.Confidence}"
                        TextColor="Black" />

                    <Frame
                        Margin="0,19,0,0"
                        Padding="0"
                        BackgroundColor="#F5F9FD"
                        CornerRadius="10"
                        HasShadow="False"
                        HeightRequest="22">
                        <Grid>
                            <Frame
                                x:Name="confidenceProgress"
                                Padding="0"
                                CornerRadius="10"
                                HasShadow="False"
                                HorizontalOptions="Start"
                                WidthRequest="0">
                            </Frame>


                            <Slider
                                x:Name="confidenceSlider"
                                Margin="{x:OnPlatform Android='0,-3,0,0',
                                                    iOS='1,-2,1,0'}"
                                Maximum="5"
                                MaximumTrackColor="Transparent"
                                Minimum="0"
                                MinimumTrackColor="Transparent"
                                ThumbColor="White"

                                ValueChanged="confidenceValueChanged"
                                Value="{Binding Source={x:Reference this}, Path=Tracker.Confidence, Mode=TwoWay}">
                                <!--<Slider.ThumbImageSource>
                                    <FontImageSource
                                        FontFamily="{x:OnPlatform iOS=Ionicons,
                                                                Android=ionicons.ttf#}"
                                        Glyph="&#9679;"
                                        Size="{x:OnPlatform Android=44,
                                                          iOS=24}"
                                        Color="White" />
                                </Slider.ThumbImageSource>-->
                            </Slider>


                        </Grid>
                    </Frame>


                    <Grid
                        x:Name="confidenceGridScale"
                        Margin="2,8,4,0"
                        HeightRequest="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                        </Grid.ColumnDefinitions>

                        <Label
                            Grid.Column="0"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="0"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="1"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="1"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="2"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="2"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="3"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="3"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="4"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="4"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="4"
                            FontSize="14"
                            HorizontalOptions="End"
                            Text="5"
                            TextColor="{x:StaticResource ColorText}" />
                    </Grid>

                </StackLayout>
            </Grid>

            <Grid Grid.Row="3">
                <StackLayout Spacing="0">
                    <Label
                        FontAttributes="Bold"
                        FontSize="17"

                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.Stress}"
                        TextColor="Black" />

                    <Frame
                        Margin="0,19,0,0"
                        Padding="0"
                        BackgroundColor="#F5F9FD"
                        CornerRadius="10"
                        HasShadow="False"
                        HeightRequest="22">
                        <Grid>
                            <Frame
                                x:Name="stressProgress"
                                Padding="0"
                                CornerRadius="10"
                                HasShadow="False"
                                HorizontalOptions="Start"
                                WidthRequest="0">
                            </Frame>

                            <Slider
                                x:Name="stressSlider"
                                Margin="{x:OnPlatform Android='0,-3,0,0',
                                                    iOS='1,-2,1,0'}"
                                Maximum="5"
                                MaximumTrackColor="Transparent"
                                Minimum="0"
                                MinimumTrackColor="Transparent"
                                ThumbColor="White"

                                ValueChanged="stressValueChanged"
                                Value="{Binding Source={x:Reference this}, Path=Tracker.Stress, Mode=TwoWay}">
                                <!--<Slider.ThumbImageSource>
                                    <FontImageSource
                                        FontFamily="{x:OnPlatform iOS=Ionicons,
                                                                Android=ionicons.ttf#}"
                                        Glyph="&#9679;"
                                        Size="{x:OnPlatform Android=44,
                                                          iOS=24}"
                                        Color="White" />
                                </Slider.ThumbImageSource>-->
                            </Slider>

                        </Grid>
                    </Frame>


                    <Grid
                        x:Name="stressGridScale"
                        Margin="2,8,4,0"
                        HeightRequest="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                        </Grid.ColumnDefinitions>

                        <Label
                            Grid.Column="0"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="0"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="1"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="1"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="2"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="2"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="3"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="3"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="4"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="4"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="4"
                            FontSize="14"
                            HorizontalOptions="End"
                            Text="5"
                            TextColor="{x:StaticResource ColorText}" />
                    </Grid>

                </StackLayout>
            </Grid>

            <Grid Grid.Row="4">
                <StackLayout Spacing="0">
                    <Label
                        FontAttributes="Bold"
                        FontSize="17"

                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.Anxienty}"
                        TextColor="Black" />

                    <Frame
                        x:Name="anxientyFrame"
                        Margin="0,19,0,0"
                        Padding="0"
                        BackgroundColor="#F5F9FD"
                        CornerRadius="10"
                        HasShadow="False"
                        HeightRequest="22">
                        <Grid>
                            <Frame
                                x:Name="anxientyProgress"
                                Padding="0"
                                CornerRadius="10"
                                HasShadow="False"
                                HorizontalOptions="Start"
                                WidthRequest="0">
                            </Frame>

                            <Slider
                                x:Name="anxientySlider"
                                Margin="{x:OnPlatform Android='0,-3,0,0',
                                                    iOS='1,-2,1,0'}"
                                Maximum="5"
                                MaximumTrackColor="Transparent"
                                Minimum="0"
                                MinimumTrackColor="Transparent"
                                ThumbColor="White"
                                ValueChanged="anxientyValueChanged"
                                Value="{Binding Source={x:Reference this}, Path=Tracker.Anxienty, Mode=TwoWay}">
                                <!--<Slider.ThumbImageSource>
                                    <FontImageSource
                                        FontFamily="{x:OnPlatform iOS=Ionicons,
                                                                Android=ionicons.ttf#}"
                                        Glyph="&#9679;"
                                        Size="{x:OnPlatform Android=44,
                                                          iOS=24}"
                                        Color="White" />
                                </Slider.ThumbImageSource>-->
                            </Slider>


                        </Grid>
                    </Frame>


                    <Grid
                        x:Name="anxientyGridScale"
                        Margin="2,8,4,0"
                        HeightRequest="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                        </Grid.ColumnDefinitions>

                        <Label
                            Grid.Column="0"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="0"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="1"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="1"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="2"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="2"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="3"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="3"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="4"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="4"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="4"
                            FontSize="14"
                            HorizontalOptions="End"
                            Text="5"
                            TextColor="{x:StaticResource ColorText}" />
                    </Grid>

                </StackLayout>
            </Grid>

            <Grid Grid.Row="5">
                <StackLayout Spacing="0">
                    <Label
                        FontAttributes="Bold"
                        FontSize="17"

                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.Annoyance}"
                        TextColor="Black" />

                    <Frame
                        Margin="0,19,0,0"
                        Padding="0"
                        BackgroundColor="#F5F9FD"
                        CornerRadius="10"
                        HasShadow="False"
                        HeightRequest="22">
                        <Grid>
                            
                            <Frame
                                x:Name="annoyanceProgress"
                                Padding="0"
                                CornerRadius="10"
                                HasShadow="False"
                                HorizontalOptions="Start"
                                WidthRequest="0">
                            </Frame>


                            <Slider
                                x:Name="annoyanceSlider"
                                Margin="{x:OnPlatform Android='0,-3,0,0',
                                                    iOS='1,-2,1,0'}"
                                Maximum="5"
                                MaximumTrackColor="Transparent"
                                Minimum="0"
                                MinimumTrackColor="Transparent"
                                ThumbColor="White"
                                ValueChanged="annoyanceValueChanged"
                                Value="{Binding Source={x:Reference this}, Path=Tracker.Annoyance, Mode=TwoWay}">
                                <!--<Slider.ThumbImageSource>
                                    <FontImageSource
                                        FontFamily="{x:OnPlatform iOS=Ionicons,
                                                                Android=ionicons.ttf#}"
                                        Glyph="&#9679;"
                                        Size="{x:OnPlatform Android=44,
                                                          iOS=24}"
                                        Color="White" />
                                </Slider.ThumbImageSource>-->
                            </Slider>

                        </Grid>
                    </Frame>


                    <Grid
                        x:Name="annoyanceGridScale"
                        Margin="2,8,4,0"
                        HeightRequest="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                        </Grid.ColumnDefinitions>

                        <Label
                            Grid.Column="0"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="0"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="1"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="1"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="2"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="2"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="3"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="3"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="4"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="4"
                            TextColor="{x:StaticResource ColorText}" />
                        <Label
                            Grid.Column="4"
                            FontSize="14"
                            HorizontalOptions="End"
                            Text="5"
                            TextColor="{x:StaticResource ColorText}" />
                    </Grid>

                </StackLayout>
            </Grid>

        </Grid>

        <!--</Frame>-->
    </ContentView.Content>
</ContentView>