﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models;
using Triggero.Models.ChatBot;



namespace Triggero.Controls.ChatBot
{

    public partial class ChatBotTextBotWithButton : ContentView
    {
        private ChatOptionButton _chatOptionButton;
        public ChatBotTextBotWithButton(ChatOptionButton btn)
        {
            _chatOptionButton = btn;
            InitializeComponent();
        }


        public event EventHandler<ChatBotEnterTextEventArgs> Clicked;
        private void onClick(object sender, EventArgs e)
        {
            Clicked?.Invoke(this, new ChatBotEnterTextEventArgs
            {
                Button = _chatOptionButton,
                Text = textEdit.Text
            });
        }

        public event EventHandler Focused;
        private void onFocused(object sender, FocusEventArgs e)
        {
            Focused?.Invoke(this, e);
        }
        public event EventHandler Unfocused;
        private void onUnfocused(object sender, FocusEventArgs e)
        {
            Unfocused?.Invoke(this, e);
        }
    }
}